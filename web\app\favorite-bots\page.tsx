import { AuthGuard } from "@/components/auth-guard"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Filter } from "lucide-react"

const filterCategories = [
  { name: "FemalePOV", count: 1 },
  { name: "Male", count: 1 },
  { name: "Romantic", count: 1 },
]

export default function FavoriteBotsPage() {
  return (
    <AuthGuard requireAuth={true}>
      <div className="bg-black min-h-screen text-white">
        <div className="flex h-full">
          {/* Left Sidebar */}
          <div className="w-80 border-r bg-background p-4">
            <div className="mb-4">
              <h3 className="font-medium mb-2 text-white">Narrow by tag</h3>
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search tags" className="pl-9 bg-gray-800 border-gray-700 text-white" />
              </div>
            </div>

            <div className="space-y-1">
              {filterCategories.map((category) => (
                <div
                  key={category.name}
                  className="flex items-center justify-between py-1 px-2 hover:bg-accent rounded text-sm"
                >
                  <span className="text-white">{category.name}</span>
                  <span className="text-muted-foreground">{category.count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6">
            <div className="mb-6">
              <h1 className="text-2xl font-bold mb-4">Favorite Bots</h1>

              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-white hover:bg-gray-800 bg-transparent"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Narrow by tag
                </Button>

                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Dive into endless fantasies - start searching!"
                    className="pl-9 bg-gray-800 border-gray-700 text-white"
                  />
                </div>

                <div className="flex items-center gap-4">
                  <span className="text-sm text-white">NSFW</span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-gray-600 text-white hover:bg-gray-800 bg-transparent"
                  >
                    Trending
                  </Button>
                </div>
              </div>

              <div className="text-sm text-muted-foreground mb-6">1 result found in 36ms</div>
            </div>

            {/* Single Bot Card */}
            <div className="w-48">
              <Card className="bg-gray-900 border-gray-700 overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative">
                  <img
                    src="/placeholder.svg?height=200&width=150"
                    alt="ghost-ring"
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 right-2">
                    <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                      <span className="text-white">♡</span>
                    </Button>
                  </div>
                </div>
                <CardContent className="p-3">
                  <h3 className="font-medium text-sm mb-1 text-white">ghost-ring</h3>
                  <p className="text-xs text-gray-400 mb-2">🔥 you forgot your fiancé</p>
                  <div className="flex flex-wrap gap-1 mb-2">
                    <Badge variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                      Male
                    </Badge>
                    <Badge variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                      FemalePOV
                    </Badge>
                    <Badge variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                      Romantic
                    </Badge>
                  </div>
                  <div className="text-xs text-gray-400">52.0k 63% 1410</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
