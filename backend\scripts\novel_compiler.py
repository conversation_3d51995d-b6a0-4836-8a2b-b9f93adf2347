#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
织梦者引擎 - 小说游戏化编译器
将文本小说转化为包含情感交互和分支选择的互动游戏

使用方法:
1. 自动模式 (推荐):
   python novel_compiler.py
   - 自动查找novel目录中的.txt文件
   - 自动创建或使用测试用户

2. 手动模式:
   python novel_compiler.py --file "path/to/novel.txt" --user-id "user-uuid"
"""

import asyncio
import argparse
import base64
import json
import re
import uuid
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

# 导入项目依赖
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services import llm_service, supabase_service, simple_imagekit_service
from src.utils.user_management import create_user_if_not_exists
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv(Path(__file__).parent.parent / '.env')

# 测试用户配置
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)

# --- 用户管理功能 ---
# 现在使用统一的用户管理模块

def find_novel_files() -> List[Path]:
    """在novel目录中查找.txt文件"""
    # 尝试多个可能的novel目录位置
    possible_paths = [
        Path("novel"),  # 当前目录下的novel
        Path("../novel"),  # 上级目录下的novel
        Path("../../novel"),  # 上上级目录下的novel
    ]

    novel_dir = None
    for path in possible_paths:
        if path.exists():
            novel_dir = path
            break

    if not novel_dir:
        print(f"ERROR: 在以下位置都没有找到novel目录:")
        for path in possible_paths:
            print(f"  - {path.absolute()}")
        return []

    txt_files = list(novel_dir.glob("*.txt"))
    if not txt_files:
        print(f"ERROR: novel目录中没有找到.txt文件: {novel_dir.absolute()}")
        return []

    print(f"INFO: 在novel目录中找到 {len(txt_files)} 个小说文件: {novel_dir.absolute()}")
    for file in txt_files:
        print(f"  - {file.name}")

    return txt_files

class NovelCompiler:
    """小说游戏化编译器核心类"""
    
    def __init__(self, novel_file_path: Path, user_id: str):
        self.novel_file_path = novel_file_path
        self.user_id = user_id
        self.novel_text = ""
        self.gdd = {}
        self.llm_service = llm_service
        
    def _load_novel_text(self):
        """加载小说文本"""
        print(f"INFO: 正在加载小说文件: {self.novel_file_path}")
        try:
            with open(self.novel_file_path, 'r', encoding='utf-8') as f:
                self.novel_text = f.read()
            print(f"SUCCESS: 小说加载成功，总字数: {len(self.novel_text)}")
        except Exception as e:
            print(f"ERROR: 小说文件加载失败: {e}")
            raise
    
    def _chunkify_novel_by_plot(self) -> List[str]:
        """根据章节标题切分小说文本
        
        这是一个简化实现，通过章节标题切分小说文本。
        生产环境中可能需要更智能的语义分块算法。
        """
        print("INFO: 正在按章节切分小说文本...")
        chunks = []
        plot_structure = self.gdd.get('chapters', [])
        
        if not plot_structure:
            # 如果没有章节结构，直接返回整个文本
            return [self.novel_text]
        
        text = self.novel_text
        for i in range(len(plot_structure)):
            chapter_title = plot_structure[i]['title']
            start_pos = text.find(chapter_title)
            
            if start_pos == -1:
                # 如果找不到章节标题，使用平均分割
                chunk_size = len(text) // len(plot_structure)
                start_pos = i * chunk_size
                end_pos = (i + 1) * chunk_size if i + 1 < len(plot_structure) else len(text)
                chunks.append(text[start_pos:end_pos])
            else:
                # 找到下一个章节的开始位置
                if i + 1 < len(plot_structure):
                    next_title = plot_structure[i + 1]['title']
                    end_pos = text.find(next_title, start_pos + len(chapter_title))
                    if end_pos == -1:
                        end_pos = len(text)
                else:
                    end_pos = len(text)
                
                chunks.append(text[start_pos:end_pos])
        
        print(f"SUCCESS: 小说已切分为 {len(chunks)} 个文本块")
        return chunks
    
    async def _create_story_entry(self, protagonist_agent_id: Optional[str] = None) -> Optional[str]:
        """创建故事条目"""
        print("INFO: 正在创建故事数据库条目...")
        try:
            story_title = self.gdd.get('title', '未命名故事')
            theme_summary = self.gdd.get('theme_summary', '')
            worldview = self.gdd.get('worldview', '')

            # 生成故事封面图
            # 必须在这里导入ImageGenerationRequest模型
            from src.pydantic_models import ImageGenerationRequest
            import json

            print("  - 正在为故事生成封面图...")
            cover_image_url = None
            cover_image_prompt = None
            try:
                # 1. 构建一个增强的图片生成请求
                image_request = ImageGenerationRequest(
                    description=f"为小说《{story_title}》的封面设计一张引人入胜、充满情感张力的主视觉图。",
                    image_type="cover",  # 指定图片类型为封面
                    additional_context=json.dumps({"story_synopsis": theme_summary})
                )
                # 2. 调用新的统一图片生成接口
                result = await self.llm_service.generate_enhanced_image_from_request(image_request)

                if result:
                    cover_image_bytes, cover_image_prompt = result
                else:
                    cover_image_bytes = None

                # 2. 如果成功生成，则上传到ImageKit
                if cover_image_bytes:
                    print("  - 正在上传封面图...")
                    upload_result = await simple_imagekit_service.upload_image_from_base64(
                        base64.b64encode(cover_image_bytes).decode('utf-8'),
                        f"cover_{story_title.replace(' ', '_')}.png",
                        "/xinglian/story/"
                    )
                    if upload_result["success"]:
                        cover_image_url = upload_result.get("url")
                        print(f"    ✓ 封面图上传成功: {cover_image_url}")
                    else:
                        print(f"    ✗ 封面图上传失败: {upload_result.get('error')}")
                else:
                    print("    ✗ LLM未能生成封面图。")
            except Exception as img_e:
                print(f"    ✗ 生成或上传封面图时发生异常: {img_e}")

            story_data = {
                'user_id': self.user_id,
                'title': story_title,
                'theme_prompt': theme_summary,
                'worldview_text': worldview,
                'source_analysis': self.gdd,  # 存储完整的GDD
                'protagonist_agent_id': protagonist_agent_id,  # 关联主角ID
                'is_public': True,
                'cover_image_url': cover_image_url,  # 使用新生成的URL
                'cover_image_prompt': cover_image_prompt  # 保存生成封面图的prompt
            }

            story = await supabase_service.create_story(**story_data)
            if story:
                story_id = story['id']
                print(f"SUCCESS: 故事条目创建成功，ID: {story_id}")
                if protagonist_agent_id:
                    print(f"SUCCESS: 已关联主角 Agent ID: {protagonist_agent_id}")
                return story_id
            else:
                print("ERROR: 故事条目创建失败")
                return None

        except Exception as e:
            print(f"ERROR: 创建故事条目时发生错误: {e}")
            return None

    async def _process_single_character(self, character: Dict[str, Any], semaphore: asyncio.Semaphore) -> Optional[Tuple[str, str, bool]]:
        """并发处理单个角色的创建"""
        async with semaphore:
            character_name = character.get('name', '未知角色')
            is_protagonist = character.get('is_protagonist', False)
            theme_summary = self.gdd.get('theme_summary', '')

            print(f"  [+] 开始为角色 '{character_name}' 生成档案... {'[主角]' if is_protagonist else ''}")

            try:
                # 使用新的LLM方法生成角色数据
                agent_data, image_bytes, image_prompt = await self.llm_service.generate_agent_from_analysis(
                    character_gdd=character,         # 传递角色GDD本身
                    story_theme_summary=theme_summary # 明确传递主题摘要
                )

                # 上传图片
                image_url = None
                if image_bytes:
                    image_url = await simple_imagekit_service.upload_image(
                        image_bytes, f"novel_character_{character_name}.png", folder="/xinglian/agent/"
                    )

                if image_url:
                    print(f"    ✓ 角色 '{character_name}' 图片上传成功")
                else:
                    print(f"    ⚠ 角色 '{character_name}' 图片处理失败，将使用默认图片")

                # 创建智能体
                new_agent = await supabase_service.create_agent(
                    user_id=self.user_id,
                    name=agent_data.name,
                    description=agent_data.description,
                    tags=agent_data.tags,
                    image_url=image_url,
                    avatar_url=image_url,
                    gender=agent_data.gender,
                    voice_name=agent_data.voice_name,
                    personality=agent_data.personality,
                    scenario=agent_data.scenario,
                    first_mes=agent_data.first_mes,
                    mes_example=agent_data.mes_example,
                    system_prompt=agent_data.system_prompt,
                    creator_notes=agent_data.creator_notes,
                    image_generation_prompt=image_prompt,
                    is_public=True,
                    is_system_agent=False,
                    data=agent_data.model_dump()
                )

                if new_agent:
                    agent_id = new_agent['id']
                    if not is_protagonist:
                        initial_relationship_description = await self.llm_service.generate_initial_relationship_prompt(
                            character_name,
                            character.get('key_variables', [])
                        )
                        await supabase_service.create_agent_mode_config(
                            agent_id=agent_id,
                            mode='story',
                            mode_specific_instructions=initial_relationship_description,
                            enable_mes_example=False
                        )
                    print(f"    ✓ 角色 '{character_name}' 创建成功，ID: {agent_id}")
                    return (character_name, agent_id, is_protagonist)
                else:
                    raise Exception("Supabase create_agent failed")

            except Exception as e:
                print(f"    ✗ 角色 '{character_name}' 创建失败: {e}")
                traceback.print_exc()
                return None

    async def _create_character_agents(self) -> tuple[Dict[str, str], Optional[str]]:
        """根据GDD并发创建角色智能体，返回角色映射和主角ID"""
        print("INFO: 正在并发创建角色智能体...")
        agent_map = {}
        protagonist_agent_id = None
        characters = self.gdd.get('characters', [])

        # 使用信号量控制并发数量，防止API过载
        semaphore = asyncio.Semaphore(5)
        tasks = [self._process_single_character(char, semaphore) for char in characters]

        results = await asyncio.gather(*tasks)

        for result in results:
            if result:
                char_name, agent_id, is_protagonist = result
                agent_map[char_name] = agent_id
                if is_protagonist:
                    protagonist_agent_id = agent_id

        print(f"SUCCESS: 角色创建完成，成功创建 {len(agent_map)} / {len(characters)} 个角色")
        if protagonist_agent_id:
            print(f"SUCCESS: 识别到主角，Agent ID: {protagonist_agent_id}")
        else:
            print("WARN: 未识别到主角角色")

        return agent_map, protagonist_agent_id
    
    async def _create_story_chapter(self, story_id: str, chapter_gdd: dict, novel_chunk: str, agent_map: dict, previous_chapter_gdd: Optional[dict] = None):
        """创建故事章节"""
        chapter_title = chapter_gdd.get('title', '未知章节')
        chapter_number = chapter_gdd.get('chapter_number', 1)
        chapter_summary = chapter_gdd.get('summary', '')
        print(f"  - 正在为章节 '{chapter_title}' 生成互动场景...")

        try:
            # 别忘了导入模型
            from src.pydantic_models import ImageGenerationRequest
            import json

            # 禁用章节背景图生成以减少token消耗
            background_image_url = None
            print("    - [已禁用] 跳过章节背景图生成。")

            # 生成互动场景序列（传递更丰富的角色信息）
            # 找出本章涉及的角色完整信息
            involved_character_names = []
            for interaction in chapter_gdd.get('key_interactions', []):
                if "与" in interaction:
                    involved_character_names.append(interaction.split("与")[1])

            involved_characters_profiles = [
                char for char in self.gdd.get('characters', [])
                if char.get('name') in involved_character_names
            ]

            # 构建初始游戏状态（用于第一次生成）
            initial_game_state = {}
            for char_name in involved_character_names:
                if char_name:
                    initial_game_state[f"{char_name}.好感度"] = 50
                    initial_game_state[f"{char_name}.误解程度"] = 30

            # 构建上一章的上下文摘要
            previous_chapter_summary = "这是故事的第一章。"
            if previous_chapter_gdd:
                prev_title = previous_chapter_gdd.get('title', '')
                prev_summary = previous_chapter_gdd.get('summary', '')
                prev_interactions = ", ".join(previous_chapter_gdd.get('key_interactions', []))
                previous_chapter_summary = f"上一章 '{prev_title}' 的结尾摘要：{prev_summary} 关键互动包括：{prev_interactions}。"

            # 【解决方案】不再基于GDD摘要预先筛选角色，将完整的角色列表交给AI，
            # 让其根据更可靠的小说原文来判断谁应该出场。
            print(f"    - [INFO] 将全部 {len(agent_map)} 个角色信息提供给AI进行场景生成。")

            # ▼▼▼【修复 #3 开始 + 索引翻译逻辑】▼▼▼
            # 1. 创建索引映射（用索引替代UUID以提高LLM准确性）
            involved_agents = list(agent_map.keys())
            agent_id_list = [agent_map[name] for name in involved_agents]
            agent_index_map = {name: i for i, name in enumerate(involved_agents)}

            print(f"    - [INFO] 创建角色索引映射: {agent_index_map}")

            # 2. 调用优化后的方法，传递索引映射而不是UUID映射
            scene_data = await self.llm_service.generate_interactive_scene(
                gdd=self.gdd,  # 完整的GDD
                chapter_gdd=chapter_gdd,  # 当前章节的GDD
                novel_chunk=novel_chunk,  # 当前章节对应的原文
                agent_map=agent_index_map,  # <-- 使用索引映射而不是UUID映射
                character_profiles=involved_characters_profiles,  # 新增：传入角色完整档案
                current_game_state=initial_game_state,  # 新增：传入游戏状态
                previous_chapter_summary=previous_chapter_summary  # 新增：传递上一章摘要
            )

            # 3. 从返回的字典中提取数据并进行索引翻译
            raw_interactive_sequence = scene_data.get('interactive_sequence', [])
            completion_summary = scene_data.get('completion_summary', f'关于《{chapter_title}》的故事还在继续...')  # 提供一个后备总结

            # 4. 将LLM返回的索引翻译回UUID
            interactive_sequence = []
            for element in raw_interactive_sequence:
                processed_element = element.copy()

                # 处理对话元素的agent_index
                if 'agent_index' in processed_element and processed_element['agent_index'] is not None:
                    index = processed_element.pop('agent_index')  # 取出并移除index
                    if 0 <= index < len(agent_id_list):
                        processed_element['agent_id'] = agent_id_list[index]  # 添加正确的UUID
                        print(f"    - [翻译] 索引 {index} -> UUID {agent_id_list[index][:8]}...")

                # 处理选择元素中的target_agent_index
                if 'choices' in processed_element and processed_element['choices']:
                    processed_choices = []
                    for choice in processed_element['choices']:
                        processed_choice = choice.copy()
                        if 'target_agent_index' in processed_choice and processed_choice['target_agent_index'] is not None:
                            index = processed_choice.pop('target_agent_index')  # 取出并移除index
                            if 0 <= index < len(agent_id_list):
                                processed_choice['target_agent_id'] = agent_id_list[index]  # 添加正确的UUID
                                print(f"    - [翻译] 选择目标索引 {index} -> UUID {agent_id_list[index][:8]}...")
                        processed_choices.append(processed_choice)
                    processed_element['choices'] = processed_choices

                interactive_sequence.append(processed_element)

            # 3. 删除原来单独生成 completion_summary 的代码块
            # (原有的 llm_service.generate_completion_summary 调用可以被安全删除)
            # ▲▲▲【修复 #3 结束】▲▲▲

            # ▼▼▼【新增逻辑】▼▼▼
            # 基于章节GDD的关键互动点，生成供AI阅读的剧本摘要
            print(f"    - 正在为章节 '{chapter_title}' 生成AI剧本摘要...")
            chapter_event_summary = await self.llm_service.generate_chapter_summary_for_npc(
                chapter_gdd.get('summary', ''),
                chapter_gdd.get('key_interactions', [])
            )
            # ▲▲▲【新增逻辑】▲▲▲

            # 处理互动序列，过滤掉图片节点以减少token消耗
            print("    - 正在处理互动序列 (已禁用情节图生成)...")
            processed_sequence = []
            for element in interactive_sequence:
                # 跳过图片类型的节点
                if element.get("element_type") == "image":
                    print(f"      - 发现并跳过一个 'image' 类型的情节图节点。")
                    continue
                processed_sequence.append(element)

            # 创建章节记录 (这里的逻辑保持不变，因为我们已经提取了所需变量)
            chapter_data = {
                'story_id': story_id,
                'chapter_number': chapter_number,
                'title': chapter_title,
                'mission_objective_text': chapter_gdd.get('mission_objective_text', ''),
                'background_text': chapter_summary,
                'clear_condition_text': chapter_gdd.get('clear_condition_text', f"完成本章的情感目标：{chapter_gdd.get('emotional_goal', '')}"),
                'opening_sequence': processed_sequence,  # 使用处理过的序列
                'chapter_event_summary': chapter_event_summary, # <-- 存入新字段
                'completion_summary_text': completion_summary, # <-- 使用新获取的总结
                'background_image_url': background_image_url  # 使用新生成的URL
            }

            chapter = await supabase_service.create_story_chapter(**chapter_data)
            if chapter:
                print(f"    ✓ 章节 '{chapter_title}' 创建成功")
            else:
                print(f"    ✗ 章节 '{chapter_title}' 创建失败")

        except Exception as e:
            print(f"    ✗ 章节 '{chapter_title}' 创建失败: {e}")
    
    async def run(self):
        """运行编译流程"""
        print("=" * 60)
        print("🌟 织梦者引擎 - 小说游戏化编译器启动")
        print("=" * 60)
        
        try:
            # 1. 加载小说文本
            self._load_novel_text()

            # 2. 【新流程】提炼小说摘要（获取详细分块分析）
            print("\n[1/6] 正在分析小说并提炼核心摘要...")
            # distill_story_summary 在文本过长时会返回分块分析的合并文本
            # 我们直接使用这个更详细的版本来生成GDD
            detailed_summary_for_gdd = await self.llm_service.distill_story_summary(self.novel_text)

            # 3. 【新流程】基于摘要生成游戏设计文档
            print("\n[2/6] 正在基于摘要生成游戏设计文档(GDD)...")
            self.gdd = await self.llm_service.generate_game_design_document(detailed_summary_for_gdd)



            # 4. 创建角色智能体（先创建角色以获取主角ID）
            print("\n[3/6] 正在创建角色智能体...")
            agent_map, protagonist_agent_id = await self._create_character_agents()

            # 5. 创建故事条目（使用主角ID）
            print("\n[4/6] 正在创建故事数据库条目...")
            story_id = await self._create_story_entry(protagonist_agent_id)
            if not story_id:
                print("ERROR: 故事创建失败，编译终止")
                return

            # 6. 建立故事与角色的关联
            print("\n[5/6] 正在建立故事与角色的关联...")
            if story_id and agent_map:
                story_agent_relations = [
                    {"story_id": story_id, "agent_id": agent_id}
                    for agent_id in agent_map.values()
                ]
                try:
                    await asyncio.to_thread(
                        lambda: supabase_service.supabase.table("story_agents").insert(story_agent_relations).execute()
                    )
                    print(f"SUCCESS: 成功关联了 {len(story_agent_relations)} 个角色到故事 {story_id}")
                except Exception as link_e:
                    print(f"ERROR: 关联故事与角色失败: {link_e}")

            # 7. 按章节处理并创建互动场景（并发模式）
            print("\n[6/6] 正在生成章节互动场景（并发模式，最多10个并发）...")
            plot_chunks = self._chunkify_novel_by_plot()
            plot_structure = self.gdd.get('chapters', [])

            # 创建信号量控制并发数量
            semaphore = asyncio.Semaphore(10)  # 最多允许10个并发任务

            async def process_chapter_with_semaphore(i, chapter_gdd, novel_chunk, previous_gdd):
                async with semaphore:
                    # 确保任务启动间隔至少1秒
                    await asyncio.sleep(1)
                    print(f"   [+] 开始处理章节 {i+1}/{len(plot_structure)}: {chapter_gdd.get('title', '未知章节')}")
                    await self._create_story_chapter(story_id, chapter_gdd, novel_chunk, agent_map, previous_chapter_gdd=previous_gdd)
                    print(f"   [✓] 完成章节 {i+1}: {chapter_gdd.get('title', '未知章节')}")

            # 创建所有章节处理任务
            tasks = []
            for i, chapter_gdd in enumerate(plot_structure):
                novel_chunk = plot_chunks[i] if i < len(plot_chunks) else ""
                # 获取上一章的GDD
                previous_gdd = plot_structure[i-1] if i > 0 else None
                tasks.append(process_chapter_with_semaphore(i, chapter_gdd, novel_chunk, previous_gdd))

            # 并发执行所有章节处理任务
            print(f"   [+] 启动 {len(tasks)} 个章节处理任务...")
            await asyncio.gather(*tasks)
            print(f"   [✓] 所有章节处理完成！")

            print("\n[6/6] 编译完成！")
            print("=" * 60)
            print(f"✅ 小说 '{self.gdd.get('title')}' 已成功编译为互动游戏！")
            print(f"📖 故事ID: {story_id}")
            print(f"👥 角色数量: {len(agent_map)}")
            print(f"📚 章节数量: {len(plot_structure)}")
            if protagonist_agent_id:
                print(f"👑 主角Agent ID: {protagonist_agent_id}")
            print("=" * 60)
            
        except Exception as e:
            print(f"\n❌ 编译过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description="星恋AI - 小说游戏化编译引擎")
    parser.add_argument("--file", help="小说源文件路径 (.txt) [可选，默认自动查找novel目录]")
    parser.add_argument("--user-id", help="创作者的用户ID (UUID) [可选，默认使用测试用户]")

    args = parser.parse_args()

    print("=" * 60)
    print("🌟 织梦者引擎 - 小说游戏化编译器启动")
    print("=" * 60)

    # 处理用户ID
    user_id = args.user_id
    if not user_id:
        print("\n--- [STEP 1/3] 确保测试用户存在 ---")
        user_id = await create_user_if_not_exists(
            supabase_admin_client,
            TEST_USER_EMAIL,
            TEST_USER_PASSWORD,
            display_name="测试用户"
        )

        if not user_id:
            print("\n❌ [FATAL] 无法创建或获取测试用户。脚本终止。")
            return

        print(f"\n✅ 测试用户已就绪!")
        print(f"Email:    {TEST_USER_EMAIL}")
        print(f"Password: {TEST_USER_PASSWORD}")
        print(f"User ID:  {user_id}")
    else:
        print(f"\n--- 使用指定的用户ID: {user_id} ---")

    # 处理小说文件
    novel_file = None
    if args.file:
        novel_file = Path(args.file)
        if not novel_file.exists():
            print(f"ERROR: 指定的小说文件不存在: {novel_file}")
            return
        print(f"\n--- 使用指定的小说文件: {novel_file} ---")
    else:
        print("\n--- [STEP 2/3] 自动查找小说文件 ---")
        novel_files = find_novel_files()
        if not novel_files:
            print("\n❌ [FATAL] 没有找到可用的小说文件。请在novel目录中放置.txt文件。")
            return

        # 使用第一个找到的文件
        novel_file = novel_files[0]
        print(f"\n✅ 将使用小说文件: {novel_file}")

        if len(novel_files) > 1:
            print(f"⚠️  注意: 找到多个文件，仅处理第一个。其他文件:")
            for file in novel_files[1:]:
                print(f"  - {file.name}")

    print(f"\n--- [STEP 3/3] 开始编译小说 ---")

    # 创建编译器并运行
    compiler = NovelCompiler(novel_file, user_id)
    await compiler.run()

    print(f"\n{'='*60}")
    print(f"✅✅✅ 编译完成！用于前端测试的用户ID: {user_id} ✅✅✅")
    print(f"{'='*60}")

def main():
    """主函数"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n[INFO] 用户手动中断了脚本。")
    except Exception as e:
        print(f"\n[FATAL] 脚本执行过程中发生未捕获的顶层异常: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()