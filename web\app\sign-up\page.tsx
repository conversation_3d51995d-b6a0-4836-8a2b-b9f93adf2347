"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import Link from "next/link"

export default function SignUp() {
  const [showModal, setShowModal] = useState(false)

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="flex items-center justify-center h-full p-6">
        <Card className="w-full max-w-md bg-gray-900 border-gray-700">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white">Register for Free</CardTitle>
            <div className="text-red-400 text-sm font-medium">Only <PERSON>ail Required</div>
            <CardDescription className="text-gray-400 space-y-1">
              <div>Fully Uncensored Chats</div>
              <div>Unlock NSFW Chatbots</div>
              <div>Favorite Chatbots and Saved Chats</div>
              <div>Create your own Chatbots</div>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button className="w-full bg-blue-600 hover:bg-blue-700" onClick={() => setShowModal(true)}>
              Create Free Account
            </Button>
            <div className="text-center text-sm text-gray-400">
              Already have an account?{" "}
              <Link href="/sign-in" className="text-blue-400 hover:underline">
                Login
              </Link>
            </div>
          </CardContent>
        </Card>

        <Dialog open={showModal} onOpenChange={setShowModal}>
          <DialogContent className="bg-gray-900 border-gray-700 text-white">
            <DialogHeader>
              <DialogTitle>Sign Up</DialogTitle>
              <DialogDescription className="text-gray-400">Create your free account to get started</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-white">Email</label>
                <Input
                  type="email"
                  className="mt-1 bg-gray-800 border-gray-600 text-white"
                  placeholder="Enter your email"
                />
              </div>
              <Button className="w-full bg-blue-600 hover:bg-blue-700">Create Account</Button>
              <div className="text-center text-gray-400">OR</div>
              <div className="space-y-2">
                <Button variant="outline" className="w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                  <span className="mr-2">G</span> Continue with Google
                </Button>
                <Button variant="outline" className="w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                  <span className="mr-2">🍎</span> Continue with Apple
                </Button>
                <Button variant="outline" className="w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                  <span className="mr-2">X</span> Continue with X
                </Button>
                <Button variant="outline" className="w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                  <span className="mr-2">🎮</span> Continue with Discord
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
