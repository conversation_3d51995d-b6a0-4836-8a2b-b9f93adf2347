# ID类型错误修复总结

## 问题分析

根据你提供的错误分析，核心问题在于**前端与后端之间数据ID类型的不匹配**：

1. **数据库设计**：所有主键都使用 `UUID` 类型（如 `550e8400-e29b-41d4-a716-************`）
2. **前端错误**：错误地使用整数ID（如 `"1"`, `"2"`）来请求后端API
3. **后端错误**：当收到 `/api/agents/1` 请求时，PostgreSQL报错 `invalid input syntax for type uuid: "1"`

## 已修复的问题

### 1. 前端ID处理错误修复

**文件：`web/app/page.tsx`**
- **问题**：第166行 `id: agent.id.toString()` 错误地将UUID转换为字符串
- **修复**：直接使用 `id: agent.id`，因为UUID本身就是字符串类型

### 2. 模拟数据ID格式修复

修复了以下文件中的硬编码数字ID，全部改为UUID格式：

**文件：`web/app/page.tsx`**
- 修复 `featuredBots` 数组中的ID从 `"1"`, `"2"` 改为UUID格式

**文件：`web/app/chat/[id]/page.tsx`**
- 修复 `characterData` 对象的key从数字改为UUID格式
- 更新后备字符串引用

**文件：`web/app/recommended-bots/page.tsx`**
- 修复 `bots` 数组中的所有ID

**文件：`web/app/creators/leaderboard/page.tsx`**
- 修复创作者数据中的ID

**文件：`web/app/chats/page.tsx`**
- 修复 `defaultConversations` 中的ID

**文件：`web/app/blocked-creators/page.tsx`**
- 修复 `blockedCreators` 中的ID

**文件：`web/app/author/[username]/page.tsx`**
- 修复 `authorBots` 数组中的所有ID

### 3. WebSocket认证问题修复

**文件：`web/lib/api.ts`**
- **问题**：WebSocket连接缺少认证token，导致403 Forbidden错误
- **修复**：在 `createWebSocket` 方法中添加token参数支持

**文件：`web/hooks/use-websocket-chat.ts`**
- 更新WebSocket hook以支持token认证
- 修复依赖数组以包含token参数

## 修复后的数据流

### 正确的ID处理流程：
1. **后端API**：返回UUID格式的agent.id（如 `550e8400-e29b-41d4-a716-************`）
2. **前端接收**：直接使用UUID字符串，不进行任何转换
3. **路由跳转**：使用完整的UUID进行页面跳转（如 `/chat/550e8400-e29b-41d4-a716-************`）
4. **API请求**：使用UUID向后端请求数据（如 `/api/agents/550e8400-e29b-41d4-a716-************`）

### WebSocket认证流程：
1. **连接创建**：包含token参数的WebSocket URL
2. **后端验证**：验证token以确认用户身份
3. **连接建立**：成功建立认证的WebSocket连接

## 测试建议

1. **API测试**：确认所有agent相关的API端点都能正确处理UUID格式的ID
2. **前端测试**：验证从首页点击角色卡片能正确跳转到聊天页面
3. **WebSocket测试**：确认聊天功能的实时消息传输正常工作
4. **错误处理**：验证当使用无效UUID时的错误处理机制

## 注意事项

1. **一致性**：确保所有新增的模拟数据都使用UUID格式
2. **类型安全**：TypeScript类型定义中的ID字段应该是 `string` 类型
3. **后端兼容**：确保后端API能正确处理UUID格式的参数
4. **认证机制**：WebSocket连接需要适当的认证token传递机制

这些修复应该解决你遇到的 `invalid input syntax for type uuid: "1"` 错误和WebSocket 403 Forbidden问题。