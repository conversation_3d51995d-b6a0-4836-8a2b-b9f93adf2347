# backend/src/services/chat_service.py
"""
聊天服务层 - 负责处理所有与聊天相关的复杂业务逻辑
将业务逻辑从API层剥离，提高代码的可维护性和可测试性
"""
import asyncio
import json
import traceback
import uuid
from typing import Optional, List, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect

from .llm_service import llm_service
from .supabase_service import supabase_service
from .prompt_assembler import prompt_assembler
from .summarization_service import summarization_service


class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, chat_id: str):
        await websocket.accept()
        if chat_id not in self.active_connections:
            self.active_connections[chat_id] = []
        self.active_connections[chat_id].append(websocket)
        print(f"INFO: WebSocket connected to chat {chat_id}. Total connections: {len(self.active_connections[chat_id])}")

    def disconnect(self, websocket: WebSocket, chat_id: str):
        if chat_id in self.active_connections:
            self.active_connections[chat_id].remove(websocket)
            if not self.active_connections[chat_id]:
                del self.active_connections[chat_id]
        print(f"INFO: WebSocket disconnected from chat {chat_id}.")

    async def broadcast(self, chat_id: str, message: str):
        if chat_id in self.active_connections:
            for connection in self.active_connections[chat_id]:
                await connection.send_text(message)

manager = ConnectionManager()


class ChatService:
    def __init__(self):
        # 用于防止对同一个聊天会话的并发处理
        self.chat_locks: Dict[str, asyncio.Lock] = {}

    def get_lock(self, chat_id: str) -> asyncio.Lock:
        """获取或创建指定chat_id的锁"""
        if chat_id not in self.chat_locks:
            self.chat_locks[chat_id] = asyncio.Lock()
        return self.chat_locks[chat_id]
    
    async def start_chat_with_agent(self, user_id: str, agent_id: str) -> str:
        """处理开始与Agent聊天的逻辑，返回chat_id"""
        print(f"INFO: 开始处理用户 {user_id} 与角色 {agent_id} 的会话请求")

        # 检查是否已有现有会话
        existing_chat = await supabase_service.get_chat_by_user_and_agent(user_id, agent_id)
        if existing_chat:
            print(f"SUCCESS: 找到现有会话 {existing_chat['id']}，用户 {user_id} 与角色 {agent_id}")
            return existing_chat["id"]

        print(f"INFO: 未找到现有会话，为用户 {user_id} 与角色 {agent_id} 创建新会话")

        # 验证角色是否存在
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            print(f"ERROR: 角色 {agent_id} 不存在")
            raise ValueError("Agent not found")

        # 创建新的聊天会话
        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=[agent_id])
        if not chat_id:
            print(f"ERROR: 为用户 {user_id} 与角色 {agent_id} 创建会话失败")
            raise ValueError("Failed to create chat session")

        print(f"SUCCESS: 成功创建新会话 {chat_id}，用户 {user_id} 与角色 {agent_id}")

        # 添加角色的开场白（如果有）
        if agent.get("first_mes"):
            await supabase_service.add_message_to_chat(
                chat_id=chat_id, role='assistant', content=agent["first_mes"], agent_id=agent_id
            )
            print(f"INFO: 已添加角色 {agent_id} 的开场白到会话 {chat_id}")

        return chat_id
    
    async def process_user_message(self, chat_id: str, user_id: str, content: str, target_agent_id: Optional[str], manager):
        """处理用户发送的消息的核心入口"""
        lock = self.get_lock(chat_id)
        async with lock:
            try:
                # 1. 保存用户消息
                user_message_record = await supabase_service.add_message_to_chat(
                    chat_id=chat_id, role='user', content=content, agent_id=None
                )
                if not user_message_record:
                    await manager.broadcast(chat_id, json.dumps({"type": "error", "content": "Failed to save user message."}))
                    return
                
                # 异步生成并保存 embedding
                if user_message_record.get('id'):
                    asyncio.create_task(self.generate_and_save_embedding(user_message_record['id'], content))

                # 2. 获取会话信息判断模式
                chat_session = await supabase_service.get_chat_by_id(chat_id)
                is_story_mode = chat_session.get("story_id") is not None

                # 3. 确定回复的AI角色
                participants = await supabase_service.get_chat_participants(chat_id)
                replying_agent = self._get_replying_agent(participants, target_agent_id)
                if not replying_agent:
                    await manager.broadcast(chat_id, json.dumps({"type": "error", "content": "No agent available for reply."}))
                    return

                # 4. 调用AI进行回复
                await self.process_ai_response(chat_id, user_message_record, replying_agent, is_story_mode, manager)

            except Exception as e:
                print(f"ERROR: 处理用户消息失败 for chat {chat_id}: {e}")
                traceback.print_exc()
                await manager.broadcast(chat_id, json.dumps({"type": "error", "content": f"处理消息失败: {str(e)}"}))

    def _get_replying_agent(self, participants: List[Dict], target_agent_id: Optional[str]) -> Optional[Dict]:
        """根据目标ID或默认规则确定回复的Agent"""
        if not participants:
            return None
        if target_agent_id:
            return next((p for p in participants if p['id'] == target_agent_id), participants[0])
        return participants[0]

    async def process_ai_response(self, chat_id: str, user_message: Dict, agent: Dict, is_story_mode: bool, manager):
        """构建Prompt，调用LLM，并处理响应流"""
        mode = 'story' if is_story_mode else 'chat'
        try:
            # 使用快速构建模式，立即响应
            prompt = await prompt_assembler.build_fast_prompt(
                chat_id=chat_id, user_message=user_message['content'], agent_id=agent['id'], mode=mode
            )
            print(f"SUCCESS: 为角色 {agent.get('name')} 构建了{mode}模式的快速提示词")
        except Exception as e:
            print(f"WARN: 快速提示词构建失败，将使用基础模板: {e}")
            # 降级方案：使用简单的历史记录构建提示词
            history = await supabase_service.get_messages_by_chat_id(chat_id, limit=10)
            history_str = "\n".join([f"{msg['role']}: {msg['content']}" for msg in history])
            prompt = f"角色扮演：{agent.get('name')}\n{agent.get('personality')}\n历史对话：\n{history_str}\n用户：{user_message['content']}\n{agent.get('name')}:"
        
        # 流式处理并保存响应
        full_response = await self.stream_and_save_response(chat_id, agent, prompt, manager)

        # --- 触发所有后台任务 ---
        # 1. 异步生成并广播用户选项
        asyncio.create_task(self.regenerate_and_broadcast_choices(chat_id, agent, full_response, is_story_mode, manager))
        
        # 2. 异步检查并更新摘要
        asyncio.create_task(summarization_service.update_chat_summary(chat_id))

        # 3. 异步更新羁绊值
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if chat_session and chat_session.get('user_id'):
            asyncio.create_task(self.update_bond_value_and_broadcast(chat_session['user_id'], agent['id'], chat_id, manager))

        # 4. 异步进行完整的RAG增强（用于未来可能的模型优化，不影响本次响应）
        asyncio.create_task(prompt_assembler.build_prompt(
            chat_id=chat_id, user_message=user_message['content'], agent_id=agent['id'], mode=mode
        ))

    async def stream_and_save_response(self, chat_id: str, agent: Dict, prompt: str, manager) -> str:
        """流式发送响应到客户端并最终保存到数据库"""
        full_ai_response = ""
        temp_message_id = f"temp_{uuid.uuid4().hex}"

        # 流式广播文本块
        async for chunk in llm_service.get_streaming_chat_response(prompt):
            if chunk:
                full_ai_response += chunk
                await manager.broadcast(chat_id, json.dumps({
                    "type": "message_chunk", "temp_id": temp_message_id, "role": "assistant",
                    "agent_id": agent['id'], "content_chunk": chunk
                }))
                await asyncio.sleep(0.01)  # 防止WebSocket拥塞
        
        # 保存完整消息到数据库
        final_content = full_ai_response.strip()
        if final_content:
            ai_message = await supabase_service.add_message_to_chat(
                chat_id=chat_id, role='assistant', content=final_content, agent_id=agent['id']
            )
            await manager.broadcast(chat_id, json.dumps({
                "type": "stream_end", "temp_id": temp_message_id, "final_message": ai_message
            }, default=str))
            
            # 异步生成并保存 embedding
            if ai_message and ai_message.get('id'):
                asyncio.create_task(self.generate_and_save_embedding(ai_message['id'], final_content))

        return final_content

    async def regenerate_and_broadcast_choices(self, chat_id: str, agent: dict, last_ai_message: str, is_story_mode: bool, manager):
        """生成并广播用户回复选项"""
        try:
            print(f"INFO: [BG Task] 开始生成用户选项 for chat {chat_id}")

            # 获取最近的对话历史
            history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
            chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])

            # 获取章节任务目标（如果是故事模式）
            chapter_task_objective = None
            participants_list = []
            if is_story_mode:
                chat_session = await supabase_service.get_chat_by_id(chat_id)
                story_id = chat_session.get('story_id')
                if story_id:
                    try:
                        chapters = await supabase_service.get_story_chapters(story_id)
                        if chapters:
                            current_chapter = chapters[0]
                            chapter_task_objective = current_chapter.get('mission_objective_text', '')
                        participants_list = await supabase_service.get_story_participants(story_id)
                    except Exception as e:
                        print(f"WARN: Failed to get chapter task objective: {e}")

            # 生成用户回复选项
            user_choices = await llm_service.generate_user_reply_choices(
                agent_name=agent.get('name', 'AI'),
                agent_persona=agent.get('personality', ''),
                chat_history=chat_history_str,
                last_ai_message=last_ai_message,
                chapter_task_objective=chapter_task_objective,
                target_agent_id=agent.get('id'),
                include_target_agent_id=is_story_mode,
                participants=participants_list
            )

            # 广播选项更新事件
            choices_payload = {
                "type": "choices_updated",
                "choices": [choice.model_dump() for choice in user_choices] if user_choices else []
            }
            await manager.broadcast(chat_id, json.dumps(choices_payload, default=str))
            print(f"SUCCESS: [BG Task] 成功生成并广播 {len(user_choices)} 个用户选项 for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: [BG Task] 生成用户选项失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            # 向客户端发送错误通知
            await manager.broadcast(chat_id, json.dumps({"type": "choices_failed", "error": str(e)}))

    async def update_bond_value_and_broadcast(self, user_id: str, agent_id: str, chat_id: str, manager):
        """更新羁绊值并广播"""
        try:
            # 实现暴击逻辑 (例如10%的概率)
            import random
            is_critical_hit = random.random() < 0.1
            increase_amount = 2 if is_critical_hit else 1
            
            bond_result = await supabase_service.increment_bond_value(user_id, agent_id, increase_amount)
            
            # 通过WebSocket将更新后的羁绊值和等级通知前端
            if bond_result and bond_result.get('success'):
                await manager.broadcast(chat_id, json.dumps({
                    "type": "bond_update",
                    "agent_id": agent_id,
                    "new_bond_value": bond_result.get('new_bond_value'),
                    "new_bond_level": bond_result.get('new_bond_level'),
                    "is_critical_hit": is_critical_hit,
                    "increase_amount": increase_amount
                }))
                print(f"SUCCESS: [BG Task] 羁绊值更新完成 for user {user_id} and agent {agent_id}")
        except Exception as e:
            print(f"ERROR: [BG Task] 增加羁绊值失败: {e}")
        
    async def generate_and_save_embedding(self, message_id: int, content: str):
        """为消息生成并保存向量"""
        try:
            print(f"INFO: 开始为消息 {message_id} 生成embedding...")
            embedding = await llm_service.get_embedding(content)
            success = await supabase_service.update_message_embedding(message_id, embedding)
            if success:
                print(f"SUCCESS: 消息 {message_id} 的embedding已保存，可用于RAG检索")
            else:
                print(f"WARN: 消息 {message_id} 的embedding保存失败")
        except Exception as e:
            print(f"ERROR: 为消息 {message_id} 生成embedding失败: {e}")

    async def regenerate_user_choices(self, chat_id: str, manager):
        """重新生成用户回复选项（用于WebSocket的regenerate_choices动作）"""
        try:
            print(f"INFO: [WebSocket] 开始重新生成用户选项 for chat {chat_id}")

            # 获取聊天会话信息
            chat = await supabase_service.get_chat_by_id(chat_id)
            if not chat:
                print(f"ERROR: Chat {chat_id} not found")
                error_message = {
                    "type": "choices_regenerated",
                    "choices": [],
                    "error": "聊天会话不存在"
                }
                await manager.broadcast(chat_id, json.dumps(error_message))
                return

            # 获取参与者信息
            participants = await supabase_service.get_chat_participants(chat_id)
            if not participants:
                print(f"ERROR: No participants found for chat {chat_id}")
                error_message = {
                    "type": "choices_regenerated",
                    "choices": [],
                    "error": "未找到聊天参与者"
                }
                await manager.broadcast(chat_id, json.dumps(error_message))
                return

            # 确定主要角色
            story_id = chat.get('story_id')
            protagonist_agent_id = None
            if story_id:
                story_detail = await supabase_service.get_story_by_id(story_id)
                protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

            agent = None
            for participant in participants:
                if participant.get('id') != protagonist_agent_id:
                    agent = participant
                    break

            if not agent and participants:
                agent = participants[0]

            if not agent:
                print(f"ERROR: No agent found for chat {chat_id}")
                return

            # 获取最近的对话历史
            history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
            chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])

            # 获取最后一条AI消息
            last_ai_message = ""
            for msg in reversed(history_for_choices):
                if msg['role'] == 'assistant':
                    last_ai_message = msg['content']
                    break

            if not last_ai_message:
                print(f"WARN: No AI message found for regenerating choices in chat {chat_id}")
                return

            # 获取章节任务目标（如果是故事模式）
            chapter_task_objective = None
            is_story_mode = story_id is not None
            participants_list = []
            
            if is_story_mode:
                try:
                    chapters = await supabase_service.get_story_chapters(story_id)
                    if chapters:
                        current_chapter = chapters[0]
                        chapter_task_objective = current_chapter.get('mission_objective_text', '')
                    participants_list = await supabase_service.get_story_participants(story_id)
                except Exception as e:
                    print(f"WARN: Failed to get chapter task objective for regeneration: {e}")

            # 生成新的用户选项
            user_choices = await llm_service.generate_user_reply_choices(
                agent_name=agent.get('name', 'AI'),
                agent_persona=agent.get('personality', ''),
                chat_history=chat_history_str,
                last_ai_message=last_ai_message,
                chapter_task_objective=chapter_task_objective,
                target_agent_id=agent.get('id'),
                include_target_agent_id=is_story_mode,
                participants=participants_list
            )

            # 广播重新生成的选项
            choices_message = {
                "type": "choices_regenerated",
                "choices": [choice.model_dump() for choice in user_choices] if user_choices else []
            }
            await manager.broadcast(chat_id, json.dumps(choices_message, default=str))

            print(f"SUCCESS: [WebSocket] 成功重新生成 {len(user_choices)} 个用户选项 for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: [WebSocket] 重新生成用户选项失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            error_message = {
                "type": "choices_regenerated",
                "choices": [],
                "error": f"生成选项失败: {str(e)}"
            }
            try:
                await manager.broadcast(chat_id, json.dumps(error_message))
            except Exception as broadcast_error:
                print(f"ERROR: 发送错误消息失败: {broadcast_error}")

    async def send_initial_state(self, websocket: WebSocket, chat_id: str):
        """发送初始状态包给WebSocket客户端"""
        try:
            chat_session = await supabase_service.get_chat_by_id(chat_id)
            if not chat_session:
                await websocket.send_text(json.dumps({"type": "error", "content": "Chat session not found."}))
                return

            is_story_mode = chat_session.get("story_id") is not None
            protagonist_agent_id = None

            if is_story_mode:
                story_id = chat_session.get("story_id")
                story_detail = await supabase_service.get_story_by_id(story_id)
                if story_detail:
                    protagonist_agent_id = story_detail.get("protagonist_agent_id")
                    print(f"=== PROTAGONIST DEBUG (Backend) ===\nStory ID: {story_id}\nStory title: {story_detail.get('title')}\nProtagonist agent ID: {protagonist_agent_id}\n=====================================")

            participants = await supabase_service.get_chat_participants(chat_id)

            if is_story_mode and protagonist_agent_id:
                protagonist_agent = await supabase_service.get_agent_by_id(protagonist_agent_id)
                if protagonist_agent:
                    participants.append(protagonist_agent)
                    print(f"DEBUG: Added protagonist agent {protagonist_agent.get('name')} to participants list")

            initial_state_package = {
                "type": "game_state_sync",
                "data": {
                    "chat_id": chat_id,
                    "is_story_mode": is_story_mode,
                    "protagonist_agent_id": protagonist_agent_id,
                    "messages": await supabase_service.get_messages_by_chat_id(chat_id, limit=100),
                    "participants": participants,
                    "task_progress": chat_session.get("task_progress", {}),
                }
            }
            await websocket.send_text(json.dumps(initial_state_package, default=str))
            print(f"INFO: Sent initial game state for chat {chat_id}")
            
            if is_story_mode:
                print(f"INFO: Chat {chat_id} is in Story Mode.")

        except WebSocketDisconnect:
            # 这是一个预期的异常，当客户端在发送期间关闭连接时发生。
            # 只需记录并安静地退出即可，无需再尝试发送错误消息。
            print(f"WARN: Client disconnected during send_initial_state for chat {chat_id}.")
        except Exception as e:
            print(f"ERROR: 发送初始状态失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            # 在向已关闭的连接发送消息时，这里也可能失败，所以需要一个额外的try-except
            try:
                await websocket.send_text(json.dumps({"type": "error", "content": f"Failed to send initial state: {str(e)}"}))
            except WebSocketDisconnect:
                print(f"WARN: Client was already disconnected when trying to send error for chat {chat_id}.")

    async def handle_websocket_message(self, chat_id: str, user_id: str, message_data: Dict[str, Any]):
        """处理WebSocket消息"""
        try:
            action = message_data.get("action")
            
            # 处理重新生成选项的动作
            if action == 'regenerate_choices':
                asyncio.create_task(self.regenerate_user_choices(chat_id, self.manager))
                return

            # 处理故事模式的next动作
            chat_session = await supabase_service.get_chat_by_id(chat_id)
            is_story_mode = chat_session.get("story_id") is not None
            
            if is_story_mode and action == 'next':
                # 这里需要调用process_opening_sequence，但现在它还在supabase_main.py中
                # 暂时先跳过，后续会移动这个函数
                print(f"INFO: Story mode 'next' action for chat {chat_id} - 需要实现process_opening_sequence")
                return

            # 处理普通消息
            content = message_data.get("content", "").strip()
            if not content:
                print(f"INFO: Received empty content message, skipping...")
                return

            target_agent_id = message_data.get("target_agent_id")
            target_agent_index = message_data.get("target_agent_index")

            if is_story_mode:
                # 故事模式的复杂逻辑暂时保留在supabase_main.py中
                # 这里需要进一步重构
                print(f"INFO: Story mode message processing for chat {chat_id} - 需要进一步重构")
                return
            else:
                # 将常规聊天的消息处理委托给现有的方法
                await self.process_user_message(
                    chat_id=chat_id,
                    user_id=user_id,
                    content=content,
                    target_agent_id=target_agent_id,
                    manager=self.manager
                )

        except Exception as e:
            print(f"ERROR: 处理WebSocket消息失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            await self.manager.broadcast(chat_id, json.dumps({"type": "error", "content": f"处理消息失败: {str(e)}"}))

    # 将manager设为实例属性，方便访问
    @property
    def manager(self):
        return manager


# 创建一个全局单例
chat_service = ChatService()