"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  ImageIcon,
  Mic,
  Share,
  MoreHorizontal,
  MessageSquare,
  Trash2,
  ThumbsUp,
  RotateCcw,
  ArrowRight,
  Star,
  Volume2,
  Loader2,
  AlertCircle,
} from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/contexts/AuthContext"
import { apiClient, type Agent, type Message } from "@/lib/api"

// 使用API中的Message类型，避免重复定义
interface LocalMessage {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
  username?: string
}

// 修复：使用UUID格式的模拟数据作为后备，添加image属性
const characterData = {
  "550e8400-e29b-41d4-a716-************": { 
    name: "<PERSON>", 
    creator: "zays", 
    description: "Soulmate",
    image: "/placeholder.svg?height=40&width=40"
  },
  "550e8400-e29b-41d4-a716-************": { 
    name: "Rayhan", 
    creator: "zays", 
    description: "The man who married you who is a widow with two children",
    image: "/placeholder.svg?height=40&width=40"
  },
  "550e8400-e29b-41d4-a716-************": { 
    name: "Kenji, Riku, Shinji", 
    creator: "zays", 
    description: "|| Accidentally raising monsters?",
    image: "/placeholder.svg?height=40&width=40"
  },
}

export default function ChatPage() {
  const params = useParams()
  const router = useRouter()
  const { user, isLoading: isAuthLoading, signInAsGuest } = useAuth()
  
  // 状态管理
  const [agent, setAgent] = useState<Agent | null>(null)
  const [chatId, setChatId] = useState<string | null>(null)
  const [input, setInput] = useState("")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [choices, setChoices] = useState<{text: string}[]>([])
  const [useRealAPI, setUseRealAPI] = useState(true)
  
  // WebSocket连接
  const wsRef = useRef<WebSocket | null>(null)
  const characterId = params.id as string
  
  // 模拟数据作为后备
  const fallbackCharacter = characterData[characterId as keyof typeof characterData] || characterData["550e8400-e29b-41d4-a716-************"]
  const [fallbackMessages, setFallbackMessages] = useState<LocalMessage[]>([])

  // 初始化聊天
  const initializeChat = useCallback(async () => {
    if (isAuthLoading) return // 等待认证状态加载完成
    
    if (!user) {
      await signInAsGuest() // 如果没有用户，则自动以游客身份登录
      return // 登录后，useEffect会因为user变化而重新运行
    }

    setLoading(true)
    setError(null)

    try {
      if (useRealAPI) {
        console.log('Initializing chat with real API for agent:', characterId, 'user:', user.id)
        
        // 1. 获取角色信息
        const agentData = await apiClient.getAgent(characterId)
        setAgent(agentData)
        
        // 2. 开始聊天会话 - 使用真实的用户ID
        const chatResponse = await apiClient.startChat(characterId, user.id)
        setChatId(chatResponse.chat_id)
        
        // 3. 获取历史消息
        const historyMessages = await apiClient.getChatHistory(chatResponse.chat_id)
        setMessages(historyMessages)
        
        // 4. 连接WebSocket
        if (wsRef.current) {
          wsRef.current.close()
        }
        
        const token = localStorage.getItem('access_token')
        const ws = apiClient.createWebSocket(chatResponse.chat_id, user.id, token || undefined)
        wsRef.current = ws
        
        ws.onopen = () => console.log('WebSocket连接成功')
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data)
          console.log('WebSocket message received:', data)
          
          // 处理不同类型的WebSocket消息
          switch (data.type) {
            case 'message_chunk':
              // 流式消息块
              setMessages(prev => {
                const existing = prev.find(m => m.id === data.temp_id)
                if (existing) {
                  return prev.map(m => 
                    m.id === data.temp_id 
                      ? { ...m, content: m.content + data.content_chunk }
                      : m
                  )
                } else {
                  return [...prev, {
                    id: data.temp_id,
                    role: data.role,
                    content: data.content_chunk,
                    agent_id: data.agent_id,
                    created_at: new Date().toISOString()
                  }]
                }
              })
              break
              
            case 'stream_end':
              // 流式消息结束，更新为最终消息
              if (data.final_message) {
                setMessages(prev => 
                  prev.map(m => 
                    m.id === data.temp_id 
                      ? {
                          id: data.final_message.id,
                          role: data.final_message.role,
                          content: data.final_message.content,
                          agent_id: data.final_message.agent_id,
                          created_at: data.final_message.created_at
                        }
                      : m
                  )
                )
              }
              break
              
            case 'error':
              console.error('WebSocket error:', data.content)
              setError(data.content)
              break
          }
        }
        
        ws.onclose = (event) => {
          console.log('WebSocket连接已关闭', { code: event.code, reason: event.reason })
        }
        ws.onerror = (event) => {
          console.error('WebSocket错误:', {
            type: event.type,
            target: event.target?.readyState,
            timestamp: new Date().toISOString()
          })
        }
        
        console.log('Chat initialized:', { agent: agentData.name, chatId: chatResponse.chat_id })
      } else {
        throw new Error('Using fallback mode')
      }
    } catch (err) {
      console.error('Failed to initialize real API chat:', err)
      setError('Failed to connect to server. Using offline mode.')
      setUseRealAPI(false)
      
      // 使用模拟数据
      const initialMessages: LocalMessage[] = [
        {
          id: "550e8400-e29b-41d4-a716-************",
          role: "assistant",
          content: `Hello! I'm ${fallbackCharacter.name}. ${fallbackCharacter.description}. How can I help you today?`,
          timestamp: new Date(),
        },
      ]
      setFallbackMessages(initialMessages)
    } finally {
      setLoading(false)
    }
  }, [characterId, user, isAuthLoading, signInAsGuest, useRealAPI, fallbackCharacter])

  useEffect(() => {
    if (characterId) {
      initializeChat()
    }
    
    // 组件卸载时关闭WebSocket连接
    return () => {
      wsRef.current?.close()
    }
  }, [characterId, initializeChat])

  // 发送消息处理
  const handleSendMessage = async () => {
    if (!input.trim()) return

    if (useRealAPI && chatId && wsRef.current?.readyState === WebSocket.OPEN) {
      // 使用真实API发送消息
      const userMessage: Message = {
        id: `temp_${Date.now()}`,
        role: 'user',
        content: input.trim(),
        created_at: new Date().toISOString()
      }
      
      setMessages(prev => [...prev, userMessage])
      
      wsRef.current.send(JSON.stringify({
        action: 'message',
        content: input.trim(),
      }))
      
      setInput("")
    } else {
      // 使用模拟模式
      const userMessage: LocalMessage = {
        id: Date.now().toString(),
        role: "user",
        content: input,
        timestamp: new Date(),
        username: user?.display_name || "Guest",
      }

      const newMessages = [...fallbackMessages, userMessage]
      setFallbackMessages(newMessages)
      setInput("")

      // 模拟AI回复
      setTimeout(() => {
        const aiMessage: LocalMessage = {
          id: (Date.now() + 1).toString(),
          role: "assistant",
          content: `Thank you for your message: "${input}". This is a simulated response from ${fallbackCharacter.name}.`,
          timestamp: new Date(),
        }

        setFallbackMessages((prev) => [...prev, aiMessage])
      }, 1000)
    }
  }

  // 重新生成选项
  const handleRegenerateChoices = () => {
    if (useRealAPI && chatId && wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        action: 'regenerate_choices'
      }))
    }
  }

  // 获取当前显示的角色和消息
  const currentCharacter = useRealAPI && agent ? {
    name: agent.name,
    creator: agent.creator_name || 'Unknown',
    description: agent.description || agent.personality || 'No description available',
    image: agent.avatar_url || agent.image_url
  } : fallbackCharacter

  const currentMessages = useRealAPI && chatId ? messages : fallbackMessages

  const getCharacterUrl = (characterName: string) => {
    return `/character/${characterName.toLowerCase()}`
  }

  // 加载状态
  if (loading) {
    return (
      <div className="max-w-4xl mx-auto flex flex-col h-full bg-black text-white">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
            <p className="text-white">Initializing chat...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto flex flex-col h-full bg-black text-white">
      {/* Error Notice */}
      {error && (
        <div className="bg-red-900/50 border-b border-red-700 p-3 text-center">
          <div className="flex items-center justify-center gap-2">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <p className="text-sm text-red-200">{error}</p>
          </div>
        </div>
      )}

      {/* Connection Status */}
      {useRealAPI && chatId && (
        <div className={`border-b p-2 text-center text-xs ${
          wsRef.current?.readyState === WebSocket.OPEN ? 'bg-green-900/50 border-green-700 text-green-200' : 'bg-yellow-900/50 border-yellow-700 text-yellow-200'
        }`}>
          {wsRef.current?.readyState === WebSocket.OPEN ? '🟢 Connected to server' : '🟡 Connecting...'}
        </div>
      )}

      {/* Guest Notice */}
      {user && (
        <div className="bg-blue-900/50 border-b border-blue-700 p-3 text-center">
          <p className="text-sm text-blue-200">
            You are not registered, you have limited text and image generation.{" "}
            <Link href="/sign-up" className="text-blue-400 hover:underline">
              Register/upgrade plan for more features
            </Link>
            . Your chats will not be saved.
          </p>
        </div>
      )}

      {/* Header */}
      <header className="border-b border-gray-700 p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar>
            <img
              src={currentCharacter.image || "/placeholder.svg?height=40&width=40"}
              alt={currentCharacter.name}
              className="w-10 h-10 rounded-full object-cover"
            />
          </Avatar>
          <div>
            <Link
              href={getCharacterUrl(currentCharacter.name)}
              className="font-medium text-white hover:text-blue-400 transition-colors"
            >
              {currentCharacter.name}
            </Link>
            <Link href={`/author/${currentCharacter.creator}`} className="block text-xs text-orange-400 hover:underline">
              @{currentCharacter.creator}
            </Link>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-gray-800">
            <ThumbsUp className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-gray-800">
            <Share className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-gray-800">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
              <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
                <MessageSquare className="mr-2 h-4 w-4" />
                Start New Chat
              </DropdownMenuItem>
              <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
                <Trash2 className="mr-2 h-4 w-4" />
                Clear Chat
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      {/* Character Info */}
      <div className="p-4 border-b border-gray-700 text-center">
        <div className="flex items-center justify-center gap-3 mb-2">
          <Avatar className="w-16 h-16">
            <img
              src={currentCharacter.image || "/placeholder.svg?height=64&width=64"}
              alt={currentCharacter.name}
              className="w-full h-full object-cover"
            />
          </Avatar>
        </div>
        <h2 className="font-medium mb-1 text-white">{currentCharacter.name}</h2>
        <div className="flex items-center justify-center gap-2 mb-2">
          {useRealAPI && agent?.tags ? (
            agent.tags.slice(0, 2).map((tag) => (
              <Badge key={tag} variant="secondary" className="bg-gray-700 text-gray-300">
                {tag}
              </Badge>
            ))
          ) : (
            <>
              <Badge variant="secondary" className="bg-gray-700 text-gray-300">
                Dominant
              </Badge>
              <Badge variant="secondary" className="bg-gray-700 text-gray-300">
                Romantic
              </Badge>
            </>
          )}
        </div>
        <p className="text-sm text-gray-400">{currentCharacter.description}</p>
        <p className="text-xs text-gray-500 mt-2">
          星恋AI is powered by AI for creative storytelling and roleplay. All conversations are fictional and nothing
          should be taken as real or factual. Enjoy responsibly!
        </p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-auto p-4 space-y-4">
        {/* Loading indicator */}
        {(loading || (useRealAPI && currentMessages.length === 0)) && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
            <span className="text-gray-400 text-sm">AI is thinking...</span>
          </div>
        )}

        {currentMessages.map((message, index) => {
          const isLatestAssistantMessage = message.role === "assistant" && index === currentMessages.length - 1

          return (
            <div key={message.id} className="space-y-2">
              <div className="flex items-center gap-2">
                <Avatar className="w-6 h-6">
                  {message.role === "user" ? (
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                      {user?.display_name?.charAt(0) || "G"}
                    </div>
                  ) : (
                    <img
                      src={currentCharacter.image || "/placeholder.svg?height=24&width=24"}
                      alt={currentCharacter.name}
                      className="w-full h-full object-cover"
                    />
                  )}
                </Avatar>
                <span className="text-sm font-medium text-white">
                  {message.role === "user" ? 
                    (useRealAPI ? "Guest" : (message as LocalMessage).username || "Guest") : 
                    currentCharacter.name
                  }
                </span>
              </div>
              <div className="ml-8">
                <p className="text-sm leading-relaxed text-gray-300">{message.content}</p>
                {message.role === "assistant" && (
                  <div className="flex items-center gap-2 mt-2">
                    {isLatestAssistantMessage ? (
                      <>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-800"
                          title="Regenerate"
                          onClick={handleRegenerateChoices}
                          disabled={!useRealAPI || wsRef.current?.readyState !== WebSocket.OPEN}
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-800"
                          title="Continue"
                        >
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-800"
                          title="Rate"
                        >
                          <Star className="h-3 w-3" />
                        </Button>
                      </>
                    ) : null}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-800"
                      title="Listen"
                    >
                      <Volume2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )
        })}

        {/* User Choices (only for real API) */}
        {useRealAPI && choices.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm text-gray-400">建议回复:</p>
            <div className="space-y-2">
              {choices.map((choice: {text: string}, index: number) => (
                <Button
                  key={index}
                  variant="outline"
                  className="w-full text-left justify-start bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700"
                  onClick={() => {
                    setInput(choice.text)
                  }}
                >
                  {choice.text}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex gap-2">
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-gray-800">
            <ImageIcon size={20} />
          </Button>
          <Input
            placeholder="Message..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault()
                handleSendMessage()
              }
            }}
            className="flex-1 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400"
          />
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-gray-800">
            <Mic size={20} />
          </Button>
        </div>
      </div>
    </div>
  )
}
