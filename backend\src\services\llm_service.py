#!/usr/bin/env python3
"""
统一LLM服务
整合所有AI模型交互功能，提供完整的API接口
"""

import os
import asyncio
import json
import traceback
import time
from typing import Optional, Callable, Any, Type, List, Dict, Tuple, AsyncGenerator
from functools import wraps

from google import genai
from google.genai import types
from google.api_core import exceptions as google_exceptions
from dotenv import load_dotenv
import instructor
from pydantic import BaseModel
from instructor import Mode

from ..pydantic_models import (
    ImageGenerationRequest, GeneratedCoreAgentData, 
    StoryProgressScore, UserChoice, StructuredChatResponse
)

# --- 统一的纯文本安全设置常量 ---
TEXT_ONLY_SAFETY_SETTINGS = [
    {
        "category": types.HarmCategory.HARM_CATEGORY_HARASSMENT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
]

TEXT_ONLY_SAFETY_SETTINGS_DICT = {
    types.HarmCategory.HARM_CATEGORY_HARASSMENT: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_HATE_SPEECH: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: types.HarmBlockThreshold.BLOCK_NONE,
}

# 加载环境变量
load_dotenv()

class APIManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.api_keys = self._load_api_keys()
        self.current_index = 0
        self.rate_limit_delay = 12  # 每个API密钥的速率限制间隔（秒）
        self.last_request_times = {}  # 记录每个API密钥的最后请求时间
        
    def _load_api_keys(self):
        """加载API密钥"""
        keys_str = os.getenv("GEMINI_API_KEYS", "")
        if not keys_str:
            raise ValueError("未找到GEMINI_API_KEYS环境变量")
        
        keys = [key.strip() for key in keys_str.split(",") if key.strip()]
        if not keys:
            raise ValueError("GEMINI_API_KEYS为空")
        
        print(f"INFO: 已加载 {len(keys)} 个Gemini API密钥，速率限制: 5次/60秒")
        return keys
    
    async def get_next_client(self):
        """获取下一个可用的API客户端"""
        for _ in range(len(self.api_keys)):
            api_key = self.api_keys[self.current_index]
            current_time = time.time()

            # 检查速率限制
            last_time = self.last_request_times.get(self.current_index, 0)
            if current_time - last_time >= self.rate_limit_delay:
                # 更新最后请求时间
                self.last_request_times[self.current_index] = current_time

                # 创建客户端
                client = genai.Client(api_key=api_key)

                print(f"INFO: 使用 API 密钥索引 {self.current_index}: ...{api_key[-4:]}")

                # 轮换到下一个密钥
                self.current_index = (self.current_index + 1) % len(self.api_keys)

                return client
            else:
                # 当前密钥还在速率限制中，尝试下一个
                self.current_index = (self.current_index + 1) % len(self.api_keys)
        
        # 所有密钥都在速率限制中，等待最短的剩余时间
        min_wait_time = min(
            self.rate_limit_delay - (current_time - self.last_request_times.get(i, 0))
            for i in range(len(self.api_keys))
        )
        
        if min_wait_time > 0:
            print(f"INFO: 所有API密钥都在速率限制中，等待 {min_wait_time:.1f} 秒...")
            await asyncio.sleep(min_wait_time)
        
        # 递归调用，获取可用客户端
        return await self.get_next_client()

# 全局API管理器实例
api_manager = APIManager()

def retry_on_api_error(max_retries: int = 3, base_delay: float = 5.0):
    """API错误重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                    
                except google_exceptions.ResourceExhausted as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: ⚠️  API配额耗尽 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后切换API密钥重试...")
                        await asyncio.sleep(delay)
                    
                except google_exceptions.DeadlineExceeded as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: ⏰ API请求超时 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后重试...")
                        await asyncio.sleep(delay)
                        
                except google_exceptions.ServiceUnavailable as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: 🔄 API服务过载 (尝试 {attempt + 1}/{max_retries}) - 模型繁忙中")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后切换API密钥重试...")
                        await asyncio.sleep(delay)
                        
                except Exception as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"ERROR: ❌ 函数 {func.__name__} 发生错误 (尝试 {attempt + 1}/{max_retries}): {str(e)[:100]}...")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后重试...")
                        await asyncio.sleep(delay)
            
            print(f"ERROR: [Retry Decorator] 所有重试均失败，最终错误: {last_exception}")
            raise last_exception
            
        return wrapper
    return decorator

class LLMService:
    """统一LLM服务类 - 整合所有AI模型交互功能"""
    
    def __init__(self):
        self.story_model_name = os.getenv("GEMINI_GEN_STORY_MODEL", "gemini-2.5-pro")
        self.role_model_name = os.getenv("GEMINI_GEN_ROLE_MODEL", "gemini-2.5-pro")
        self.chat_model_name = os.getenv("GEMINI_CHAT_MODEL", "gemini-2.5-flash")
        self.image_check_model_name = os.getenv("GEMINI_CHECK_IMAGE_MODEL", "gemini-2.5-flash")
        self.image_gen_model_name = os.getenv("GEMINI_GEN_IMAGE_MODEL", "gemini-2.0-flash-preview-image-generation")
        self.tts_model_name = os.getenv("GEMINI_TTS_MODEL", "gemini-2.5-flash-preview-tts")
        self.task_check_model_name = os.getenv("GEMINI_CHECK_TASK_MODEL", "gemini-2.5-flash")
        self.embedding_model_name = os.getenv("GEMINI_EMBEDDING_MODEL", "gemini-embedding-001")
        
        # 图片生成相关配置
        self.IMAGE_MAX_RETRIES = 3
        self.IMAGE_MIN_SCORE = float(os.getenv("IMAGE_MIN_SCORE", "6.0"))
        
    async def get_client(self):
        """获取API客户端"""
        return await api_manager.get_next_client()
    
    # ========================================
    # 基础文本生成方法
    # ========================================
    
    @retry_on_api_error()
    async def generate_text(self, prompt: str, model_name: Optional[str] = None,
                          temperature: float = 0.7, max_tokens: Optional[int] = None) -> str:
        """生成文本的通用方法"""
        client = await self.get_client()
        model = model_name or self.chat_model_name

        print("="*80)
        print(f"DEBUG: [LLM Request - generate_text] Prompt sent to model: {model}")
        print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
        print("="*80)

        config = types.GenerateContentConfig(
            temperature=temperature,
            max_output_tokens=max_tokens,
            safety_settings=TEXT_ONLY_SAFETY_SETTINGS
        )

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=prompt,
            config=config
        )

        if not response.text:
            raise Exception("模型未返回有效的文本响应")

        print("="*80)
        print("DEBUG: [LLM Response - generate_text] Content received:")
        print(f"--- RESPONSE START ---\n{response.text}\n--- RESPONSE END ---")
        print("="*80)

        print(f"SUCCESS: 成功生成文本响应，长度: {len(response.text)}")
        return response.text
    
    @retry_on_api_error()
    async def generate_json(self, prompt: str, model_name: Optional[str] = None,
                          temperature: float = 0.7) -> dict:
        """生成JSON响应的通用方法"""
        client = await self.get_client()
        model = model_name or self.role_model_name

        print("="*80)
        print(f"DEBUG: [LLM Request - generate_json] Prompt sent to model: {model}")
        print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
        print("="*80)

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=prompt,
            config=types.GenerateContentConfig(
                response_mime_type="application/json",
                temperature=temperature,
                safety_settings=TEXT_ONLY_SAFETY_SETTINGS
            )
        )

        if not response.text:
            raise Exception("模型未返回有效的JSON响应")

        print("="*80)
        print("DEBUG: [LLM Response - generate_json] Raw JSON received:")
        print(f"--- RESPONSE START ---\n{response.text}\n--- RESPONSE END ---")
        print("="*80)

        try:
            json_data = json.loads(response.text)
            print(f"SUCCESS: 成功生成JSON响应，包含 {len(json_data)} 个字段")
            return json_data
        except json.JSONDecodeError as e:
            print(f"ERROR: JSON解析失败: {e}")
            print(f"DEBUG: 原始响应: {response.text}")
            raise ValueError(f"JSON解析失败: {e}")
    
    @retry_on_api_error()
    async def generate_structured_response(
         self,
         prompt: Any,
         response_model: Type[BaseModel],
         model_name: Optional[str] = None,
         temperature: float = 0.7,
     ) -> Any:
        """使用 Gemini 原生 JSON 响应能力 + Pydantic 校验，返回结构化数据"""
        print(f"INFO: [Gemini] 正在为模型 '{model_name or self.story_model_name}' 生成 {response_model.__name__} 结构化响应…")

        try:
            # 1. 直接调用 generate_json 生成原始 JSON 数据
            raw_json: dict = await self.generate_json(
                prompt=prompt,
                model_name=model_name or self.story_model_name,
                temperature=temperature,
            )

            # 2. 使用 Pydantic 进行结构化验证
            if isinstance(raw_json, list):
                if len(raw_json) == 0:
                    raise ValueError("LLM 返回了空列表，无法解析为结构化对象")
                raw_json_to_validate = raw_json[0]
            else:
                raw_json_to_validate = raw_json

            structured_obj = response_model.model_validate(raw_json_to_validate)

            print(f"SUCCESS: [Gemini] {response_model.__name__} 对象生成并验证成功。")
            return structured_obj

        except Exception as e:
            print(f"ERROR: [Gemini] 生成结构化响应失败: {e}")
            traceback.print_exc()
            raise e
    
    # ========================================
    # 流式聊天响应方法
    # ========================================
    
    async def get_streaming_chat_response(self, prompt: str) -> AsyncGenerator[str, None]:
        """流式聊天响应 - 修复版"""
        try:
            client = await self.get_client()

            print("="*80)
            print(f"DEBUG: [LLM Request - streaming] Prompt sent to model: {self.chat_model_name}")
            print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
            print("="*80)

            config = types.GenerateContentConfig(
                temperature=0.7,
                safety_settings=TEXT_ONLY_SAFETY_SETTINGS
            )

            # 修复：在异步环境中正确处理同步迭代器
            # 将同步的API调用放到一个独立的线程中执行，避免阻塞事件循环
            def _generate_stream():
                return client.models.generate_content_stream(
                    model=self.chat_model_name,
                    contents=prompt,
                    config=config
                )

            response_iterator = await asyncio.to_thread(_generate_stream)

            accumulated_text = ""
            # 在异步函数中遍历同步迭代器
            for chunk in response_iterator:
                if chunk and hasattr(chunk, 'text') and chunk.text:
                    accumulated_text += chunk.text
                    yield chunk.text
                # 短暂暂停，将控制权交还给事件循环，保持服务响应性
                await asyncio.sleep(0.001)

            print(f"SUCCESS: 流式响应完成，总长度: {len(accumulated_text)}")

        except Exception as e:
            print(f"ERROR: 流式响应失败: {e}")
            traceback.print_exc()
            raise
    
    # ========================================
    # 文本生成相关方法
    # ========================================
    
    async def generate_text_response(self, prompt: str) -> str:
        """生成文本响应"""
        return await self.generate_text(prompt, model_name=self.chat_model_name)
    
    async def get_story_progress_score(self, mission: str, clear_condition: str, 
                                     current_progress: int, history: List[Dict[str, str]], 
                                     user_message: str) -> StoryProgressScore:
        """获取故事进度评分"""
        from ..prompt_templates import STORY_PROGRESS_PROMPT
        
        # 构建历史对话字符串
        history_str = "\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-10:]])
        
        prompt = STORY_PROGRESS_PROMPT.format(
            mission=mission,
            clear_condition=clear_condition,
            current_progress=current_progress,
            history=history_str,
            user_message=user_message
        )
        
        return await self.generate_structured_response(
            prompt=prompt,
            response_model=StoryProgressScore,
            model_name=self.task_check_model_name
        )
    
    async def generate_user_reply_choices(self, agent_name: str, agent_persona: str,
                                        chat_history: str, last_ai_message: str,
                                        chapter_task_objective: str = None,
                                        target_agent_id: str = None,
                                        include_target_agent_id: bool = False,
                                        participants: List[Dict] = None) -> List[UserChoice]:
        """生成用户回复选项"""
        from ..prompt_templates import USER_CHOICE_PROMPT
        
        prompt = USER_CHOICE_PROMPT.format(
            agent_name=agent_name,
            agent_persona=agent_persona,
            chat_history=chat_history,
            last_ai_message=last_ai_message,
            chapter_task_objective=chapter_task_objective or "无特定目标"
        )
        
        response = await self.generate_json(prompt, model_name=self.chat_model_name)
        
        # 转换为UserChoice对象列表
        choices = []
        for choice_data in response.get("choices", []):
            choice = UserChoice(
                text=choice_data.get("text", ""),
                emotion=choice_data.get("emotion", "neutral"),
                target_agent_id=target_agent_id if include_target_agent_id else None
            )
            choices.append(choice)
        
        return choices
    
    async def generate_game_design_document(self, story_summary: str) -> dict:
        """生成游戏设计文档"""
        from ..prompt_templates import GAME_DESIGN_PROMPT
        
        prompt = GAME_DESIGN_PROMPT.format(story_summary=story_summary)
        return await self.generate_json(prompt, model_name=self.story_model_name)
    
    async def generate_worldview_text(self, story_theme: str) -> str:
        """生成世界观文本"""
        from ..prompt_templates import WORLDVIEW_PROMPT
        
        prompt = WORLDVIEW_PROMPT.format(story_theme=story_theme)
        return await self.generate_text(prompt, model_name=self.story_model_name)
    
    async def generate_agent_backstory(self, character_info: str, story_setting: str) -> str:
        """生成角色背景故事"""
        from ..prompt_templates import AGENT_BACKSTORY_PROMPT
        
        prompt = AGENT_BACKSTORY_PROMPT.format(
            character_info=character_info,
            story_setting=story_setting
        )
        return await self.generate_text(prompt, model_name=self.role_model_name)
    
    async def summarize_long_novel_in_chunks(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """分块摘要长篇小说 - 并发处理版本"""
        if len(novel_text) <= chunk_size:
            return await self.generate_text(f"请为以下小说内容生成详细摘要：\n\n{novel_text}")
        
        # 分块处理
        chunks = [novel_text[i:i+chunk_size] for i in range(0, len(novel_text), chunk_size)]
        
        # 并发处理各个块
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def summarize_chunk(chunk):
            async with semaphore:
                return await self.generate_text(f"请为以下文本片段生成摘要：\n\n{chunk}")
        
        chunk_summaries = await asyncio.gather(*[summarize_chunk(chunk) for chunk in chunks])
        
        # 合并摘要
        combined_summary = "\n\n".join(chunk_summaries)
        
        # 最终整合
        final_prompt = f"请将以下分段摘要整合为一个完整的故事摘要：\n\n{combined_summary}"
        return await self.generate_text(final_prompt)

    async def distill_story_summary(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """提炼故事摘要 - 支持并发处理"""
        summary = await self.summarize_long_novel_in_chunks(novel_text, chunk_size, max_concurrent)
        
        # 进一步提炼
        distill_prompt = f"请将以下摘要进一步提炼为核心故事要点：\n\n{summary}"
        return await self.generate_text(distill_prompt)

    async def generate_initial_relationship_prompt(self, character_name: str, key_variables: List[str]) -> str:
        """生成初始关系描述"""
        variables_str = ", ".join(key_variables)
        prompt = f"为角色 {character_name} 生成初始关系设定，考虑以下关键要素：{variables_str}"
        return await self.generate_text(prompt)

    async def generate_interactive_scene(self, gdd: dict, chapter_gdd: dict,
                                       novel_chunk: str, agent_map: dict,
                                       character_profiles: list = None,
                                       current_game_state: dict = None,
                                       previous_chapter_summary: str = None) -> dict:
        """生成互动场景序列"""
        from ..prompt_templates import INTERACTIVE_SCENE_PROMPT
        
        prompt = INTERACTIVE_SCENE_PROMPT.format(
            gdd=json.dumps(gdd, ensure_ascii=False, indent=2),
            chapter_gdd=json.dumps(chapter_gdd, ensure_ascii=False, indent=2),
            novel_chunk=novel_chunk,
            agent_map=json.dumps(agent_map, ensure_ascii=False, indent=2),
            character_profiles=json.dumps(character_profiles or [], ensure_ascii=False, indent=2),
            current_game_state=json.dumps(current_game_state or {}, ensure_ascii=False, indent=2),
            previous_chapter_summary=previous_chapter_summary or "无"
        )
        
        return await self.generate_json(prompt, model_name=self.story_model_name)

    async def generate_chapter_summary_for_npc(self, chapter_summary: str,
                                             key_interactions: List[str]) -> str:
        """为NPC生成章节剧本摘要"""
        interactions_str = "\n".join(key_interactions)
        prompt = f"基于章节摘要和关键互动，为NPC生成剧本摘要：\n\n章节摘要：{chapter_summary}\n\n关键互动：\n{interactions_str}"
        return await self.generate_text(prompt)
    
    # ========================================
    # 图片生成相关方法
    # ========================================
    
    @retry_on_api_error()
    async def generate_enhanced_image_from_request(self, request: ImageGenerationRequest) -> Optional[Tuple[bytes, str]]:
        """生成增强图片"""
        client = await self.get_client()
        
        # 构建提示词
        if request.structured_data:
            prompt = self._convert_structured_prompt_to_string(request.structured_data)
        else:
            prompt = request.prompt
        
        print(f"INFO: 开始生成图片，提示词: {prompt[:100]}...")
        
        try:
            response = await asyncio.to_thread(
                client.models.generate_content,
                model=self.image_gen_model_name,
                contents=prompt,
                config=types.GenerateContentConfig(
                    safety_settings=TEXT_ONLY_SAFETY_SETTINGS
                )
            )
            
            if response.candidates and response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'inline_data') and part.inline_data:
                        image_data = part.inline_data.data
                        mime_type = part.inline_data.mime_type
                        print(f"SUCCESS: 图片生成成功，类型: {mime_type}")
                        return (image_data, mime_type)
            
            print("ERROR: 未能从响应中提取图片数据")
            return None
            
        except Exception as e:
            print(f"ERROR: 图片生成失败: {e}")
            return None
    
    async def generate_structured_image_from_request(self, request: ImageGenerationRequest) -> Optional[Tuple[bytes, str]]:
        """生成结构化图片"""
        return await self.generate_enhanced_image_from_request(request)
    
    def _convert_structured_prompt_to_string(self, structured_data: dict) -> str:
        """转换结构化提示词"""
        if structured_data.get("type") == "character":
            return self._build_character_prompt(structured_data)
        elif structured_data.get("type") == "cover":
            return self._build_cover_prompt(structured_data)
        else:
            # 通用转换
            parts = []
            for key, value in structured_data.items():
                if key != "type" and value:
                    parts.append(f"{key}: {value}")
            return ", ".join(parts)
    
    def _build_character_prompt(self, data: dict) -> str:
        """构建角色提示词"""
        parts = []
        
        if data.get("name"):
            parts.append(f"角色名称: {data['name']}")
        
        if data.get("appearance"):
            parts.append(f"外观: {data['appearance']}")
        
        if data.get("clothing"):
            parts.append(f"服装: {data['clothing']}")
        
        if data.get("pose"):
            parts.append(f"姿势: {data['pose']}")
        
        if data.get("background"):
            parts.append(f"背景: {data['background']}")
        
        # 添加默认的高质量描述符
        parts.append("高质量, 详细, 专业插画风格")
        
        return ", ".join(parts)
    
    def _build_cover_prompt(self, data: dict) -> str:
        """构建封面提示词"""
        parts = []
        
        if data.get("title"):
            parts.append(f"标题: {data['title']}")
        
        if data.get("theme"):
            parts.append(f"主题: {data['theme']}")
        
        if data.get("style"):
            parts.append(f"风格: {data['style']}")
        
        if data.get("elements"):
            parts.append(f"元素: {data['elements']}")
        
        # 添加封面特定描述符
        parts.append("书籍封面, 专业设计, 吸引人的视觉效果")
        
        return ", ".join(parts)
    
    async def generate_image_from_description_with_prompt(self, description: str) -> Optional[Tuple[bytes, str]]:
        """根据描述生成图片"""
        request = ImageGenerationRequest(
            prompt=description,
            width=1024,
            height=1024
        )
        return await self.generate_enhanced_image_from_request(request)
    
    # ========================================
    # 角色相关方法
    # ========================================
    
    async def generate_agent_from_analysis(self, character_gdd: Dict[str, Any],
                                         story_theme_summary: str) -> Tuple[GeneratedCoreAgentData, Optional[bytes], Optional[str]]:
        """生成角色档案"""
        from ..prompt_templates import AGENT_GENERATION_PROMPT
        
        prompt = AGENT_GENERATION_PROMPT.format(
            character_gdd=json.dumps(character_gdd, ensure_ascii=False, indent=2),
            story_theme_summary=story_theme_summary
        )
        
        # 生成角色数据
        agent_data = await self.generate_structured_response(
            prompt=prompt,
            response_model=GeneratedCoreAgentData,
            model_name=self.role_model_name
        )
        
        # 生成角色图片
        image_data = None
        image_mime_type = None
        
        if agent_data.appearance:
            image_request = ImageGenerationRequest(
                structured_data={
                    "type": "character",
                    "name": agent_data.name,
                    "appearance": agent_data.appearance,
                    "clothing": "适合角色设定的服装",
                    "pose": "自然站立姿势",
                    "background": "简洁背景"
                }
            )
            
            image_result = await self.generate_enhanced_image_from_request(image_request)
            if image_result:
                image_data, image_mime_type = image_result
        
        return agent_data, image_data, image_mime_type
    
    async def create_agent_profile(self, name: str, description: str, 
                                 personality: str, scenario: str,
                                 additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建角色档案"""
        profile = {
            "name": name,
            "description": description,
            "personality": personality,
            "scenario": scenario,
            "created_at": asyncio.get_event_loop().time()
        }
        
        if additional_data:
            profile.update(additional_data)
        
        return profile
    
    async def generate_character_variations(self, base_character: Dict[str, Any], 
                                          variation_count: int = 3) -> List[Dict[str, Any]]:
        """生成角色变体"""
        variations = []
        base_name = base_character.get("name", "角色")
        
        for i in range(variation_count):
            prompt = f"基于以下角色创建一个变体版本（变体{i+1}）：\n{json.dumps(base_character, ensure_ascii=False, indent=2)}"
            
            variation_data = await self.generate_json(prompt, model_name=self.role_model_name)
            variations.append(variation_data)
        
        return variations
    
    async def analyze_character_compatibility(self, character1: Dict[str, Any], 
                                            character2: Dict[str, Any]) -> Dict[str, Any]:
        """分析角色兼容性"""
        prompt = f"""分析以下两个角色的兼容性：

角色1：{json.dumps(character1, ensure_ascii=False, indent=2)}

角色2：{json.dumps(character2, ensure_ascii=False, indent=2)}

请分析他们的性格兼容性、互动潜力和可能的冲突点。"""
        
        return await self.generate_json(prompt, model_name=self.role_model_name)
    
    async def enhance_character_backstory(self, character: Dict[str, Any], 
                                        story_context: str = "") -> str:
        """增强角色背景故事"""
        prompt = f"""为以下角色创建详细的背景故事：

角色信息：{json.dumps(character, ensure_ascii=False, indent=2)}

故事背景：{story_context}

请创建一个丰富、有深度的背景故事。"""
        
        return await self.generate_text(prompt, model_name=self.role_model_name)
    
    def get_character_summary(self, character: Dict[str, Any]) -> str:
        """获取角色摘要"""
        name = character.get("name", "未知角色")
        personality = character.get("personality", "")
        description = character.get("description", "")
        
        summary_parts = [name]
        if personality:
            summary_parts.append(f"性格：{personality[:50]}...")
        if description:
            summary_parts.append(f"描述：{description[:50]}...")
        
        return " | ".join(summary_parts)
    
    # ========================================
    # 嵌入相关方法
    # ========================================
    
    @retry_on_api_error()
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本嵌入向量 - 修复版"""
        try:
            client = await self.get_client()

            # 修复：使用正确的客户端方法调用
            response = await asyncio.to_thread(
                client.models.embed_content,
                model=self.embedding_model_name,
                contents=text,
                config=types.EmbedContentConfig(
                    task_type="RETRIEVAL_DOCUMENT"  # 明确任务类型以获得更好的向量质量
                )
            )

            # 修复：使用正确的响应结构访问嵌入数据
            if response.embeddings and len(response.embeddings) > 0:
                return response.embeddings[0].values
            else:
                print(f"ERROR: 获取嵌入向量失败 - 响应中不包含有效的嵌入数据。响应: {response}")
                raise Exception("未能获取有效的嵌入向量")

        except Exception as e:
            print(f"ERROR: 获取嵌入向量时发生异常: {e}")
            traceback.print_exc()
            raise e
    
    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取嵌入向量"""
        embeddings = []
        
        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(5)
        
        async def get_single_embedding(text):
            async with semaphore:
                return await self.get_embedding(text)
        
        embeddings = await asyncio.gather(*[get_single_embedding(text) for text in texts])
        return embeddings
    
    def calculate_cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算余弦相似度"""
        import math
        
        # 计算点积
        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
        
        # 计算向量长度
        magnitude1 = math.sqrt(sum(a * a for a in embedding1))
        magnitude2 = math.sqrt(sum(a * a for a in embedding2))
        
        # 避免除零
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    async def semantic_search(self, query: str, documents: List[str], 
                            top_k: int = 5) -> List[Tuple[int, str, float]]:
        """语义搜索"""
        # 获取查询的嵌入向量
        query_embedding = await self.get_embedding(query)
        
        # 获取所有文档的嵌入向量
        doc_embeddings = await self.get_embeddings_batch(documents)
        
        # 计算相似度
        similarities = []
        for i, doc_embedding in enumerate(doc_embeddings):
            similarity = self.calculate_cosine_similarity(query_embedding, doc_embedding)
            similarities.append((i, documents[i], similarity))
        
        # 按相似度排序并返回top_k
        similarities.sort(key=lambda x: x[2], reverse=True)
        return similarities[:top_k]
    
    async def cluster_texts(self, texts: List[str], num_clusters: int = 3) -> Dict[int, List[int]]:
        """文本聚类"""
        # 获取所有文本的嵌入向量
        embeddings = await self.get_embeddings_batch(texts)
        
        # 简单的K-means聚类实现
        import random
        
        # 随机初始化聚类中心
        centroids = random.sample(embeddings, num_clusters)
        
        for _ in range(10):  # 最多迭代10次
            clusters = {i: [] for i in range(num_clusters)}
            
            # 分配每个点到最近的聚类中心
            for i, embedding in enumerate(embeddings):
                distances = [
                    1 - self.calculate_cosine_similarity(embedding, centroid)
                    for centroid in centroids
                ]
                closest_cluster = distances.index(min(distances))
                clusters[closest_cluster].append(i)
            
            # 更新聚类中心
            new_centroids = []
            for cluster_id in range(num_clusters):
                if clusters[cluster_id]:
                    # 计算聚类中心
                    cluster_embeddings = [embeddings[i] for i in clusters[cluster_id]]
                    centroid = [
                        sum(emb[j] for emb in cluster_embeddings) / len(cluster_embeddings)
                        for j in range(len(cluster_embeddings[0]))
                    ]
                    new_centroids.append(centroid)
                else:
                    new_centroids.append(centroids[cluster_id])
            
            centroids = new_centroids
        
        return clusters
    
    async def find_duplicate_texts(self, texts: List[str], 
                                 similarity_threshold: float = 0.9) -> List[Tuple[int, int, float]]:
        """找到重复文本"""
        embeddings = await self.get_embeddings_batch(texts)
        duplicates = []
        
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                similarity = self.calculate_cosine_similarity(embeddings[i], embeddings[j])
                if similarity >= similarity_threshold:
                    duplicates.append((i, j, similarity))
        
        return duplicates
    
    def get_embedding_stats(self, embeddings: List[List[float]]) -> Dict[str, Any]:
        """获取嵌入统计信息"""
        if not embeddings:
            return {"count": 0}
        
        import statistics
        
        # 计算维度
        dimension = len(embeddings[0])
        
        # 计算每个维度的统计信息
        dimension_stats = []
        for dim in range(dimension):
            values = [emb[dim] for emb in embeddings]
            dimension_stats.append({
                "mean": statistics.mean(values),
                "median": statistics.median(values),
                "std": statistics.stdev(values) if len(values) > 1 else 0
            })
        
        return {
            "count": len(embeddings),
            "dimension": dimension,
            "dimension_stats": dimension_stats
        }
    
    # ========================================
    # 工具方法
    # ========================================
    
    def log_success(self, operation: str, details: str = ""):
        """记录成功日志"""
        print(f"SUCCESS: {operation}" + (f" - {details}" if details else ""))
    
    def log_error(self, operation: str, error: Exception):
        """记录错误日志"""
        print(f"ERROR: {operation} - {error}")
        traceback.print_exc()

# 创建全局实例，保持与原有代码的兼容性
llm_service = LLMService()
