"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Search, Grid3X3, Heart, MessageSquare, MoreHorizontal } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"

// 修复：使用UUID格式的模拟数据
const authorBots = [
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "<PERSON>",
    description: "Soulmate",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "FemalePOV", "Romantic", "Dominant"],
    stats: { chats: "94.8k", rating: "85%", messages: "690" },
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "<PERSON>",
    description: "His papa",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "FemalePOV", "Dominant", "Gentle Dom"],
    stats: { chats: "87.7k", rating: "79%", messages: "479" },
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "Alexander",
    description: "he's not stripper, he's your boss, girl...",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "FemalePOV", "Dominant", "Gentle Dom"],
    stats: { chats: "90.5k", rating: "", messages: "558" },
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "Adam Rayson",
    description: "Your tired husband",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "FemalePOV", "Dominant", "Gentle Dom"],
    stats: { chats: "95.4k", rating: "", messages: "568" },
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440005",
    name: "Erik de Almo...",
    description: "King needs wife",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "Dominant", "Gentle Dom", "Historical"],
    stats: { chats: "64.8k", rating: "", messages: "518" },
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440006",
    name: "Alexander V...",
    description: "you're his wife... and his professor",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "FemalePOV", "Married Partner"],
    stats: { chats: "86.7k", rating: "", messages: "570" },
  },
]

export default function AuthorPage({ params }: { params: { username: string } }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [nsfw, setNsfw] = useState(false)

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href="/chats">
            <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">@{params.username}'s Chatbots</h1>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-4">
          <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800">
            <Grid3X3 className="h-5 w-5" />
          </Button>

          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Dive into endless fantasies - start searching!"
              className="pl-9 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2">
            <Switch checked={nsfw} onCheckedChange={setNsfw} />
            <span className="text-sm text-white">NSFW</span>
          </div>

          <Select defaultValue="trending">
            <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              <SelectItem value="trending">Trending</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-gray-400 mb-6">23 results found in 46ms</div>

        {/* Bot Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {authorBots.map((bot) => (
            <Card
              key={bot.id}
              className="bg-gray-900 border-gray-700 overflow-hidden hover:bg-gray-800 transition-colors cursor-pointer"
            >
              <div className="relative">
                <img src={bot.image || "/placeholder.svg"} alt={bot.name} className="w-full h-48 object-cover" />
                <div className="absolute top-2 right-2 flex gap-1">
                  <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                    <Heart className="h-4 w-4 text-white" />
                  </Button>
                  <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                    <MessageSquare className="h-4 w-4 text-white" />
                  </Button>
                  <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                    <MoreHorizontal className="h-4 w-4 text-white" />
                  </Button>
                </div>
              </div>
              <CardContent className="p-3">
                <h3 className="font-medium text-sm mb-1 line-clamp-1 text-white">{bot.name}</h3>
                <p className="text-xs text-gray-400 mb-2 line-clamp-2">{bot.description}</p>
                <div className="flex flex-wrap gap-1 mb-2">
                  {bot.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <div className="text-xs text-gray-400 flex items-center gap-2">
                  <span>{bot.stats.chats}</span>
                  {bot.stats.rating && <span>{bot.stats.rating}</span>}
                  <span>{bot.stats.messages}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
