globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/recommended-bots/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(ssr)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar.tsx":{"*":{"id":"(ssr)/./components/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/chat/[id]/page.tsx":{"*":{"id":"(ssr)/./app/chat/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/auth-guard.tsx":{"*":{"id":"(ssr)/./components/auth-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/select.tsx":{"*":{"id":"(ssr)/./components/ui/select.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/creators/leaderboard/page.tsx":{"*":{"id":"(ssr)/./app/creators/leaderboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/recommended-bots/page.tsx":{"*":{"id":"(ssr)/./app/recommended-bots/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/chats/page.tsx":{"*":{"id":"(ssr)/./app/chats/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\projects\\agent\\spicychat-clone\\web\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\components\\header.tsx":{"id":"(app-pages-browser)/./components/header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\components\\sidebar.tsx":{"id":"(app-pages-browser)/./components/sidebar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\app\\chat\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/chat/[id]/page.tsx","name":"*","chunks":[],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\components\\auth-guard.tsx":{"id":"(app-pages-browser)/./components/auth-guard.tsx","name":"*","chunks":[],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx":{"id":"(app-pages-browser)/./components/ui/select.tsx","name":"*","chunks":[],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\app\\creators\\leaderboard\\page.tsx":{"id":"(app-pages-browser)/./app/creators/leaderboard/page.tsx","name":"*","chunks":[],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\app\\recommended-bots\\page.tsx":{"id":"(app-pages-browser)/./app/recommended-bots/page.tsx","name":"*","chunks":["app/recommended-bots/page","static/chunks/app/recommended-bots/page.js"],"async":false},"E:\\projects\\agent\\spicychat-clone\\web\\app\\chats\\page.tsx":{"id":"(app-pages-browser)/./app/chats/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"E:\\projects\\agent\\spicychat-clone\\web\\":[],"E:\\projects\\agent\\spicychat-clone\\web\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"E:\\projects\\agent\\spicychat-clone\\web\\app\\loading":[],"E:\\projects\\agent\\spicychat-clone\\web\\app\\not-found":[],"E:\\projects\\agent\\spicychat-clone\\web\\app\\page":[],"E:\\projects\\agent\\spicychat-clone\\web\\app\\recommended-bots\\loading":[],"E:\\projects\\agent\\spicychat-clone\\web\\app\\recommended-bots\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(rsc)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar.tsx":{"*":{"id":"(rsc)/./components/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/chat/[id]/page.tsx":{"*":{"id":"(rsc)/./app/chat/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/auth-guard.tsx":{"*":{"id":"(rsc)/./components/auth-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/select.tsx":{"*":{"id":"(rsc)/./components/ui/select.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/creators/leaderboard/page.tsx":{"*":{"id":"(rsc)/./app/creators/leaderboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/recommended-bots/page.tsx":{"*":{"id":"(rsc)/./app/recommended-bots/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/chats/page.tsx":{"*":{"id":"(rsc)/./app/chats/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}