"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Heart,
  MessageSquare,
  Search,
  Filter,
  MoreHorizontal,
  ThumbsUp,
  Share,
  Flag,
  UserX,
  Loader2,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import Link from "next/link";
import { apiClient, type Agent } from "@/lib/api";

const filterCategories = [
  { name: "男性", count: 490, active: false },
  { name: "女性视角", count: 381, active: false },
  { name: "浪漫", count: 740, active: false },
  { name: "强势", count: 167, active: false },
  { name: "戏剧", count: 125, active: false },
  { name: "男性视角", count: 105, active: false },
  { name: "温柔强势", count: 97, active: false },
  { name: "中文", count: 90, active: false },
  { name: "身高差", count: 73, active: false },
  { name: "LGBTQ+", count: 71, active: false },
  { name: "幻想", count: 54, active: false },
  { name: "动作", count: 46, active: false },
  { name: "男男", count: 44, active: false },
  { name: "已婚伴侣", count: 43, active: false },
  { name: "成熟", count: 42, active: false },
  { name: "多角色", count: 42, active: false },
  { name: "女性", count: 41, active: false },
  { name: "肌肉", count: 39, active: false },
  { name: "原创角色", count: 39, active: false },
  { name: "温馨", count: 38, active: false },
];

// 修复：使用UUID格式的模拟数据作为后备
const featuredBots = [
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "Emma frost",
    creator: "akarixena",
    description: "The white queen (those thighs...)",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Female", "Dominant"],
    stats: { comments: "77.7k", likes: "65%", hearts: "790" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "Elion Harvey",
    creator: "akarixena",
    description: "Cruel, handsome, cold fighter.",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "Romantic", "FemalePOV", "Dominant"],
    stats: { comments: "154.3k", likes: "72%", hearts: "356" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "Marin",
    creator: "akarixena",
    description: "WLW lover — she found out what you're doing...",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Female", "FemalePOV", "Anime", "Partner", "WLW"],
    stats: { comments: "148.3k", likes: "69%", hearts: "483" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440004",
    name: "🌸 Scythia ...",
    creator: "akarixena",
    description: "The dragon princess & lover saves you from yo...",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Female", "Fantasy", "Gothic", "Anime", "MalePOV", "Drama"],
    stats: { comments: "90.5k", likes: "63%", hearts: "1228" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440005",
    name: "Princess Bazy...",
    creator: "akarixena",
    description: "The princess you are commanded to keep...",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Female", "Historical", "MalePOV", "Tsundere"],
    stats: { comments: "121.2k", likes: "58%", hearts: "406" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440006",
    name: "Damien Night...",
    creator: "akarixena",
    description: '"I hate you." "I hate you too, baby." Emma x ...',
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Romantic", "Male", "Dominant"],
    stats: { comments: "139.5k", likes: "61%", hearts: "319" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440007",
    name: "Chelsea",
    creator: "akarixena",
    description: "Your tomboy friend is... jealous?",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Female", "English", "Tomboy", "Tsundere", "Romantic"],
    stats: { comments: "59.3k", likes: "58%", hearts: "295" },
    forYou: true,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440008",
    name: "Desmond Har...",
    creator: "akarixena",
    description: "🔥💀 He always got his eyes on you.",
    image: "/placeholder.svg?height=200&width=150",
    tags: ["Male", "FemalePOV", "Historical", "Gentle Dom"],
    stats: { comments: "113.2k", likes: "74%", hearts: "889" },
    forYou: true,
  },
];

export default function Home() {
  const [searchQuery, setSearchQuery] = useState("");
  const [nsfw, setNsfw] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  useEffect(() => {
    const loadAgents = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await apiClient.getPublicAgents(20);
        setAgents(data);
      } catch (err) {
        console.error("Failed to load agents:", err);
        setError("Failed to load characters. Using fallback data.");
        setAgents([]);
      } finally {
        setLoading(false);
      }
    };

    loadAgents();
  }, []);

  // 筛选和搜索逻辑
  useEffect(() => {
    let filtered = agents.length > 0 ? agents : featuredBots.map(bot => ({
      id: bot.id,
      name: bot.name,
      description: bot.description,
      personality: bot.description,
      avatar_url: bot.image,
      image_url: bot.image,
      tags: bot.tags,
      creator_name: bot.creator,
      is_public: true,
      created_at: new Date().toISOString(),
    }));

    // 按搜索关键词筛选
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(query) ||
        agent.description?.toLowerCase().includes(query) ||
        agent.personality?.toLowerCase().includes(query) ||
        agent.creator_name?.toLowerCase().includes(query) ||
        agent.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 按标签筛选
    if (activeFilters.length > 0) {
      filtered = filtered.filter(agent =>
        activeFilters.some(filter =>
          agent.tags?.some(tag => 
            tag.toLowerCase().includes(filter.toLowerCase()) ||
            filter.toLowerCase().includes(tag.toLowerCase())
          ) ||
          agent.name.toLowerCase().includes(filter.toLowerCase()) ||
          agent.description?.toLowerCase().includes(filter.toLowerCase())
        )
      );
    }

    setFilteredAgents(filtered);
  }, [agents, searchQuery, activeFilters]);

  // 处理筛选器点击
  const handleFilterClick = (filterName: string) => {
    setActiveFilters(prev => {
      if (prev.includes(filterName)) {
        return prev.filter(f => f !== filterName);
      } else {
        return [...prev, filterName];
      }
    });
  };

  const displayedBots = filteredAgents.map((agent) => ({
    id: agent.id,
    name: agent.name,
    creator: agent.creator_name || "Unknown",
    description: agent.description || agent.personality || "暂无描述",
    image: agent.avatar_url || agent.image_url || "/placeholder.svg?height=200&width=150",
    tags: agent.tags || ["AI", "聊天"],
    stats: { comments: "0", likes: "0%", hearts: "0" },
    forYou: true,
  }));

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              className="border-gray-600 text-white hover:bg-gray-800 bg-transparent"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Narrow by tag
            </Button>

            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Dive into endless fantasies - start searching!"
                className="pl-9 bg-gray-800 border-gray-700 text-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch checked={nsfw} onCheckedChange={setNsfw} />
                <span className="text-sm text-white">NSFW</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="border-gray-600 text-white hover:bg-gray-800 bg-transparent"
              >
                Featured
              </Button>
            </div>
          </div>

          <div className="text-sm text-gray-400 mb-4">精选角色 - 开始你的冒险</div>
          
          {/* 当前筛选状态 */}
          {(activeFilters.length > 0 || searchQuery.trim()) && (
            <div className="mb-4 p-3 bg-gray-800 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <span className="text-gray-300">当前筛选:</span>
                {searchQuery.trim() && (
                  <Badge variant="secondary" className="bg-blue-600 text-white">
                    搜索: "{searchQuery}"
                  </Badge>
                )}
                {activeFilters.map(filter => (
                  <Badge 
                    key={filter} 
                    variant="secondary" 
                    className="bg-purple-600 text-white cursor-pointer hover:bg-purple-700"
                    onClick={() => handleFilterClick(filter)}
                  >
                    {filter} ×
                  </Badge>
                ))}
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white text-xs"
                  onClick={() => {
                    setActiveFilters([]);
                    setSearchQuery('');
                  }}
                >
                  清除全部
                </Button>
              </div>
              <div className="text-xs text-gray-400 mt-1">
                找到 {displayedBots.length} 个角色
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 mb-6">
            <p className="text-red-200 text-sm">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
            <span className="ml-2 text-white">Loading characters...</span>
          </div>
        )}

        {/* Grid Container */}
        {!loading && (
          <div className={`grid gap-4 ${showFilters ? "grid-cols-8" : "grid-cols-9"}`}>
            {/* Filter Panel - Only show when filters are enabled */}
            {showFilters && (
              <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 row-span-1">
                <h3 className="font-medium mb-4 text-white text-sm">Narrow by tag</h3>
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search tags" className="pl-9 bg-gray-800 border-gray-700 text-white text-sm" />
                </div>
                <div className="space-y-1 max-h-96 overflow-y-auto">
                  {filterCategories.map((category) => (
                    <div
                      key={category.name}
                      className={`flex items-center justify-between py-1 px-2 hover:bg-gray-800 rounded text-sm cursor-pointer transition-colors ${
                        activeFilters.includes(category.name) ? 'bg-blue-600 text-white' : 'text-white'
                      }`}
                      onClick={() => handleFilterClick(category.name)}
                    >
                      <span className="text-xs">{category.name}</span>
                      <span className="text-gray-400 text-xs">{category.count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Bot Cards */}
            {displayedBots.map((bot) => (
              <Link key={bot.id} href={`/chat/${bot.id}`}>
                <Card className="bg-gray-900 border-gray-700 overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="relative">
                    <img src={bot.image || "/placeholder.svg"} alt={bot.name} className="w-full h-48 object-cover" />
                    <div className="absolute top-2 right-2 flex gap-1">
                      <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                        <Heart className="h-4 w-4 text-white" />
                      </Button>
                      <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                        <MessageSquare className="h-4 w-4 text-white" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="icon" variant="ghost" className="h-8 w-8 bg-black/50 hover:bg-black/70">
                            <MoreHorizontal className="h-4 w-4 text-white" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700 text-white">
                          <DropdownMenuItem className="hover:bg-gray-700">
                            <Share className="mr-2 h-4 w-4" />
                            Share
                          </DropdownMenuItem>
                          <DropdownMenuItem className="hover:bg-gray-700">
                            <Flag className="mr-2 h-4 w-4" />
                            Report
                          </DropdownMenuItem>
                          <DropdownMenuItem className="hover:bg-gray-700">
                            <UserX className="mr-2 h-4 w-4" />
                            Block Creator
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    {bot.forYou && (
                      <div className="absolute bottom-2 left-2">
                        <Badge className="bg-purple-600 hover:bg-purple-700">For You</Badge>
                      </div>
                    )}
                  </div>
                  <CardContent className="p-3">
                    <h3 className="font-medium text-sm mb-1 line-clamp-1 text-white">{bot.name}</h3>
                    <p className="text-xs text-blue-400 mb-1">@{bot.creator}</p>
                    <p className="text-xs text-gray-400 mb-2 line-clamp-2">{bot.description}</p>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {bot.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center gap-3 text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        <span>{bot.stats.comments}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="h-3 w-3" />
                        <span>{bot.stats.likes}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        <span>{bot.stats.hearts}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
