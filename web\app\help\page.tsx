import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Search, FileText, MessageSquare, HelpCircle } from "lucide-react"
import Link from "next/link"

const faqItems = [
  {
    question: "What is SpicyChat AI?",
    answer:
      "SpicyChat AI is a platform that allows you to create and chat with AI personas and chatbots. You can create your own custom chatbots or use ones created by other users.",
  },
  {
    question: "How do I create a chatbot?",
    answer:
      "To create a chatbot, click on the 'Create Chatbot' option in the sidebar. You'll be guided through a process to set up your chatbot's name, description, appearance, and behavior instructions.",
  },
  {
    question: "Is SpicyChat AI free to use?",
    answer:
      "SpicyChat AI offers a free tier with limited features. For full access to all features, you can subscribe to our Pro or Premium plans.",
  },
  {
    question: "How do I report inappropriate content?",
    answer:
      "If you encounter inappropriate content, click on the menu in the chat and select 'Report'. You can also visit our Reporting page to submit a detailed report.",
  },
  {
    question: "Can I delete my chat history?",
    answer: "Yes, you can delete individual chats or clear your entire chat history from the settings page.",
  },
]

export default function HelpPage() {
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Help Center</h1>

      <div className="mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input placeholder="Search for help..." className="pl-10" />
        </div>
      </div>

      <Tabs defaultValue="faq">
        <TabsList className="mb-6">
          <TabsTrigger value="faq">
            <HelpCircle className="mr-2 h-4 w-4" /> FAQ
          </TabsTrigger>
          <TabsTrigger value="docs">
            <FileText className="mr-2 h-4 w-4" /> Documentation
          </TabsTrigger>
          <TabsTrigger value="support">
            <MessageSquare className="mr-2 h-4 w-4" /> Contact Support
          </TabsTrigger>
        </TabsList>

        <TabsContent value="faq">
          <div className="space-y-4">
            {faqItems.map((item, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{item.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>{item.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="docs">
          <Card>
            <CardHeader>
              <CardTitle>Documentation</CardTitle>
              <CardDescription>
                Explore our comprehensive documentation to learn more about SpicyChat AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" className="h-auto p-4 justify-start bg-transparent" asChild>
                  <Link href="https://docs.spicychat.ai/getting-started">
                    <div className="flex flex-col items-start">
                      <span className="font-medium">Getting Started</span>
                      <span className="text-sm text-muted-foreground">Learn the basics of SpicyChat AI</span>
                    </div>
                  </Link>
                </Button>
                <Button variant="outline" className="h-auto p-4 justify-start bg-transparent" asChild>
                  <Link href="https://docs.spicychat.ai/creating-chatbots">
                    <div className="flex flex-col items-start">
                      <span className="font-medium">Creating Chatbots</span>
                      <span className="text-sm text-muted-foreground">Learn how to create effective chatbots</span>
                    </div>
                  </Link>
                </Button>
                <Button variant="outline" className="h-auto p-4 justify-start bg-transparent" asChild>
                  <Link href="https://docs.spicychat.ai/api-reference">
                    <div className="flex flex-col items-start">
                      <span className="font-medium">API Reference</span>
                      <span className="text-sm text-muted-foreground">Technical documentation for developers</span>
                    </div>
                  </Link>
                </Button>
                <Button variant="outline" className="h-auto p-4 justify-start bg-transparent" asChild>
                  <Link href="https://docs.spicychat.ai/tutorials">
                    <div className="flex flex-col items-start">
                      <span className="font-medium">Tutorials</span>
                      <span className="text-sm text-muted-foreground">Step-by-step guides for common tasks</span>
                    </div>
                  </Link>
                </Button>
              </div>
              <Button className="w-full" asChild>
                <Link href="https://docs.spicychat.ai">View Full Documentation</Link>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="support">
          <Card>
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>Need help? Our support team is here to assist you.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="subject" className="text-sm font-medium">
                  Subject
                </label>
                <Input id="subject" placeholder="Brief description of your issue" />
              </div>
              <div className="space-y-2">
                <label htmlFor="message" className="text-sm font-medium">
                  Message
                </label>
                <textarea
                  id="message"
                  placeholder="Please describe your issue in detail"
                  className="w-full min-h-[150px] rounded-md border border-input bg-background px-3 py-2 text-sm"
                />
              </div>
              <Button className="w-full">Submit Support Request</Button>
              <div className="text-center text-sm text-muted-foreground">Typical response time: 24-48 hours</div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
