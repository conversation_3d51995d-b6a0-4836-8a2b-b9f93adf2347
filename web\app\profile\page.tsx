"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

export default function ProfilePage() {
  const [username, setUsername] = useState("tommick839")
  const [name, setName] = useState("tommick839")
  const [highlights, setHighlights] = useState(
    "A petite and slim 22 years old girl, with lavender-long hair, silver-gray eyes, and is wearing stylish black glasses. Her skin is snow white, smooth and unblemished, like a princess.",
  )
  const [nsfw, setNsfw] = useState(false)
  const [blurImages, setBlurImages] = useState(false)

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Profile Settings</h1>

      <div className="space-y-6">
        {/* Username */}
        <div className="space-y-2">
          <Label htmlFor="username">
            Username <span className="text-muted-foreground">You can change this at any time.</span>
          </Label>
          <div className="relative">
            <Input id="username" value={username} onChange={(e) => setUsername(e.target.value)} className="pr-8" />
            <Button variant="ghost" size="icon" className="absolute right-1 top-1 h-6 w-6">
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Name */}
        <div className="space-y-2">
          <Label htmlFor="name">
            Name <span className="text-muted-foreground">The name you'll use for chatting.</span>
          </Label>
          <div className="relative">
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} className="pr-8" />
            <Button variant="ghost" size="icon" className="absolute right-1 top-1 h-6 w-6">
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Current Plan */}
        <div className="space-y-2">
          <Label>Current Plan</Label>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-muted">
              Free Tier
            </Badge>
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <span className="text-xs">ⓘ</span>
            </Button>
          </div>
        </div>

        {/* Avatar */}
        <div className="space-y-3">
          <Label>
            Avatar{" "}
            <span className="text-muted-foreground">You can either create an image from text or upload an image.</span>
          </Label>
          <div className="flex gap-2">
            <Button className="bg-blue-600 hover:bg-blue-700">Generate Avatar</Button>
            <span className="text-muted-foreground self-center">or</span>
            <Button variant="outline">Choose File</Button>
            <Button variant="outline" className="text-muted-foreground bg-transparent">
              No File Chosen
            </Button>
          </div>
        </div>

        {/* Highlights */}
        <div className="space-y-2">
          <Label htmlFor="highlights">
            Highlights{" "}
            <span className="text-muted-foreground">
              (Optional) Used only in your conversations to help the AI with context. Keep it short (1-2 sentences).
            </span>
          </Label>
          <Textarea
            id="highlights"
            value={highlights}
            onChange={(e) => setHighlights(e.target.value)}
            className="min-h-[80px]"
          />
        </div>

        {/* NSFW Settings */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="nsfw">Display chatbots with explicit images or languages (NSFW)</Label>
            </div>
            <Switch id="nsfw" checked={nsfw} onCheckedChange={setNsfw} />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="blur">Blur explicit images (NSFW)</Label>
            </div>
            <Switch id="blur" checked={blurImages} onCheckedChange={setBlurImages} />
          </div>
        </div>

        {/* Preferred Chat Language */}
        <div className="space-y-2">
          <Label>Preferred Chat Language</Label>
          <div className="flex items-center gap-2">
            <Badge className="bg-purple-600 text-white">Beta Testing</Badge>
            <Select defaultValue="english">
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="english">English</SelectItem>
                <SelectItem value="spanish">Spanish</SelectItem>
                <SelectItem value="french">French</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <p className="text-sm text-muted-foreground">
            If you enjoy chatting in a language other than English, configuring a chat language guides the AI to respond
            in that language and enables chatbot translations when available. Works best with our premium AI models.
          </p>
        </div>

        {/* Advanced */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Advanced</h3>
            <Button variant="ghost" size="sm">
              <span className="transform rotate-180">▼</span>
            </Button>
          </div>

          <div className="flex justify-between gap-4">
            <Button variant="outline" className="flex-1 bg-transparent">
              Update
            </Button>
            <Button variant="outline" className="flex-1 text-blue-400 bg-transparent">
              Logout
            </Button>
            <Button variant="outline" className="flex-1 text-red-400 bg-transparent">
              Remove Account
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
