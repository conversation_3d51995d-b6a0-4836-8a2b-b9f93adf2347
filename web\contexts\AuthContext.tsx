"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  email?: string;
  display_name?: string;
}

interface AuthContextType {
  user: User | null;
  isGuest: boolean;
  isLoading: boolean;
  signInAsGuest: () => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isGuest, setIsGuest] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 检查本地存储中的用户信息
  useEffect(() => {
    const checkStoredUser = () => {
      try {
        const storedUser = localStorage.getItem('current_user');
        const storedIsGuest = localStorage.getItem('is_guest') === 'true';
        
        if (storedUser) {
          setUser(JSON.parse(storedUser));
          setIsGuest(storedIsGuest);
        }
      } catch (error) {
        console.error('Error loading stored user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkStoredUser();
  }, []);

  const signInAsGuest = async () => {
    setIsLoading(true);
    
    try {
      // 生成或获取设备ID
      let deviceId = localStorage.getItem('guest_device_id');
      if (!deviceId) {
        deviceId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        localStorage.setItem('guest_device_id', deviceId);
      }

      // 调用后端游客登录API
      const response = await fetch('http://localhost:8000/api/auth/guest-login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ guest_id: deviceId }),
      });

      if (!response.ok) {
        throw new Error(`游客登录失败: ${response.status}`);
      }

      const sessionData = await response.json();
      
      // 创建用户对象
      const guestUser: User = {
        id: sessionData.user.id, // 这应该是后端返回的UUID
        email: sessionData.user.email,
        display_name: sessionData.user.display_name || 'Guest User'
      };

      // 保存到状态和本地存储
      setUser(guestUser);
      setIsGuest(true);
      localStorage.setItem('current_user', JSON.stringify(guestUser));
      localStorage.setItem('is_guest', 'true');
      localStorage.setItem('access_token', sessionData.access_token);

      console.log('游客登录成功:', guestUser);
    } catch (error) {
      console.error("游客登录失败:", error);
      // 如果后端登录失败，创建一个符合UUID格式的临时用户ID
      const generateUUID = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c == 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      };
      
      const fallbackUser: User = {
        id: generateUUID(), // 使用UUID格式的ID
        display_name: 'Guest User'
      };
      setUser(fallbackUser);
      setIsGuest(true);
      localStorage.setItem('current_user', JSON.stringify(fallbackUser));
      localStorage.setItem('is_guest', 'true');
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setUser(null);
    setIsGuest(false);
    localStorage.removeItem('current_user');
    localStorage.removeItem('is_guest');
    localStorage.removeItem('access_token');
    localStorage.removeItem('guest_device_id');
  };

  const value = {
    user,
    isGuest,
    isLoading,
    signInAsGuest,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};