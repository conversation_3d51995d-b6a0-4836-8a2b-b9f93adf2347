"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Trophy, ChevronDown, ChevronUp } from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

const topCreators = [
  // 修复：使用UUID格式的模拟数据
  {
    id: "550e8400-e29b-41d4-a716-************",
    username: "scarlett_bloodfallen",
    avatar: "/placeholder.svg?height=80&width=80",
    numBots: 317,
    score: "12683.8k",
    mmRank: "NEW",
    position: 2,
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    username: "yukischi",
    avatar: "/placeholder.svg?height=80&width=80",
    numBots: 197,
    score: "16857.3k",
    mmRank: "NEW",
    position: 1,
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    username: "lowpoly",
    avatar: "/placeholder.svg?height=80&width=80",
    numBots: 308,
    score: "11546.8k",
    mmRank: "NEW",
    position: 3,
  },
]

const newBotsData = [
  { rank: 1, creator: "scarlett_bloodfallen", numBots: 317, score: "12.7m", mmRank: "NEW" },
  { rank: 2, creator: "yukischi", numBots: 197, score: "16.9m", mmRank: "NEW" },
  { rank: 3, creator: "steviejones", numBots: 7, score: "2.9m", mmRank: "NEW" },
  { rank: 4, creator: "palmako", numBots: 126, score: "3.9m", mmRank: "NEW" },
  { rank: 5, creator: "strayblackbird", numBots: 53, score: "3.6m", mmRank: "NEW" },
  { rank: 6, creator: "loanstar72", numBots: 23, score: "3.5m", mmRank: "-5" },
  { rank: 7, creator: "shredder92", numBots: 17, score: "3.3m", mmRank: "NEW" },
  { rank: 8, creator: "alex557", numBots: 3, score: "3.1m", mmRank: "NEW" },
  { rank: 9, creator: "kuuki3013", numBots: 9, score: "3.0m", mmRank: "NEW" },
  { rank: 10, creator: "naylorblaky", numBots: 23, score: "2.8m", mmRank: "NEW" },
  { rank: 11, creator: "loas", numBots: 10, score: "2.7m", mmRank: "-9" },
  { rank: 12, creator: "pro_123476", numBots: 23, score: "2.6m", mmRank: "NEW" },
  { rank: 13, creator: "colyu", numBots: 12, score: "2.3m", mmRank: "NEW" },
  { rank: 14, creator: "h1314", numBots: 32, score: "2.3m", mmRank: "NEW" },
  { rank: 15, creator: "expo1013", numBots: 14, score: "2.3m", mmRank: "-17" },
  { rank: 16, creator: "raidersystem", numBots: 38, score: "2.2m", mmRank: "+5" },
  { rank: 17, creator: "kbqunides", numBots: 13, score: "2.1m", mmRank: "NEW" },
  { rank: 18, creator: "scarlett_bloodfallen", numBots: 14, score: "2.0m", mmRank: "NEW" },
  { rank: 19, creator: "andysuice", numBots: 44, score: "1.9m", mmRank: "-21" },
  { rank: 20, creator: "thekitty", numBots: 14, score: "1.8m", mmRank: "NEW" },
  { rank: 21, creator: "keva8", numBots: 2, score: "1.8m", mmRank: "NEW" },
  { rank: 22, creator: "yuranuratos", numBots: 4, score: "1.7m", mmRank: "-24" },
  { rank: 23, creator: "coatyoto", numBots: 5, score: "1.6m", mmRank: "-25" },
  { rank: 24, creator: "burguesa", numBots: 18, score: "1.4m", mmRank: "NEW" },
  { rank: 25, creator: "bearduden", numBots: 15, score: "1.3m", mmRank: "-27" },
]

const allBotsData = [
  { rank: 1, creator: "yukischi", numBots: 63, score: "7.0m", mmRank: "+18" },
  { rank: 2, creator: "jinmei", numBots: 53, score: "5.2m", mmRank: "-1" },
  { rank: 3, creator: "steviejones", numBots: 7, score: "3.9m", mmRank: "NEW" },
  { rank: 4, creator: "palmako", numBots: 126, score: "2.9m", mmRank: "NEW" },
  { rank: 5, creator: "strayblackbird", numBots: 53, score: "2.8m", mmRank: "NEW" },
  { rank: 6, creator: "therian72", numBots: 23, score: "1.5m", mmRank: "+1" },
  { rank: 7, creator: "shredder92", numBots: 27, score: "3.3m", mmRank: "NEW" },
  { rank: 8, creator: "alex557", numBots: 3, score: "3.1m", mmRank: "NEW" },
  { rank: 9, creator: "kuuki3013", numBots: 9, score: "3.0m", mmRank: "NEW" },
  { rank: 10, creator: "naylorblaky", numBots: 23, score: "2.8m", mmRank: "NEW" },
  { rank: 11, creator: "loas", numBots: 10, score: "2.7m", mmRank: "-9" },
  { rank: 12, creator: "pro_123476", numBots: 23, score: "2.6m", mmRank: "NEW" },
  { rank: 13, creator: "colyu", numBots: 12, score: "2.3m", mmRank: "NEW" },
  { rank: 14, creator: "h1314", numBots: 32, score: "2.3m", mmRank: "NEW" },
  { rank: 15, creator: "expo1013", numBots: 14, score: "2.3m", mmRank: "-17" },
  { rank: 16, creator: "raidersystem", numBots: 38, score: "2.2m", mmRank: "+5" },
  { rank: 17, creator: "kbqunides", numBots: 13, score: "2.1m", mmRank: "NEW" },
  { rank: 18, creator: "scarlett_bloodfallen", numBots: 14, score: "2.0m", mmRank: "NEW" },
  { rank: 19, creator: "andysuice", numBots: 44, score: "1.9m", mmRank: "-21" },
  { rank: 20, creator: "thekitty", numBots: 14, score: "1.8m", mmRank: "NEW" },
  { rank: 21, creator: "keva8", numBots: 2, score: "1.8m", mmRank: "NEW" },
  { rank: 22, creator: "yuranuratos", numBots: 4, score: "1.7m", mmRank: "-24" },
  { rank: 23, creator: "coatyoto", numBots: 5, score: "1.6m", mmRank: "-25" },
  { rank: 24, creator: "burguesa", numBots: 18, score: "1.4m", mmRank: "NEW" },
  { rank: 25, creator: "bearduden", numBots: 15, score: "1.3m", mmRank: "-27" },
]

export default function LeaderboardPage() {
  const [activeTab, setActiveTab] = useState<"new" | "all">("new")
  const [isHowItWorksOpen, setIsHowItWorksOpen] = useState(false)

  const currentData = activeTab === "new" ? newBotsData : allBotsData
  const currentDescription =
    activeTab === "new"
      ? "New Bots - Creators with the most engaging NEW characters created in the previous month"
      : "All Bots - Creators with the most engaging characters in the previous month"

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center gap-2 mb-2">
        <Trophy className="h-6 w-6 text-yellow-500" />
        <h1 className="text-2xl font-bold">Creators Leaderboard</h1>
      </div>

      <div className="text-sm text-muted-foreground mb-4">May 2025</div>

      <p className="text-sm mb-6">
        Welcome to our Creator Showcase page, where we celebrate the ingenuity and creativity of our vibrant community!
      </p>

      {/* How it Works Collapsible Section */}
      <Collapsible open={isHowItWorksOpen} onOpenChange={setIsHowItWorksOpen} className="mb-6">
        <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium hover:text-primary">
          How it Works:
          {isHowItWorksOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-4 space-y-4">
          <div className="text-sm">
            <p className="mb-3">Each month, we feature two categories of creators:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Top 25 creators of newly created characters</li>
              <li>Top 25 creators of all characters</li>
            </ul>
            <p className="mt-3 text-yellow-500">
              *Our ranking system combines various engagement metrics to highlight these standout creators, showcasing
              their dedication and skill in crafting compelling conversational experiences.
            </p>
          </div>

          <div className="space-y-4">
            <h4 className="font-medium">Prizes</h4>
            <div className="flex gap-4">
              <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 p-3 rounded-lg">
                <Trophy className="h-5 w-5 text-yellow-500" />
                <div className="text-sm">
                  <div className="font-medium">Top 3 from each category</div>
                  <div className="text-muted-foreground">Free Month of "I'm All In Tier" Subscription</div>
                </div>
              </div>
              <div className="flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-3 rounded-lg">
                <Trophy className="h-5 w-5 text-blue-500" />
                <div className="text-sm">
                  <div className="font-medium">Remaining Top 7</div>
                  <div className="text-muted-foreground">Free Month of "True Supporter" Subscription</div>
                </div>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              *Note: Users cannot receive prizes for both categories in the same month.
            </p>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Tab Buttons */}
      <div className="flex gap-2 mb-4">
        <Button variant={activeTab === "new" ? "secondary" : "ghost"} size="sm" onClick={() => setActiveTab("new")}>
          New Bots
        </Button>
        <Button variant={activeTab === "all" ? "secondary" : "ghost"} size="sm" onClick={() => setActiveTab("all")}>
          All Bots
        </Button>
      </div>

      <p className="text-sm text-muted-foreground mb-4">{currentDescription}</p>

      <div className="flex items-center gap-2 text-sm mb-6">
        <Trophy className="h-4 w-4 text-yellow-500" />
        <span className="text-yellow-500">Free month of I'm All In Tier subscription</span>
      </div>

      {/* Top 3 Creators Visual */}
      <div className="flex justify-center items-end gap-8 mb-8 relative">
        {/* Laurel decorations */}
        <div className="absolute left-0 top-0 text-6xl text-yellow-500/20">🏆</div>
        <div className="absolute right-0 top-0 text-6xl text-yellow-500/20">🏆</div>

        {/* Left laurel */}
        <div className="absolute left-20 top-10">
          <div className="w-16 h-32 text-yellow-500/40">
            <svg viewBox="0 0 100 200" className="w-full h-full">
              <path
                d="M20 20 Q30 40 25 60 Q35 80 30 100 Q40 120 35 140 Q45 160 40 180"
                stroke="currentColor"
                strokeWidth="3"
                fill="none"
              />
              <path d="M15 30 Q20 35 18 40" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M22 50 Q27 55 25 60" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M18 70 Q23 75 21 80" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M25 90 Q30 95 28 100" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M20 110 Q25 115 23 120" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M27 130 Q32 135 30 140" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M22 150 Q27 155 25 160" stroke="currentColor" strokeWidth="2" fill="none" />
            </svg>
          </div>
        </div>

        {/* Right laurel */}
        <div className="absolute right-20 top-10">
          <div className="w-16 h-32 text-yellow-500/40 scale-x-[-1]">
            <svg viewBox="0 0 100 200" className="w-full h-full">
              <path
                d="M20 20 Q30 40 25 60 Q35 80 30 100 Q40 120 35 140 Q45 160 40 180"
                stroke="currentColor"
                strokeWidth="3"
                fill="none"
              />
              <path d="M15 30 Q20 35 18 40" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M22 50 Q27 55 25 60" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M18 70 Q23 75 21 80" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M25 90 Q30 95 28 100" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M20 110 Q25 115 23 120" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M27 130 Q32 135 30 140" stroke="currentColor" strokeWidth="2" fill="none" />
              <path d="M22 150 Q27 155 25 160" stroke="currentColor" strokeWidth="2" fill="none" />
            </svg>
          </div>
        </div>

        {topCreators.map((creator, index) => (
          <div key={creator.id} className="flex flex-col items-center">
            <div className="relative mb-4">
              <div className="w-24 h-24 relative">
                {/* Hexagonal frame */}
                <div className="absolute inset-0">
                  <svg viewBox="0 0 100 100" className="w-full h-full">
                    <polygon points="50,5 85,25 85,75 50,95 15,75 15,25" fill="none" stroke="#fbbf24" strokeWidth="3" />
                  </svg>
                </div>
                {/* Profile image */}
                <div
                  className="absolute inset-2 overflow-hidden"
                  style={{ clipPath: "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)" }}
                >
                  <img
                    src={creator.avatar || "/placeholder.svg"}
                    alt={creator.username}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              {creator.position === 1 && (
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-sm font-bold border-2 border-background">
                  1
                </div>
              )}
            </div>
            <div className="text-center">
              <div className="font-medium text-orange-400">@{creator.username}</div>
              <div className="text-xs text-muted-foreground">Num bots: {creator.numBots}</div>
              <div className="text-xs text-muted-foreground">Score: {creator.score}</div>
              <div className="text-xs text-muted-foreground">
                M/M Rank: <span className="text-blue-400">{creator.mmRank}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mb-4">
        <Button variant="outline" size="sm">
          May 2025
        </Button>
      </div>

      {/* Leaderboard Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b bg-muted/50">
                <tr className="text-left">
                  <th className="p-4 font-medium">Rank</th>
                  <th className="p-4 font-medium">Creator</th>
                  <th className="p-4 font-medium">Num Bots</th>
                  <th className="p-4 font-medium">Score</th>
                  <th className="p-4 font-medium">M/M Rank ▲</th>
                </tr>
              </thead>
              <tbody>
                {currentData.map((item, index) => (
                  <tr key={item.rank} className={`border-b hover:bg-accent/50 ${index % 2 === 0 ? "bg-muted/20" : ""}`}>
                    <td className="p-4 font-medium">{item.rank}</td>
                    <td className="p-4">
                      <span className="text-orange-400 hover:underline cursor-pointer">{item.creator}</span>
                    </td>
                    <td className="p-4">{item.numBots}</td>
                    <td className="p-4">{item.score}</td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          <span className="text-yellow-500">⭐</span>
                          <span className="text-yellow-500">⭐</span>
                        </div>
                        <span
                          className={`text-sm ${
                            item.mmRank === "NEW"
                              ? "text-blue-400"
                              : item.mmRank.startsWith("+")
                                ? "text-green-400"
                                : item.mmRank.startsWith("-")
                                  ? "text-red-400"
                                  : "text-blue-400"
                          }`}
                        >
                          {item.mmRank}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
