/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/my-chatbots/page";
exports.ids = ["app/my-chatbots/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a87ea7c17e11\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkU6XFxwcm9qZWN0c1xcYWdlbnRcXHNwaWN5Y2hhdC1jbG9uZVxcd2ViXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTg3ZWE3YzE3ZTExXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sidebar */ \"(rsc)/./components/sidebar.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/header */ \"(rsc)/./components/header.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/footer */ \"(rsc)/./components/footer.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"SpicyChat AI\",\n    description: \"AI-powered chat platform\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className)} antialiased bg-black`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"dark\",\n                    enableSystem: false,\n                    disableTransitionOnChange: false,\n                    storageKey: \"spicychat-theme\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-screen bg-black\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                        className: \"flex-1 overflow-auto bg-black\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-h-full flex flex-col bg-black text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBVU1BO0FBUGdCO0FBQ3FDO0FBQ047QUFDWDtBQUNGO0FBQ0E7QUFJakMsTUFBTU0sV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNYQyxXQUFXO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLHdCQUF3QjtrQkFDdEMsNEVBQUNDO1lBQUtDLFdBQVcsR0FBR2hCLDJKQUFlLENBQUMscUJBQXFCLENBQUM7c0JBQ3hELDRFQUFDRSwrREFBWUE7MEJBQ1gsNEVBQUNELHFFQUFhQTtvQkFDWmdCLFdBQVU7b0JBQ1ZDLGNBQWE7b0JBQ2JDLGNBQWM7b0JBQ2RDLDJCQUEyQjtvQkFDM0JDLFlBQVc7OEJBRVgsNEVBQUNDO3dCQUFJTixXQUFVOzswQ0FDYiw4REFBQ2IsMkRBQU9BOzs7OzswQ0FDUiw4REFBQ21CO2dDQUFJTixXQUFVOztrREFDYiw4REFBQ1osMERBQU1BOzs7OztrREFDUCw4REFBQ21CO3dDQUFLUCxXQUFVO2tEQUNkLDRFQUFDTTs0Q0FBSU4sV0FBVTs7OERBQ2IsOERBQUNNO29EQUFJTixXQUFVOzhEQUFVTDs7Ozs7OzhEQUN6Qiw4REFBQ04sMERBQU1BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVM0IiLCJzb3VyY2VzIjpbIkU6XFxwcm9qZWN0c1xcYWdlbnRcXHNwaWN5Y2hhdC1jbG9uZVxcd2ViXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIlxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXJcIlxuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIkAvY29udGV4dHMvQXV0aENvbnRleHRcIlxuaW1wb3J0IFNpZGViYXIgZnJvbSBcIkAvY29tcG9uZW50cy9zaWRlYmFyXCJcbmltcG9ydCBIZWFkZXIgZnJvbSBcIkAvY29tcG9uZW50cy9oZWFkZXJcIlxuaW1wb3J0IEZvb3RlciBmcm9tIFwiQC9jb21wb25lbnRzL2Zvb3RlclwiXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJTcGljeUNoYXQgQUlcIixcbiAgZGVzY3JpcHRpb246IFwiQUktcG93ZXJlZCBjaGF0IHBsYXRmb3JtXCIsXG4gICAgZ2VuZXJhdG9yOiAndjAuZGV2J1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci5jbGFzc05hbWV9IGFudGlhbGlhc2VkIGJnLWJsYWNrYH0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAgPFRoZW1lUHJvdmlkZXJcbiAgICAgICAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgICAgICAgIGRlZmF1bHRUaGVtZT1cImRhcmtcIlxuICAgICAgICAgICAgZW5hYmxlU3lzdGVtPXtmYWxzZX1cbiAgICAgICAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2U9e2ZhbHNlfVxuICAgICAgICAgICAgc3RvcmFnZUtleT1cInNwaWN5Y2hhdC10aGVtZVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLWJsYWNrXCI+XG4gICAgICAgICAgICAgIDxTaWRlYmFyIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPEhlYWRlciAvPlxuICAgICAgICAgICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvIGJnLWJsYWNrXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLWZ1bGwgZmxleCBmbGV4LWNvbCBiZy1ibGFjayB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+e2NoaWxkcmVufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8Rm9vdGVyIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L21haW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9UaGVtZVByb3ZpZGVyPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlRoZW1lUHJvdmlkZXIiLCJBdXRoUHJvdmlkZXIiLCJTaWRlYmFyIiwiSGVhZGVyIiwiRm9vdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZ2VuZXJhdG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiYXR0cmlidXRlIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSIsInN0b3JhZ2VLZXkiLCJkaXYiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiRTpcXHByb2plY3RzXFxhZ2VudFxcc3BpY3ljaGF0LWNsb25lXFx3ZWJcXGFwcFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/my-chatbots/loading.tsx":
/*!*************************************!*\
  !*** ./app/my-chatbots/loading.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbXktY2hhdGJvdHMvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiRTpcXHByb2plY3RzXFxhZ2VudFxcc3BpY3ljaGF0LWNsb25lXFx3ZWJcXGFwcFxcbXktY2hhdGJvdHNcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiBudWxsXG59XG4iXSwibmFtZXMiOlsiTG9hZGluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/my-chatbots/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/my-chatbots/page.tsx":
/*!**********************************!*\
  !*** ./app/my-chatbots/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyChatbotsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_guard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth-guard */ \"(rsc)/./components/auth-guard.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(rsc)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(rsc)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n\n\n\n\n\n\nfunction MyChatbotsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_guard__WEBPACK_IMPORTED_MODULE_1__.AuthGuard, {\n        requireAuth: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black min-h-screen text-white p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"My Chatbots\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex-1 max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search\",\n                                    className: \"pl-9 bg-gray-800 border-gray-700 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                            defaultValue: \"all\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                    className: \"w-32 bg-gray-800 border-gray-700 text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {}, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                    className: \"bg-gray-800 border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                            value: \"all\",\n                                            children: \"All\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                            value: \"public\",\n                                            children: \"Public\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                            value: \"private\",\n                                            children: \"Private\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                            defaultValue: \"latest\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                    className: \"w-32 bg-gray-800 border-gray-700 text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {}, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                    className: \"bg-gray-800 border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                            value: \"latest\",\n                                            children: \"Latest\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                            value: \"oldest\",\n                                            children: \"Oldest\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                            value: \"popular\",\n                                            children: \"Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center py-20 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-medium mb-2 text-white\",\n                            children: \"No Results Found\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6\",\n                            children: \"Create your first bot and start chatting!\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: \"Create Chatbot\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/my-chatbots/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./components/ui/button.tsx\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center h-full p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-7xl font-bold mb-6\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-bold mb-2\",\n                children: \"This page is gone.\"\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-8 text-muted-foreground\",\n                children: \"The page you are looking for does not exist or never existed.\"\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    children: \"Back to home\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTRCO0FBQ21CO0FBRWhDLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQTBCOzs7Ozs7MEJBQ3hDLDhEQUFDRTtnQkFBR0YsV0FBVTswQkFBMEI7Ozs7OzswQkFDeEMsOERBQUNHO2dCQUFFSCxXQUFVOzBCQUFxQzs7Ozs7OzBCQUdsRCw4REFBQ0gseURBQU1BO2dCQUFDTyxPQUFPOzBCQUNiLDRFQUFDUixrREFBSUE7b0JBQUNTLE1BQUs7OEJBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXZCIiwic291cmNlcyI6WyJFOlxccHJvamVjdHNcXGFnZW50XFxzcGljeWNoYXQtY2xvbmVcXHdlYlxcYXBwXFxub3QtZm91bmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbCBwLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTd4bCBmb250LWJvbGQgbWItNlwiPjQwNDwvaDE+XG4gICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIG1iLTJcIj5UaGlzIHBhZ2UgaXMgZ29uZS48L2gyPlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBtYi04IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UgYXJlIGxvb2tpbmcgZm9yIGRvZXMgbm90IGV4aXN0IG9yIG5ldmVyIGV4aXN0ZWQuXG4gICAgICA8L3A+XG4gICAgICA8QnV0dG9uIGFzQ2hpbGQ+XG4gICAgICAgIDxMaW5rIGhyZWY9XCIvXCI+QmFjayB0byBob21lPC9MaW5rPlxuICAgICAgPC9CdXR0b24+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiQnV0dG9uIiwiTm90Rm91bmQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImgyIiwicCIsImFzQ2hpbGQiLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth-guard.tsx":
/*!***********************************!*\
  !*** ./components/auth-guard.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthGuard = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\auth-guard.tsx",
"AuthGuard",
);

/***/ }),

/***/ "(rsc)/./components/footer.tsx":
/*!*******************************!*\
  !*** ./components/footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-black text-white mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-lg\",\n                                        children: [\n                                            \"SPICYCHAT \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-500\",\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                lineNumber: 12,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                        lineNumber: 11,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400 space-y-1 max-w-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"Owned & operated by:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"SpicyChat AI is powered by Spicy Chat Suite, 2024. Montreal, Quebec H3G1A1, Canada\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 17,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"SpicyChat AI is a product of Spicy Chat Suite. Spicy Chat, Spicychat, 2024. Spicychat\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"SpicyChat AI Ltd. 2024 - Montreal, Quebec, 2024. Spicychat\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-12 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-3 text-white\",\n                                            children: \"Resources\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Terms & Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/privacy\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/refunds\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Refund Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/support\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-3 text-white\",\n                                            children: \"Community\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/guidelines\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Community Guidelines\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/affiliates\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Become an Affiliate\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/reporting\",\n                                                    className: \"block hover:text-white transition-colors\",\n                                                    children: \"Report Content\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-3 text-white\",\n                                            children: \"Join Us\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"flex items-center gap-2 hover:text-white transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M13.545 2.907a13.227 13.227 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.19 12.19 0 0 0-3.658 0 8.258 8.258 0 0 0-.412-.833.051.051 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.041.041 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032c.001.014.01.028.021.037a13.276 13.276 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019c.308-.42.582-.863.818-1.329a.05.05 0 0 0-.01-.059.051.051 0 0 0-.018-.011 8.875 8.875 0 0 1-1.248-.595.05.05 0 0 1-.02-.066.051.051 0 0 1 .015-.019c.084-.063.168-.129.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.052.052 0 0 1 .053.007c.08.066.164.132.248.195a.051.051 0 0 1-.004.085 8.254 8.254 0 0 1-1.249.594.05.05 0 0 0-.03.03.052.052 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.235 13.235 0 0 0 4.001-2.02.049.049 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.034.034 0 0 0-.02-.019Zm-8.198 7.307c-.789 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612Zm5.316 0c-.788 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                                lineNumber: 61,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Discord\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"flex items-center gap-2 hover:text-white transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Twitter\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"flex items-center gap-2 hover:text-white transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Reddit\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-6 border-t border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://apps.apple.com/app/spicychat\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white text-black px-3 py-1 rounded text-xs font-medium\",\n                                            children: \"App Store\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://play.google.com/store/apps/details?id=ai.spicychat\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white text-black px-3 py-1 rounded text-xs font-medium\",\n                                            children: \"Android\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"18 U.S.C. 2257 Record-Keeping Requirements Compliance Statement\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\sidebar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJFOlxccHJvamVjdHNcXGFnZW50XFxzcGljeWNoYXQtY2xvbmVcXHdlYlxcY29tcG9uZW50c1xcdWlcXGlucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBSZWFjdC5Db21wb25lbnRQcm9wczxcImlucHV0XCI+PihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LWJhc2UgcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gZmlsZTp0ZXh0LWZvcmVncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/select.tsx":
/*!**********************************!*\
  !*** ./components/ui/select.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Select: () => (/* binding */ Select),
/* harmony export */   SelectContent: () => (/* binding */ SelectContent),
/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),
/* harmony export */   SelectItem: () => (/* binding */ SelectItem),
/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),
/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),
/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),
/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),
/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),
/* harmony export */   SelectValue: () => (/* binding */ SelectValue)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Select = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"Select",
);const SelectGroup = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectGroup() from the server but SelectGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectGroup",
);const SelectValue = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectValue() from the server but SelectValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectValue",
);const SelectTrigger = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectTrigger() from the server but SelectTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectTrigger",
);const SelectContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectContent() from the server but SelectContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectContent",
);const SelectLabel = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectLabel() from the server but SelectLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectLabel",
);const SelectItem = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectItem() from the server but SelectItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectItem",
);const SelectSeparator = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectSeparator() from the server but SelectSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectSeparator",
);const SelectScrollUpButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectScrollUpButton() from the server but SelectScrollUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectScrollUpButton",
);const SelectScrollDownButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SelectScrollDownButton() from the server but SelectScrollDownButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\components\\ui\\select.tsx",
"SelectScrollDownButton",
);

/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\projects\\agent\\spicychat-clone\\web\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJFOlxccHJvamVjdHNcXGFnZW50XFxzcGljeWNoYXQtY2xvbmVcXHdlYlxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmy-chatbots%2Fpage&page=%2Fmy-chatbots%2Fpage&appPaths=%2Fmy-chatbots%2Fpage&pagePath=private-next-app-dir%2Fmy-chatbots%2Fpage.tsx&appDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmy-chatbots%2Fpage&page=%2Fmy-chatbots%2Fpage&appPaths=%2Fmy-chatbots%2Fpage&pagePath=private-next-app-dir%2Fmy-chatbots%2Fpage.tsx&appDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/my-chatbots/loading.tsx */ \"(rsc)/./app/my-chatbots/loading.tsx\"));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/my-chatbots/page.tsx */ \"(rsc)/./app/my-chatbots/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'my-chatbots',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module5, \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\layout.tsx\"],\n'loading': [module1, \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\loading.tsx\"],\n'not-found': [module2, \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\app\\\\my-chatbots\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/my-chatbots/page\",\n        pathname: \"/my-chatbots\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmy-chatbots%2Fpage&page=%2Fmy-chatbots%2Fpage&appPaths=%2Fmy-chatbots%2Fpage&pagePath=private-next-app-dir%2Fmy-chatbots%2Fpage.tsx&appDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/header.tsx */ \"(rsc)/./components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sidebar.tsx */ \"(rsc)/./components/sidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(rsc)/./contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cauth-guard.tsx%22%2C%22ids%22%3A%5B%22AuthGuard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cui%5C%5Cselect.tsx%22%2C%22ids%22%3A%5B%22Select%22%2C%22SelectTrigger%22%2C%22SelectValue%22%2C%22SelectContent%22%2C%22SelectItem%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cauth-guard.tsx%22%2C%22ids%22%3A%5B%22AuthGuard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cui%5C%5Cselect.tsx%22%2C%22ids%22%3A%5B%22Select%22%2C%22SelectTrigger%22%2C%22SelectValue%22%2C%22SelectContent%22%2C%22SelectItem%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth-guard.tsx */ \"(rsc)/./components/auth-guard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/select.tsx */ \"(rsc)/./components/ui/select.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDY29tcG9uZW50cyU1QyU1Q2F1dGgtZ3VhcmQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aEd1YXJkJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc2VsZWN0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlbGVjdCUyMiUyQyUyMlNlbGVjdFRyaWdnZXIlMjIlMkMlMjJTZWxlY3RWYWx1ZSUyMiUyQyUyMlNlbGVjdENvbnRlbnQlMjIlMkMlMjJTZWxlY3RJdGVtJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBeUk7QUFDekk7QUFDQSxnS0FBaU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhHdWFyZFwiXSAqLyBcIkU6XFxcXHByb2plY3RzXFxcXGFnZW50XFxcXHNwaWN5Y2hhdC1jbG9uZVxcXFx3ZWJcXFxcY29tcG9uZW50c1xcXFxhdXRoLWd1YXJkLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2VsZWN0XCIsXCJTZWxlY3RUcmlnZ2VyXCIsXCJTZWxlY3RWYWx1ZVwiLFwiU2VsZWN0Q29udGVudFwiLFwiU2VsZWN0SXRlbVwiXSAqLyBcIkU6XFxcXHByb2plY3RzXFxcXGFnZW50XFxcXHNwaWN5Y2hhdC1jbG9uZVxcXFx3ZWJcXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzZWxlY3QudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cauth-guard.tsx%22%2C%22ids%22%3A%5B%22AuthGuard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cui%5C%5Cselect.tsx%22%2C%22ids%22%3A%5B%22Select%22%2C%22SelectTrigger%22%2C%22SelectValue%22%2C%22SelectContent%22%2C%22SelectItem%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBNEsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCJFOlxcXFxwcm9qZWN0c1xcXFxhZ2VudFxcXFxzcGljeWNoYXQtY2xvbmVcXFxcd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/auth-guard.tsx":
/*!***********************************!*\
  !*** ./components/auth-guard.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AuthGuard auto */ \n\n\n\n\nfunction AuthGuard({ children, requireAuth = false, fallback }) {\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // 模拟用户登录状态 - 在实际应用中这应该来自认证上下文\n    const isAuthenticated = false // 暂时设为false来测试游客模式\n    ;\n    if (requireAuth && !isAuthenticated) {\n        if (fallback) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-4 text-white\",\n                    children: \"Sign In Required\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 mb-6\",\n                    children: \"You need to sign in to access this feature.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: ()=>setShowLoginModal(true),\n                    className: \"bg-blue-600 hover:bg-blue-700\",\n                    children: \"Sign In\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                    open: showLoginModal,\n                    onOpenChange: setShowLoginModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                        className: \"bg-gray-900 border-gray-700 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                        className: \"text-gray-400\",\n                                        children: \"Enter your email, and we'll send a code to your inbox. No need for passwords — like magic!\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                className: \"w-full mt-1 p-3 bg-gray-800 border border-gray-600 rounded-md text-white\",\n                                                placeholder: \"Enter your email\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                        children: \"Continue\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400\",\n                                        children: \"OR\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"G\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Continue with Google\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83C\\uDF4E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Continue with Apple\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"X\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Continue with X\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83C\\uDFAE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Continue with Discord\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\auth-guard.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2F1dGgtZ3VhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUkyQztBQUNJO0FBQzZEO0FBQzVFO0FBUXpCLFNBQVNRLFVBQVUsRUFBRUMsUUFBUSxFQUFFQyxjQUFjLEtBQUssRUFBRUMsUUFBUSxFQUFrQjtJQUNuRixNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU1PLFNBQVNkLDBEQUFTQTtJQUV4Qiw4QkFBOEI7SUFDOUIsTUFBTWUsa0JBQWtCLE1BQU0sbUJBQW1COztJQUVqRCxJQUFJTCxlQUFlLENBQUNLLGlCQUFpQjtRQUNuQyxJQUFJSixVQUFVO1lBQ1oscUJBQU87MEJBQUdBOztRQUNaO1FBRUEscUJBQ0UsOERBQUNLO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBR0QsV0FBVTs4QkFBcUM7Ozs7Ozs4QkFDbkQsOERBQUNFO29CQUFFRixXQUFVOzhCQUFxQjs7Ozs7OzhCQUNsQyw4REFBQ2hCLHlEQUFNQTtvQkFBQ21CLFNBQVMsSUFBTVAsa0JBQWtCO29CQUFPSSxXQUFVOzhCQUFnQzs7Ozs7OzhCQUkxRiw4REFBQ2YseURBQU1BO29CQUFDbUIsTUFBTVQ7b0JBQWdCVSxjQUFjVDs4QkFDMUMsNEVBQUNWLGdFQUFhQTt3QkFBQ2MsV0FBVTs7MENBQ3ZCLDhEQUFDWiwrREFBWUE7O2tEQUNYLDhEQUFDQyw4REFBV0E7a0RBQUM7Ozs7OztrREFDYiw4REFBQ0Ysb0VBQWlCQTt3Q0FBQ2EsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FJL0MsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDTztnREFBTU4sV0FBVTswREFBaUM7Ozs7OzswREFDbEQsOERBQUNPO2dEQUNDQyxNQUFLO2dEQUNMUixXQUFVO2dEQUNWUyxhQUFZOzs7Ozs7Ozs7Ozs7a0RBR2hCLDhEQUFDekIseURBQU1BO3dDQUFDZ0IsV0FBVTtrREFBdUM7Ozs7OztrREFDekQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUE0Qjs7Ozs7O2tEQUMzQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDaEIseURBQU1BO2dEQUFDMEIsU0FBUTtnREFBVVYsV0FBVTs7a0VBQ2xDLDhEQUFDVzt3REFBS1gsV0FBVTtrRUFBTzs7Ozs7O29EQUFROzs7Ozs7OzBEQUVqQyw4REFBQ2hCLHlEQUFNQTtnREFBQzBCLFNBQVE7Z0RBQVVWLFdBQVU7O2tFQUNsQyw4REFBQ1c7d0RBQUtYLFdBQVU7a0VBQU87Ozs7OztvREFBUzs7Ozs7OzswREFFbEMsOERBQUNoQix5REFBTUE7Z0RBQUMwQixTQUFRO2dEQUFVVixXQUFVOztrRUFDbEMsOERBQUNXO3dEQUFLWCxXQUFVO2tFQUFPOzs7Ozs7b0RBQVE7Ozs7Ozs7MERBRWpDLDhEQUFDaEIseURBQU1BO2dEQUFDMEIsU0FBUTtnREFBVVYsV0FBVTs7a0VBQ2xDLDhEQUFDVzt3REFBS1gsV0FBVTtrRUFBTzs7Ozs7O29EQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFRaEQ7SUFFQSxxQkFBTztrQkFBR1I7O0FBQ1oiLCJzb3VyY2VzIjpbIkU6XFxwcm9qZWN0c1xcYWdlbnRcXHNwaWN5Y2hhdC1jbG9uZVxcd2ViXFxjb21wb25lbnRzXFxhdXRoLWd1YXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgdHlwZSBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nRGVzY3JpcHRpb24sIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiXG5cbmludGVyZmFjZSBBdXRoR3VhcmRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgcmVxdWlyZUF1dGg/OiBib29sZWFuXG4gIGZhbGxiYWNrPzogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoR3VhcmQoeyBjaGlsZHJlbiwgcmVxdWlyZUF1dGggPSBmYWxzZSwgZmFsbGJhY2sgfTogQXV0aEd1YXJkUHJvcHMpIHtcbiAgY29uc3QgW3Nob3dMb2dpbk1vZGFsLCBzZXRTaG93TG9naW5Nb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICAvLyDmqKHmi5/nlKjmiLfnmbvlvZXnirbmgIEgLSDlnKjlrp7pmYXlupTnlKjkuK3ov5nlupTor6XmnaXoh6rorqTor4HkuIrkuIvmlodcbiAgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gZmFsc2UgLy8g5pqC5pe26K6+5Li6ZmFsc2XmnaXmtYvor5XmuLjlrqLmqKHlvI9cblxuICBpZiAocmVxdWlyZUF1dGggJiYgIWlzQXV0aGVudGljYXRlZCkge1xuICAgIGlmIChmYWxsYmFjaykge1xuICAgICAgcmV0dXJuIDw+e2ZhbGxiYWNrfTwvPlxuICAgIH1cblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbCBwLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBtYi00IHRleHQtd2hpdGVcIj5TaWduIEluIFJlcXVpcmVkPC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBtYi02XCI+WW91IG5lZWQgdG8gc2lnbiBpbiB0byBhY2Nlc3MgdGhpcyBmZWF0dXJlLjwvcD5cbiAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TG9naW5Nb2RhbCh0cnVlKX0gY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDBcIj5cbiAgICAgICAgICBTaWduIEluXG4gICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgIDxEaWFsb2cgb3Blbj17c2hvd0xvZ2luTW9kYWx9IG9uT3BlbkNoYW5nZT17c2V0U2hvd0xvZ2luTW9kYWx9PlxuICAgICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlci1ncmF5LTcwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICA8RGlhbG9nVGl0bGU+U2lnbiBJbjwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgRW50ZXIgeW91ciBlbWFpbCwgYW5kIHdlJ2xsIHNlbmQgYSBjb2RlIHRvIHlvdXIgaW5ib3guIE5vIG5lZWQgZm9yIHBhc3N3b3JkcyDigJQgbGlrZSBtYWdpYyFcbiAgICAgICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+RW1haWw8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC0xIHAtMyBiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgZW1haWxcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMFwiPkNvbnRpbnVlPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1ncmF5LTQwMFwiPk9SPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTgwMCBib3JkZXItZ3JheS02MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPkc8L3NwYW4+IENvbnRpbnVlIHdpdGggR29vZ2xlXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTgwMCBib3JkZXItZ3JheS02MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPvCfjY48L3NwYW4+IENvbnRpbnVlIHdpdGggQXBwbGVcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktODAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yXCI+WDwvc3Bhbj4gQ29udGludWUgd2l0aCBYXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTgwMCBib3JkZXItZ3JheS02MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPvCfjq48L3NwYW4+IENvbnRpbnVlIHdpdGggRGlzY29yZFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgPC9EaWFsb2c+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59XG4iXSwibmFtZXMiOlsidXNlUm91dGVyIiwiQnV0dG9uIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJ1c2VTdGF0ZSIsIkF1dGhHdWFyZCIsImNoaWxkcmVuIiwicmVxdWlyZUF1dGgiLCJmYWxsYmFjayIsInNob3dMb2dpbk1vZGFsIiwic2V0U2hvd0xvZ2luTW9kYWwiLCJyb3V0ZXIiLCJpc0F1dGhlbnRpY2F0ZWQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJvbkNsaWNrIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJ2YXJpYW50Iiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/auth-guard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Header() {\n    const { setTheme, theme, resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Header.useEffect\"], []);\n    const toggleTheme = ()=>{\n        setTheme(resolvedTheme === \"dark\" ? \"light\" : \"dark\");\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"h-14 border-b bg-background flex items-center justify-end px-4 gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-sm font-bold\",\n                                        children: \"T\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                            align: \"end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/profile\",\n                                        children: \"Profile Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                    children: \"Account\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-14 border-b bg-background flex items-center justify-end px-4 gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                onClick: toggleTheme,\n                className: \"relative overflow-hidden\",\n                children: [\n                    resolvedTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-5 w-5 transition-all duration-300 ease-in-out\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-5 w-5 transition-all duration-300 ease-in-out\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Toggle theme\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-sm font-bold\",\n                                    children: \"T\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                        align: \"end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/profile\",\n                                    children: \"Profile Settings\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                children: \"Account\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                children: \"Sign Out\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\header.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DiscIcon,Github,Heart,HelpCircle,Home,Loader2,Lock,Menu,MessageSquare,Package,Plus,Shield,ThumbsUp,Trophy,Twitter,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/disc.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoggingIn, setIsLoggingIn] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 使用真实的认证状态\n    const { user, isGuest, isLoading, signInAsGuest, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const isAuthenticated = !!user;\n    const isActive = (path)=>{\n        return pathname === path || pathname?.startsWith(path + \"/\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const handleProtectedRoute = (e, path)=>{\n        if (!isAuthenticated && (path.includes(\"persona\") || path.includes(\"chatbot\") || path.includes(\"favorite\"))) {\n            e.preventDefault();\n            setShowLoginModal(true);\n        }\n    };\n    const handleGuestLogin = async ()=>{\n        setIsLoggingIn(true);\n        try {\n            await signInAsGuest();\n            setShowLoginModal(false);\n        } catch (error) {\n            console.error('Guest login failed:', error);\n        } finally{\n            setIsLoggingIn(false);\n        }\n    };\n    const NavLink = ({ href, icon: Icon, children, requireAuth = false })=>{\n        const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center gap-3 px-3 py-2 rounded-md ${isActive(href) ? \"bg-accent\" : \"hover:bg-accent/50\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        children,\n                        requireAuth && !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 14,\n                            className: \"text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 49\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n        if (requireAuth && !isAuthenticated) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cursor-pointer\",\n                onClick: (e)=>handleProtectedRoute(e, href),\n                title: isCollapsed ? `${children} (Login Required)` : \"\",\n                children: content\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: href,\n            title: isCollapsed ? children?.toString() : \"\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${isCollapsed ? \"w-16\" : \"w-64\"} h-screen bg-background border-r flex flex-col transition-all duration-300`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"mr-2\",\n                                onClick: toggleSidebar,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold text-lg\",\n                                children: [\n                                    \"SPICYCHAT \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-500\",\n                                        children: \"AI\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-auto py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-1 px-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/chats\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    children: \"Chats\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/personas\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    requireAuth: true,\n                                    children: \"My Personas\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 pb-2 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm text-muted-foreground\",\n                                        children: \"Chatbots\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/chatbot/create\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    requireAuth: true,\n                                    children: \"Create Chatbot\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/my-chatbots\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    requireAuth: true,\n                                    children: \"My Chatbots\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/favorite-bots\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                    requireAuth: true,\n                                    children: \"Favorites\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/recommended-bots\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                    children: \"Recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/creators/leaderboard\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                    children: \"Leaderboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/blocked-creators\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                    requireAuth: true,\n                                    children: \"Blocked Creators\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 pb-2\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/subscribe\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                    children: \"Subscribe\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: \"/help\",\n                                    icon: _barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                    children: \"Help\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t\",\n                        children: [\n                            !isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                        onClick: handleGuestLogin,\n                                        disabled: isLoggingIn,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `flex items-center justify-center gap-2 ${isCollapsed ? \"px-0\" : \"\"}`,\n                                            children: [\n                                                isLoggingIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"lucide lucide-log-in\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"10 17 15 12 10 7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"15\",\n                                                            x2: \"3\",\n                                                            y1: \"12\",\n                                                            y2: \"12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !isCollapsed && (isLoggingIn ? \"Signing In...\" : \"Guest Login\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"w-full bg-transparent\",\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/sign-in\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Full Sign In\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 text-center\",\n                                        children: isGuest ? \"Guest User\" : user?.display_name || user?.email || \"User\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"w-full bg-transparent\",\n                                        variant: \"outline\",\n                                        onClick: signOut,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `flex items-center justify-center gap-2 ${isCollapsed ? \"px-0\" : \"\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                !isCollapsed && \"Sign Out\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-between text-xs text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"hover:underline\",\n                                                    children: \"Terms\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/privacy\",\n                                                    className: \"hover:underline\",\n                                                    children: \"Privacy\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/refunds\",\n                                                    className: \"hover:underline\",\n                                                    children: \"Refunds\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex justify-between text-xs text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/reporting\",\n                                                    className: \"hover:underline\",\n                                                    children: \"Reporting\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/guidelines\",\n                                                    className: \"hover:underline\",\n                                                    children: \"Guidelines\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex justify-between text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/support\",\n                                                        className: \"hover:underline\",\n                                                        children: \"Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/affiliates\",\n                                                        className: \"hover:underline\",\n                                                        children: \"Affiliates\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"#\",\n                                                        \"aria-label\": \"Twitter\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"#\",\n                                                        \"aria-label\": \"GitHub\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"#\",\n                                                        \"aria-label\": \"Discord\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex items-center justify-between text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"https://apps.apple.com/app/spicychat\",\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        className: \"lucide lucide-apple\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 2c1 .5 2 2 2 5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"App Store\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"https://play.google.com/store/apps/details?id=ai.spicychat\",\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        className: \"lucide lucide-android-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M18 4h1a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 2h4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M5 11h14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12 18.5v.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Android\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: showLoginModal,\n                onOpenChange: setShowLoginModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                    className: \"bg-gray-900 border-gray-700 text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                    children: \"Sign In Required\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogDescription, {\n                                    className: \"text-gray-400\",\n                                    children: \"You need to sign in to access this feature.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                    onClick: handleGuestLogin,\n                                    disabled: isLoggingIn,\n                                    children: isLoggingIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DiscIcon_Github_Heart_HelpCircle_Home_Loader2_Lock_Menu_MessageSquare_Package_Plus_Shield_ThumbsUp_Trophy_Twitter_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Signing in as Guest...\"\n                                        ]\n                                    }, void 0, true) : \"Continue as Guest\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-400\",\n                                    children: \"OR\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full mt-1 p-3 bg-gray-800 border border-gray-600 rounded-md text-white\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"w-full bg-gray-700 hover:bg-gray-600\",\n                                    children: \"Continue with Email\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"G\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Continue with Google\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"\\uD83C\\uDF4E\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Continue with Apple\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/sign-up\",\n                                        className: \"text-blue-400 hover:underline text-sm\",\n                                        children: \"Don't have an account? Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\sidebar.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNpRTtBQUcxRCxTQUFTQSxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkU6XFxwcm9qZWN0c1xcYWdlbnRcXHNwaWN5Y2hhdC1jbG9uZVxcd2ViXFxjb21wb25lbnRzXFx0aGVtZS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB0eXBlIHsgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2RpYWxvZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEI7QUFDMkI7QUFFekI7QUFFaEMsTUFBTUcsU0FBU0Ysd0RBQW9CO0FBRW5DLE1BQU1JLGdCQUFnQkosMkRBQXVCO0FBRTdDLE1BQU1NLGVBQWVOLDBEQUFzQjtBQUUzQyxNQUFNUSxjQUFjUix5REFBcUI7QUFFekMsTUFBTVUsOEJBQWdCWCw2Q0FBZ0IsQ0FHcEMsQ0FBQyxFQUFFYSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNkLDJEQUF1QjtRQUN0QmMsS0FBS0E7UUFDTEYsV0FBV1gsOENBQUVBLENBQ1gsMkpBQ0FXO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILGNBQWNNLFdBQVcsR0FBR2hCLDJEQUF1QixDQUFDZ0IsV0FBVztBQUUvRCxNQUFNQyw4QkFBZ0JsQiw2Q0FBZ0IsQ0FHcEMsQ0FBQyxFQUFFYSxTQUFTLEVBQUVNLFFBQVEsRUFBRSxHQUFHTCxPQUFPLEVBQUVDLG9CQUNwQyw4REFBQ1I7OzBCQUNDLDhEQUFDSTs7Ozs7MEJBQ0QsOERBQUNWLDJEQUF1QjtnQkFDdEJjLEtBQUtBO2dCQUNMRixXQUFXWCw4Q0FBRUEsQ0FDWCwrZkFDQVc7Z0JBRUQsR0FBR0MsS0FBSzswQkFFUks7Ozs7Ozs7Ozs7OztBQUlQRCxjQUFjRCxXQUFXLEdBQUdoQiwyREFBdUIsQ0FBQ2dCLFdBQVc7QUFFL0QsTUFBTUksZUFBZSxDQUFDLEVBQUVSLFNBQVMsRUFBRSxHQUFHQyxPQUE2QyxpQkFDakYsOERBQUNRO1FBQUlULFdBQVdYLDhDQUFFQSxDQUFDLHNEQUFzRFc7UUFBYSxHQUFHQyxLQUFLOzs7Ozs7QUFFaEdPLGFBQWFKLFdBQVcsR0FBRztBQUUzQixNQUFNTSxlQUFlLENBQUMsRUFBRVYsU0FBUyxFQUFFLEdBQUdDLE9BQTZDLGlCQUNqRiw4REFBQ1E7UUFBSVQsV0FBV1gsOENBQUVBLENBQUMsaUVBQWlFVztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUUzR1MsYUFBYU4sV0FBVyxHQUFHO0FBRTNCLE1BQU1PLDRCQUFjeEIsNkNBQWdCLENBR2xDLENBQUMsRUFBRWEsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDZCx5REFBcUI7UUFDcEJjLEtBQUtBO1FBQ0xGLFdBQVdYLDhDQUFFQSxDQUFDLHFEQUFxRFc7UUFDbEUsR0FBR0MsS0FBSzs7Ozs7O0FBR2JVLFlBQVlQLFdBQVcsR0FBR2hCLHlEQUFxQixDQUFDZ0IsV0FBVztBQUUzRCxNQUFNUyxrQ0FBb0IxQiw2Q0FBZ0IsQ0FHeEMsQ0FBQyxFQUFFYSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNkLCtEQUEyQjtRQUFDYyxLQUFLQTtRQUFLRixXQUFXWCw4Q0FBRUEsQ0FBQyxpQ0FBaUNXO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRTdHWSxrQkFBa0JULFdBQVcsR0FBR2hCLCtEQUEyQixDQUFDZ0IsV0FBVztBQWF0RSIsInNvdXJjZXMiOlsiRTpcXHByb2plY3RzXFxhZ2VudFxcc3BpY3ljaGF0LWNsb25lXFx3ZWJcXGNvbXBvbmVudHNcXHVpXFxkaWFsb2cudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBEaWFsb2dQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1kaWFsb2dcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IERpYWxvZyA9IERpYWxvZ1ByaW1pdGl2ZS5Sb290XG5cbmNvbnN0IERpYWxvZ1RyaWdnZXIgPSBEaWFsb2dQcmltaXRpdmUuVHJpZ2dlclxuXG5jb25zdCBEaWFsb2dQb3J0YWwgPSBEaWFsb2dQcmltaXRpdmUuUG9ydGFsXG5cbmNvbnN0IERpYWxvZ0Nsb3NlID0gRGlhbG9nUHJpbWl0aXZlLkNsb3NlXG5cbmNvbnN0IERpYWxvZ092ZXJsYXkgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBEaWFsb2dQcmltaXRpdmUuT3ZlcmxheT4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRGlhbG9nUHJpbWl0aXZlLk92ZXJsYXk+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEaWFsb2dQcmltaXRpdmUuT3ZlcmxheVxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcImZpeGVkIGluc2V0LTAgei01MCBiZy1ibGFjay84MCAgZGF0YS1bc3RhdGU9b3Blbl06YW5pbWF0ZS1pbiBkYXRhLVtzdGF0ZT1jbG9zZWRdOmFuaW1hdGUtb3V0IGRhdGEtW3N0YXRlPWNsb3NlZF06ZmFkZS1vdXQtMCBkYXRhLVtzdGF0ZT1vcGVuXTpmYWRlLWluLTBcIixcbiAgICAgIGNsYXNzTmFtZSxcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5EaWFsb2dPdmVybGF5LmRpc3BsYXlOYW1lID0gRGlhbG9nUHJpbWl0aXZlLk92ZXJsYXkuZGlzcGxheU5hbWVcblxuY29uc3QgRGlhbG9nQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERpYWxvZ1ByaW1pdGl2ZS5Db250ZW50PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEaWFsb2dQcmltaXRpdmUuQ29udGVudD5cbj4oKHsgY2xhc3NOYW1lLCBjaGlsZHJlbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEaWFsb2dQb3J0YWw+XG4gICAgPERpYWxvZ092ZXJsYXkgLz5cbiAgICA8RGlhbG9nUHJpbWl0aXZlLkNvbnRlbnRcbiAgICAgIHJlZj17cmVmfVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmaXhlZCBsZWZ0LVs1MCVdIHRvcC1bNTAlXSB6LTUwIGdyaWQgdy1mdWxsIG1heC13LWxnIHRyYW5zbGF0ZS14LVstNTAlXSB0cmFuc2xhdGUteS1bLTUwJV0gZ2FwLTQgYm9yZGVyIGJnLWJhY2tncm91bmQgcC02IHNoYWRvdy1sZyBkdXJhdGlvbi0yMDAgZGF0YS1bc3RhdGU9b3Blbl06YW5pbWF0ZS1pbiBkYXRhLVtzdGF0ZT1jbG9zZWRdOmFuaW1hdGUtb3V0IGRhdGEtW3N0YXRlPWNsb3NlZF06ZmFkZS1vdXQtMCBkYXRhLVtzdGF0ZT1vcGVuXTpmYWRlLWluLTAgZGF0YS1bc3RhdGU9Y2xvc2VkXTp6b29tLW91dC05NSBkYXRhLVtzdGF0ZT1vcGVuXTp6b29tLWluLTk1IGRhdGEtW3N0YXRlPWNsb3NlZF06c2xpZGUtb3V0LXRvLWxlZnQtMS8yIGRhdGEtW3N0YXRlPWNsb3NlZF06c2xpZGUtb3V0LXRvLXRvcC1bNDglXSBkYXRhLVtzdGF0ZT1vcGVuXTpzbGlkZS1pbi1mcm9tLWxlZnQtMS8yIGRhdGEtW3N0YXRlPW9wZW5dOnNsaWRlLWluLWZyb20tdG9wLVs0OCVdIHNtOnJvdW5kZWQtbGdcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9EaWFsb2dQcmltaXRpdmUuQ29udGVudD5cbiAgPC9EaWFsb2dQb3J0YWw+XG4pKVxuRGlhbG9nQ29udGVudC5kaXNwbGF5TmFtZSA9IERpYWxvZ1ByaW1pdGl2ZS5Db250ZW50LmRpc3BsYXlOYW1lXG5cbmNvbnN0IERpYWxvZ0hlYWRlciA9ICh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfTogUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+KSA9PiAoXG4gIDxkaXYgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgdGV4dC1jZW50ZXIgc206dGV4dC1sZWZ0XCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbilcbkRpYWxvZ0hlYWRlci5kaXNwbGF5TmFtZSA9IFwiRGlhbG9nSGVhZGVyXCJcblxuY29uc3QgRGlhbG9nRm9vdGVyID0gKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4pID0+IChcbiAgPGRpdiBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbC1yZXZlcnNlIHNtOmZsZXgtcm93IHNtOmp1c3RpZnktZW5kIHNtOnNwYWNlLXgtMlwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pXG5EaWFsb2dGb290ZXIuZGlzcGxheU5hbWUgPSBcIkRpYWxvZ0Zvb3RlclwiXG5cbmNvbnN0IERpYWxvZ1RpdGxlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRGlhbG9nUHJpbWl0aXZlLlRpdGxlPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEaWFsb2dQcmltaXRpdmUuVGl0bGU+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEaWFsb2dQcmltaXRpdmUuVGl0bGVcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwidGV4dC1sZyBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5EaWFsb2dUaXRsZS5kaXNwbGF5TmFtZSA9IERpYWxvZ1ByaW1pdGl2ZS5UaXRsZS5kaXNwbGF5TmFtZVxuXG5jb25zdCBEaWFsb2dEZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERpYWxvZ1ByaW1pdGl2ZS5EZXNjcmlwdGlvbj4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRGlhbG9nUHJpbWl0aXZlLkRlc2NyaXB0aW9uPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RGlhbG9nUHJpbWl0aXZlLkRlc2NyaXB0aW9uIHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkRpYWxvZ0Rlc2NyaXB0aW9uLmRpc3BsYXlOYW1lID0gRGlhbG9nUHJpbWl0aXZlLkRlc2NyaXB0aW9uLmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7XG4gIERpYWxvZyxcbiAgRGlhbG9nUG9ydGFsLFxuICBEaWFsb2dPdmVybGF5LFxuICBEaWFsb2dDbG9zZSxcbiAgRGlhbG9nVHJpZ2dlcixcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dGb290ZXIsXG4gIERpYWxvZ1RpdGxlLFxuICBEaWFsb2dEZXNjcmlwdGlvbixcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkRpYWxvZ1ByaW1pdGl2ZSIsImNuIiwiRGlhbG9nIiwiUm9vdCIsIkRpYWxvZ1RyaWdnZXIiLCJUcmlnZ2VyIiwiRGlhbG9nUG9ydGFsIiwiUG9ydGFsIiwiRGlhbG9nQ2xvc2UiLCJDbG9zZSIsIkRpYWxvZ092ZXJsYXkiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJPdmVybGF5IiwiZGlzcGxheU5hbWUiLCJEaWFsb2dDb250ZW50IiwiY2hpbGRyZW4iLCJDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwiZGl2IiwiRGlhbG9nRm9vdGVyIiwiRGlhbG9nVGl0bGUiLCJUaXRsZSIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGVzY3JpcHRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto\"\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/select.tsx":
/*!**********************************!*\
  !*** ./components/ui/select.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectGroup,SelectValue,SelectTrigger,SelectContent,SelectLabel,SelectItem,SelectSeparator,SelectScrollUpButton,SelectScrollDownButton auto */ \n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 28,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined));\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 44,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 58,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, position = \"popper\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 68,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 115,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 121,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 130,\n        columnNumber: 3\n    }, undefined));\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGuest, setIsGuest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 检查本地存储中的用户信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkStoredUser = {\n                \"AuthProvider.useEffect.checkStoredUser\": ()=>{\n                    try {\n                        const storedUser = localStorage.getItem('current_user');\n                        const storedIsGuest = localStorage.getItem('is_guest') === 'true';\n                        if (storedUser) {\n                            setUser(JSON.parse(storedUser));\n                            setIsGuest(storedIsGuest);\n                        }\n                    } catch (error) {\n                        console.error('Error loading stored user:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkStoredUser\"];\n            checkStoredUser();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signInAsGuest = async ()=>{\n        setIsLoading(true);\n        try {\n            // 生成或获取设备ID\n            let deviceId = localStorage.getItem('guest_device_id');\n            if (!deviceId) {\n                deviceId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n                localStorage.setItem('guest_device_id', deviceId);\n            }\n            // 调用后端游客登录API\n            const response = await fetch('http://localhost:8000/api/auth/guest-login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    guest_id: deviceId\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`游客登录失败: ${response.status}`);\n            }\n            const sessionData = await response.json();\n            // 创建用户对象\n            const guestUser = {\n                id: sessionData.user.id,\n                email: sessionData.user.email,\n                display_name: sessionData.user.display_name || 'Guest User'\n            };\n            // 保存到状态和本地存储\n            setUser(guestUser);\n            setIsGuest(true);\n            localStorage.setItem('current_user', JSON.stringify(guestUser));\n            localStorage.setItem('is_guest', 'true');\n            localStorage.setItem('access_token', sessionData.access_token);\n            console.log('游客登录成功:', guestUser);\n        } catch (error) {\n            console.error(\"游客登录失败:\", error);\n            // 如果后端登录失败，创建一个符合UUID格式的临时用户ID\n            const generateUUID = ()=>{\n                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n                    const r = Math.random() * 16 | 0;\n                    const v = c == 'x' ? r : r & 0x3 | 0x8;\n                    return v.toString(16);\n                });\n            };\n            const fallbackUser = {\n                id: generateUUID(),\n                display_name: 'Guest User'\n            };\n            setUser(fallbackUser);\n            setIsGuest(true);\n            localStorage.setItem('current_user', JSON.stringify(fallbackUser));\n            localStorage.setItem('is_guest', 'true');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setUser(null);\n        setIsGuest(false);\n        localStorage.removeItem('current_user');\n        localStorage.removeItem('is_guest');\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('guest_device_id');\n    };\n    const value = {\n        user,\n        isGuest,\n        isLoading,\n        signInAsGuest,\n        signOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\projects\\\\agent\\\\spicychat-clone\\\\web\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 127,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJFOlxccHJvamVjdHNcXGFnZW50XFxzcGljeWNoYXQtY2xvbmVcXHdlYlxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/header.tsx */ \"(ssr)/./components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sidebar.tsx */ \"(ssr)/./components/sidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDYWdlbnQlNUMlNUNzcGljeWNoYXQtY2xvbmUlNUMlNUN3ZWIlNUMlNUNjb21wb25lbnRzJTVDJTVDaGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDYWdlbnQlNUMlNUNzcGljeWNoYXQtY2xvbmUlNUMlNUN3ZWIlNUMlNUNjb21wb25lbnRzJTVDJTVDc2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDY29tcG9uZW50cyU1QyU1Q3RoZW1lLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDYWdlbnQlNUMlNUNzcGljeWNoYXQtY2xvbmUlNUMlNUN3ZWIlNUMlNUNjb250ZXh0cyU1QyU1Q0F1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDcHJvamVjdHMlNUMlNUNhZ2VudCU1QyU1Q3NwaWN5Y2hhdC1jbG9uZSU1QyU1Q3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDcHJvamVjdHMlNUMlNUNhZ2VudCU1QyU1Q3NwaWN5Y2hhdC1jbG9uZSU1QyU1Q3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQW1JO0FBQ25JO0FBQ0EsNEpBQW9JO0FBQ3BJO0FBQ0EsMEtBQWlKO0FBQ2pKO0FBQ0EsZ0tBQTJJO0FBQzNJO0FBQ0EsZ05BQTRLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRTpcXFxccHJvamVjdHNcXFxcYWdlbnRcXFxcc3BpY3ljaGF0LWNsb25lXFxcXHdlYlxcXFxjb21wb25lbnRzXFxcXGhlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJFOlxcXFxwcm9qZWN0c1xcXFxhZ2VudFxcXFxzcGljeWNoYXQtY2xvbmVcXFxcd2ViXFxcXGNvbXBvbmVudHNcXFxcc2lkZWJhci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxwcm9qZWN0c1xcXFxhZ2VudFxcXFxzcGljeWNoYXQtY2xvbmVcXFxcd2ViXFxcXGNvbXBvbmVudHNcXFxcdGhlbWUtcHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxwcm9qZWN0c1xcXFxhZ2VudFxcXFxzcGljeWNoYXQtY2xvbmVcXFxcd2ViXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkU6XFxcXHByb2plY3RzXFxcXGFnZW50XFxcXHNwaWN5Y2hhdC1jbG9uZVxcXFx3ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cauth-guard.tsx%22%2C%22ids%22%3A%5B%22AuthGuard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cui%5C%5Cselect.tsx%22%2C%22ids%22%3A%5B%22Select%22%2C%22SelectTrigger%22%2C%22SelectValue%22%2C%22SelectContent%22%2C%22SelectItem%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cauth-guard.tsx%22%2C%22ids%22%3A%5B%22AuthGuard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cui%5C%5Cselect.tsx%22%2C%22ids%22%3A%5B%22Select%22%2C%22SelectTrigger%22%2C%22SelectValue%22%2C%22SelectContent%22%2C%22SelectItem%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth-guard.tsx */ \"(ssr)/./components/auth-guard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/select.tsx */ \"(ssr)/./components/ui/select.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDY29tcG9uZW50cyU1QyU1Q2F1dGgtZ3VhcmQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aEd1YXJkJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc2VsZWN0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlbGVjdCUyMiUyQyUyMlNlbGVjdFRyaWdnZXIlMjIlMkMlMjJTZWxlY3RWYWx1ZSUyMiUyQyUyMlNlbGVjdENvbnRlbnQlMjIlMkMlMjJTZWxlY3RJdGVtJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBeUk7QUFDekk7QUFDQSxnS0FBaU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhHdWFyZFwiXSAqLyBcIkU6XFxcXHByb2plY3RzXFxcXGFnZW50XFxcXHNwaWN5Y2hhdC1jbG9uZVxcXFx3ZWJcXFxcY29tcG9uZW50c1xcXFxhdXRoLWd1YXJkLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2VsZWN0XCIsXCJTZWxlY3RUcmlnZ2VyXCIsXCJTZWxlY3RWYWx1ZVwiLFwiU2VsZWN0Q29udGVudFwiLFwiU2VsZWN0SXRlbVwiXSAqLyBcIkU6XFxcXHByb2plY3RzXFxcXGFnZW50XFxcXHNwaWN5Y2hhdC1jbG9uZVxcXFx3ZWJcXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzZWxlY3QudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cauth-guard.tsx%22%2C%22ids%22%3A%5B%22AuthGuard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Ccomponents%5C%5Cui%5C%5Cselect.tsx%22%2C%22ids%22%3A%5B%22Select%22%2C%22SelectTrigger%22%2C%22SelectValue%22%2C%22SelectContent%22%2C%22SelectItem%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2FnZW50JTVDJTVDc3BpY3ljaGF0LWNsb25lJTVDJTVDd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBNEsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCJFOlxcXFxwcm9qZWN0c1xcXFxhZ2VudFxcXFxzcGljeWNoYXQtY2xvbmVcXFxcd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cprojects%5C%5Cagent%5C%5Cspicychat-clone%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/@floating-ui","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmy-chatbots%2Fpage&page=%2Fmy-chatbots%2Fpage&appPaths=%2Fmy-chatbots%2Fpage&pagePath=private-next-app-dir%2Fmy-chatbots%2Fpage.tsx&appDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cprojects%5Cagent%5Cspicychat-clone%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();