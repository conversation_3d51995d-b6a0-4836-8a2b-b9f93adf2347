"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, X, ChevronDown } from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

const monthlyPlans = [
  {
    name: "Get a Taste",
    price: "$5.00",
    period: "/month",
    features: [
      "Plus Benefits From Free Tier",
      "No Ads",
      "Skip The Waiting Lines",
      "Memory Manager",
      "User Personas - Up to 10",
    ],
    buttonText: "Subscribe",
    buttonVariant: "outline" as const,
  },
  {
    name: "True Supporter",
    price: "$14.95",
    period: "/month",
    features: [
      "Plus Benefits From Get A Taste Tier",
      "8K Context (Memory)",
      "Semantic Memory 2.0",
      "Longer Responses",
      "Conversation Images",
      "Access To Additional Models",
      "User Personas - Up to 50",
    ],
    buttonText: "Subscribe",
    buttonVariant: "default" as const,
    popular: true,
  },
  {
    name: "I'm All In",
    price: "$24.95",
    period: "/month",
    features: [
      "Plus Benefits From True Supporter Tier",
      "16K Context (Memory)",
      "Priority Generation Queue",
      "Access To SpicyXL And Advanced Models",
      "Conversation Images On Private Chatbots",
      "User Personas - Up to 100",
      "Text-To-Speech (TTS) For AI Responses",
    ],
    buttonText: "Subscribe",
    buttonVariant: "outline" as const,
  },
]

const yearlyPlans = [
  {
    name: "Get a Taste",
    price: "$50.00",
    period: "/year",
    features: [
      "Plus Benefits From Free Tier",
      "No Ads",
      "Skip The Waiting Lines",
      "Memory Manager",
      "User Personas - Up to 10",
    ],
    buttonText: "Subscribe",
    buttonVariant: "outline" as const,
  },
  {
    name: "True Supporter",
    price: "$149.50",
    period: "/year",
    features: [
      "Plus Benefits From Get A Taste Tier",
      "8K Context (Memory)",
      "Semantic Memory 2.0",
      "Longer Responses",
      "Conversation Images",
      "Access To Additional Models",
      "User Personas - Up to 50",
    ],
    buttonText: "Subscribe",
    buttonVariant: "default" as const,
    popular: true,
  },
  {
    name: "I'm All In",
    price: "$249.50",
    period: "/year",
    features: [
      "Plus Benefits From True Supporter Tier",
      "16K Context (Memory)",
      "Priority Generation Queue",
      "Access To SpicyXL And Advanced Models",
      "Conversation Images On Private Chatbots",
      "User Personas - Up to 100",
      "Text-To-Speech (TTS) For AI Responses",
    ],
    buttonText: "Subscribe",
    buttonVariant: "outline" as const,
  },
]

const featureComparison = [
  { name: "Unlimited Messages", free: true, taste: true, supporter: true, allIn: true },
  { name: "Unlimited Character Creation", free: true, taste: true, supporter: true, allIn: true },
  { name: "User Personas", free: "3", taste: "10", supporter: "50", allIn: "100" },
  { name: "4K Context (Memory)", free: true, taste: true, supporter: true, allIn: true },
  { name: "No Ads", free: false, taste: true, supporter: true, allIn: true },
  { name: "Skip the Waiting Lines", free: false, taste: true, supporter: true, allIn: true },
  { name: "Memory Manager", free: false, taste: true, supporter: true, allIn: true },
  { name: "8K Context (Memory)", free: false, taste: false, supporter: true, allIn: true },
  { name: "Semantic Memory 2.0", free: false, taste: false, supporter: true, allIn: true },
  { name: "Longer Responses", free: false, taste: false, supporter: true, allIn: true },
  { name: "Conversation Images", free: false, taste: false, supporter: true, allIn: true },
  { name: "Access to additional Models", free: false, taste: false, supporter: true, allIn: true },
  { name: "16K Context (Memory)", free: false, taste: false, supporter: false, allIn: true },
  { name: "Priority Generation Queue", free: false, taste: false, supporter: false, allIn: true },
  { name: "Access to SpicyXL and advanced models", free: false, taste: false, supporter: false, allIn: true },
  { name: "Conversation Images on private Chatbots", free: false, taste: false, supporter: false, allIn: true },
  { name: "Text-To-Speech (TTS) for AI Responses", free: false, taste: false, supporter: false, allIn: true },
]

const faqItems = [
  {
    question: "Can I upgrade or downgrade my plan?",
    answer:
      "Yes, you can upgrade or downgrade your subscription plan at any time. Changes will be reflected in your next billing cycle. If you upgrade, you'll be charged the prorated difference immediately.",
  },
  {
    question: "How do I cancel my plan?",
    answer:
      "You can cancel your subscription at any time by going to your account settings and clicking 'Cancel Subscription'. Your subscription will remain active until the end of your current billing period.",
  },
  {
    question: "Can I cancel my subscription and still retain plan benefits until the end of the current billing cycle?",
    answer:
      "Yes, when you cancel your subscription, you will continue to have access to all premium features until the end of your current billing period. After that, your account will revert to the free tier.",
  },
  {
    question: "How do I get a refund?",
    answer:
      "Refunds are available within 7 days of purchase for annual subscriptions and within 24 hours for monthly subscriptions. Please contact our support team with your subscription details to process a refund.",
  },
  {
    question: "I have more questions.",
    answer:
      "If you have additional questions not covered here, please don't hesitate to contact our support team through the Help section or email us directly. We're here to help!",
  },
  {
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and various regional payment methods. You can also subscribe through platforms like SubscribeStar or Boosty.",
  },
  {
    question: "Is my payment information secure?",
    answer:
      "Yes, we use industry-standard encryption and secure payment processors to protect your payment information. We never store your credit card details on our servers.",
  },
  {
    question: "Can I pause my subscription?",
    answer:
      "Currently, we don't offer a pause feature. However, you can cancel your subscription and resubscribe at any time. Your chat history and created characters will be preserved.",
  },
  {
    question: "Do you offer student discounts?",
    answer:
      "We occasionally offer promotional discounts for students and other groups. Follow our social media channels or subscribe to our newsletter to stay updated on special offers.",
  },
  {
    question: "What happens to my data if I cancel?",
    answer:
      "Your account data, including chat history and created characters, will be preserved for 90 days after cancellation. After this period, some data may be permanently deleted according to our data retention policy.",
  },
]

export default function SubscribePage() {
  const [isYearly, setIsYearly] = useState(false)
  const plans = isYearly ? yearlyPlans : monthlyPlans

  return (
    <div className="max-w-6xl mx-auto py-8 px-6">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold mb-4 text-white">Pick the plan that's right for you</h1>
        <p className="text-lg text-gray-400 mb-8">
          Upgrade your experience and unlock your imagination with premium features
        </p>

        <div className="flex items-center justify-center gap-4 mb-8">
          <Button variant={isYearly ? "default" : "outline"} size="sm" onClick={() => setIsYearly(true)}>
            Pay Annually <Badge className="ml-2 bg-green-600">Save 17%</Badge>
          </Button>
          <Button variant={!isYearly ? "default" : "outline"} size="sm" onClick={() => setIsYearly(false)}>
            Pay Monthly
          </Button>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {plans.map((plan) => (
          <Card
            key={plan.name}
            className={`relative bg-gray-900 border-gray-700 ${plan.popular ? "border-blue-500" : ""}`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                <Badge className="bg-blue-600">Most Popular</Badge>
              </div>
            )}
            <CardHeader className="text-center">
              <CardTitle className="text-xl text-white">{plan.name}</CardTitle>
              <div className="flex items-end justify-center gap-1">
                <span className="text-3xl font-bold text-white">{plan.price}</span>
                <span className="text-gray-400">{plan.period}</span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                className={`w-full ${plan.buttonVariant === "default" ? "bg-blue-600 hover:bg-blue-700" : "border-gray-600 hover:bg-gray-800"}`}
                variant={plan.buttonVariant}
              >
                {plan.buttonText}
              </Button>

              <div>
                <h4 className="font-medium mb-2 text-white">Features Included:</h4>
                <ul className="space-y-1">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-start text-sm">
                      <Check className="mr-2 h-4 w-4 text-green-500 shrink-0 mt-0.5" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <Button variant="link" className="w-full text-gray-400 hover:text-white">
                Learn More about {plan.name} Tier
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Alternative Payment Methods */}
      <div className="text-center mb-12">
        <p className="text-gray-400 mb-4">
          You can also subscribe to a Premium plan through platforms like SubscribeStar or Boosty, offering flexible
          options to suit your needs.
        </p>
        <div className="flex justify-center gap-4">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center mb-2">
              <span className="text-white font-bold">S</span>
            </div>
            <span className="text-sm text-gray-400">Subscribe Star</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-2">
              <span className="text-white font-bold">B</span>
            </div>
            <span className="text-sm text-gray-400">Boosty</span>
          </div>
        </div>
      </div>

      {/* FAQs */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-center mb-8 text-white">FAQs</h2>
        <div className="max-w-4xl mx-auto space-y-2">
          {faqItems.map((item) => (
            <Collapsible key={item.question}>
              <CollapsibleTrigger className="w-full p-4 bg-gray-900 rounded-lg text-left hover:bg-gray-800 transition-colors flex items-center justify-between text-white">
                <span>{item.question}</span>
                <ChevronDown className="h-4 w-4" />
              </CollapsibleTrigger>
              <CollapsibleContent className="p-4 bg-gray-800 rounded-b-lg">
                <p className="text-gray-300">{item.answer}</p>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </div>

      {/* Feature Comparison */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-center mb-8 text-white">Compare Spicychat features side-by-side</h2>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-gray-900 rounded-lg overflow-hidden">
            <thead>
              <tr className="border-b border-gray-700 bg-gray-800">
                <th className="text-left p-4 font-medium text-white">Feature</th>
                <th className="text-center p-4 font-medium text-white">
                  <div>Free</div>
                  <div className="text-sm font-normal text-gray-400">$0.00/month</div>
                </th>
                <th className="text-center p-4 font-medium text-white">
                  <div>Get A Taste</div>
                  <div className="text-sm font-normal text-gray-400">{isYearly ? "$50.00/year" : "$5.00/month"}</div>
                </th>
                <th className="text-center p-4 font-medium bg-blue-600/20 text-white">
                  <div className="flex items-center justify-center gap-2">
                    True Supporter
                    <Badge className="bg-blue-600">Most Popular</Badge>
                  </div>
                  <div className="text-sm font-normal text-gray-400">{isYearly ? "$149.50/year" : "$14.95/month"}</div>
                </th>
                <th className="text-center p-4 font-medium text-white">
                  <div>I'm All In</div>
                  <div className="text-sm font-normal text-gray-400">{isYearly ? "$249.50/year" : "$24.95/month"}</div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-700">
                <td className="p-4 flex items-center gap-2 text-white">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>Included with Plan</span>
                </td>
                <td className="text-center p-4">
                  <Button variant="outline" size="sm" className="border-gray-600 hover:bg-gray-800 bg-transparent">
                    Continue With Free
                  </Button>
                </td>
                <td className="text-center p-4">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Subscribe
                  </Button>
                </td>
                <td className="text-center p-4 bg-blue-600/20">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Subscribe
                  </Button>
                </td>
                <td className="text-center p-4">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Subscribe
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-700">
                <td className="p-4 flex items-center gap-2 text-white">
                  <X className="h-4 w-4 text-red-500" />
                  <span>Not included with Plan</span>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
              {featureComparison.map((feature, index) => (
                <tr
                  key={feature.name}
                  className={`border-b border-gray-700 ${index % 2 === 0 ? "bg-gray-800/50" : ""}`}
                >
                  <td className="p-4 font-medium text-white">{feature.name}</td>
                  <td className="text-center p-4">
                    {typeof feature.free === "boolean" ? (
                      feature.free ? (
                        <Check className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      )
                    ) : (
                      <span className="text-sm text-gray-300">{feature.free}</span>
                    )}
                  </td>
                  <td className="text-center p-4">
                    {typeof feature.taste === "boolean" ? (
                      feature.taste ? (
                        <Check className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      )
                    ) : (
                      <span className="text-sm text-gray-300">{feature.taste}</span>
                    )}
                  </td>
                  <td className="text-center p-4 bg-blue-600/20">
                    {typeof feature.supporter === "boolean" ? (
                      feature.supporter ? (
                        <Check className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      )
                    ) : (
                      <span className="text-sm text-gray-300">{feature.supporter}</span>
                    )}
                  </td>
                  <td className="text-center p-4">
                    {typeof feature.allIn === "boolean" ? (
                      feature.allIn ? (
                        <Check className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      )
                    ) : (
                      <span className="text-sm text-gray-300">{feature.allIn}</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
