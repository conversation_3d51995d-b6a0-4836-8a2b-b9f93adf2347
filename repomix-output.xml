This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: .history, *.md, frontend/build, backend/project_storage, docs/images, frontend/src/components/home/<USER>/src/lib/home.tsx, todo/, backend/logs, frontend/dist, tools/redis/, android/app/.cxx/, macos/, windows/, linux/, ios/, web/, backend/dist/, backend/hostAgentAPI/service/server/test_image.py, backend/slide_agent/README.md, src/tokenizers/llama3.json, src/tokenizers/claude.json, public/lib/pdf.worker.min.mjs, public/, analysis/
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.cursorignore
.gitignore
.metadata
analysis_options.yaml
backend/.gitignore
backend/.prettierrc
backend/create_env.py
backend/initial_schema.sql
backend/package.json
backend/requirements.txt
backend/scripts/__init__.py
backend/scripts/agent_generator.py
backend/scripts/ensure_test_user.py
backend/scripts/novel_compiler.py
backend/src/__init__.py
backend/src/data/archetypes.json
backend/src/prompt_templates.py
backend/src/pydantic_models.py
backend/src/services/__init__.py
backend/src/services/chat_service.py
backend/src/services/imagekit_simple.py
backend/src/services/llm_service.py
backend/src/services/prompt_assembler.py
backend/src/services/summarization_service.py
backend/src/services/supabase_service.py
backend/src/supabase_main.py
backend/src/utils/__init__.py
backend/src/utils/user_management.py
devtools_options.yaml
novel_compiler.bat
novel/test.txt
pubspec.lock
pubspec.yaml
role_import.bat
role/import_config.json
role/role_import.py
setup.bat
start.bat
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".cursorignore">
backend\venv
</file>

<file path=".gitignore">
# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
</file>

<file path=".metadata">
# This file tracks properties of this Flutter project.
# Used by Flutter tool to assess capabilities and perform upgrades etc.
#
# This file should be version controlled and should not be manually edited.

version:
  revision: "ea121f8859e4b13e47a8f845e4586164519588bc"
  channel: "stable"

project_type: app

# Tracks metadata for the flutter migrate command
migration:
  platforms:
    - platform: root
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc
    - platform: android
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc
    - platform: ios
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc
    - platform: linux
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc
    - platform: macos
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc
    - platform: web
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc
    - platform: windows
      create_revision: ea121f8859e4b13e47a8f845e4586164519588bc
      base_revision: ea121f8859e4b13e47a8f845e4586164519588bc

  # User provided section

  # List of Local paths (relative to this file) that should be
  # ignored by the migrate tool.
  #
  # Files that are not part of the templates will be ignored by default.
  unmanaged_files:
    - 'lib/main.dart'
    - 'ios/Runner.xcodeproj/project.pbxproj'
</file>

<file path="analysis_options.yaml">
# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
</file>

<file path="backend/.gitignore">
node_modules
# Keep environment variables out of version control
.env

/generated/prisma
</file>

<file path="backend/.prettierrc">
{
  "singleQuote": true,
  "trailingComma": "all"
}
</file>

<file path="backend/create_env.py">
#!/usr/bin/env python3
# 不要随便删除此文件。它用于创建.env文件。
"""
创建.env文件的脚本
"""
import os

def create_env_file():
    """创建.env配置文件"""
    env_content = """# --- Supabase Database ---
SUPABASE_URL="https://aupddjrnljttqxmilymn.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1cGRkanJubGp0dHF4bWlseW1uIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MjQ4NTQsImV4cCI6MjA2ODMwMDg1NH0.vbR6hebyWQZ7YxeGoocR7ceFFRNWZvoONWXvrPz7TP0"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1cGRkanJubGp0dHF4bWlseW1uIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjcyNDg1NCwiZXhwIjoyMDY4MzAwODU0fQ.21vFBEMPfgZA0Mc-x2BsKg7br4h2-_lQE4M70nX2--s"

# --- ImageKit 图片存储服务 ---
IMAGEKIT_ID="uj9lnjyd82"
IMAGEKIT_URL_ENDPOINT="https://ik.imagekit.io/uj9lnjyd82"
IMAGEKIT_PUBLIC_KEY="public_MB//KTinw6AOpfpjxV5f33lXjjE="
IMAGEKIT_PRIVATE_KEY="private_L9NAklnV4lu8sWD56vUI2JYu+XE="

# --- Google Gemini AI (多个API密钥用于轮询) ---
GEMINI_API_KEYS="AIzaSyAHQq4CWqhzdUkK_ZCtvEAbNU-2pKdgSqs,AIzaSyDfIZ8JHtswOGGrEospEXq0G-_lCo2p_Is,AIzaSyCmFX2KUZpEY-_aonLIc0JvMHnnDKpKk24,AIzaSyBV1VtK__aN7ZM1KNnkNXQ5GkAHOVbWk0A,AIzaSyCIf5ZoWt8Pftc24KM-aasS8JUgsj3eCPQ,AIzaSyAI9b917EyA4cyYK-cU2QDK4xmC6bKVJGU,AIzaSyBZv4OxeYu57d0AFUqGmkQKe3rq1WL5iK0,AIzaSyBI6XTacDeodfJj0W-pbDTmzSMhSpuChBw,AIzaSyBJmxwFr7JY-YxQgD2jYKFTDQfSVwmAF9U,AIzaSyDDV8-NOXH9u9mcHzPQl6FBIdDblHQYDwU"

# --- Gemini 模型配置 ---
GEMINI_CHAT_MODEL="gemini-2.5-flash"
GEMINI_GEN_STORY_MODEL="gemini-2.5-pro"
GEMINI_GEN_ROLE_MODEL="gemini-2.5-pro"
GEMINI_GEN_IMAGE_MODEL="gemini-2.0-flash-preview-image-generation"
GEMINI_CHECK_IMAGE_MODEL="gemini-2.5-flash"
GEMINI_CHECK_TASK_MODEL="gemini-2.5-flash"
GEMINI_TTS_MODEL="gemini-2.5-flash-preview-tts"
GEMINI_EMBEDDING_MODEL="gemini-embedding-001"

# --- DOUBAO API KEYS---
DOUBAO_API_KEYS="cdd4f8ed-c1dd-44c3-b49a-39731062e198"

# --- Doubao 模型配置 ---
DOUBAO_GEN_IMAGE_MODEL="doubao-seedream-3-0-t2i-250415"
DOUBAO_EDIT_IMAGE_MODEL="doubao-seededit-3-0-i2i-250628"

# --- Python 进程配置 ---
PYTHON_PATH="python"
"""

    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env文件创建成功！")
    print(f"📁 位置: {os.path.abspath('.env')}")
    print("🗄️ 已配置Supabase数据库连接:")
    print("   - URL: https://aupddjrnljttqxmilymn.supabase.co")
    print("   - 匿名密钥和服务密钥已配置")
    print("🖼️ 已配置ImageKit图片存储服务:")
    print("   - URL: https://ik.imagekit.io/uj9lnjyd82")
    print("   - 公钥和私钥已配置")
    print("🔑 已配置5个Gemini API密钥用于轮询")
    print("🤖 已配置最新的Gemini模型:")
    print(f"   - 聊天模型: {os.getenv('GEMINI_CHAT_MODEL', 'gemini-2.5-flash')}")
    print(f"   - 故事生成模型: {os.getenv('GEMINI_GEN_STORY_MODEL', 'gemini-2.5-pro')}")
    print(f"   - 角色生成模型: {os.getenv('GEMINI_GEN_ROLE_MODEL', 'gemini-2.5-pro')}")
    print(f"   - 图像生成模型: {os.getenv('GEMINI_GEN_IMAGE_MODEL', 'gemini-2.0-flash-preview-image-generation')}")
    print(f"   - 图像审核模型: {os.getenv('GEMINI_CHECK_IMAGE_MODEL', 'gemini-2.5-flash')}")
    print(f"   - 任务检查模型: {os.getenv('GEMINI_CHECK_TASK_MODEL', 'gemini-2.5-flash')}")
    print(f"   - TTS模型: {os.getenv('GEMINI_TTS_MODEL', 'gemini-2.5-flash-preview-tts')}")
    print(f"   - Embedding模型: {os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-004')}")

if __name__ == "__main__":
    create_env_file()
</file>

<file path="backend/initial_schema.sql">
-- =================================================================
-- DANGER: THIS SCRIPT DELETES ALL YOUR DATA AND RESETS THE SCHEMA
-- =================================================================

-- Part 1: Clean up existing objects
-- -----------------------------------------------------------------

-- 1. Drop the trigger on the system table first to remove dependency
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 2. Drop all public tables. CASCADE handles dependencies like foreign keys and indexes.
DROP TABLE IF EXISTS public.agent_memories CASCADE;
DROP TABLE IF EXISTS public.agent_world_infos CASCADE;
DROP TABLE IF EXISTS public.world_info_entries CASCADE;
DROP TABLE IF EXISTS public.world_infos CASCADE;
DROP TABLE IF EXISTS public.chat_summaries CASCADE;
DROP TABLE IF EXISTS public.messages CASCADE;
DROP TABLE IF EXISTS public.chat_participants CASCADE;
DROP TABLE IF EXISTS public.chats CASCADE;
DROP TABLE IF EXISTS public.story_agents CASCADE;
DROP TABLE IF EXISTS public.story_chapters CASCADE;
DROP TABLE IF EXISTS public.stories CASCADE;
DROP TABLE IF EXISTS public.agent_mode_configs CASCADE;
DROP TABLE IF EXISTS public.agents CASCADE;
DROP TABLE IF EXISTS public.user_profiles CASCADE;

-- 3. Drop all functions. Now they have no dependencies.
DROP FUNCTION IF EXISTS public.increment_view_count(UUID);
DROP FUNCTION IF EXISTS public.increment_dialogue_count(UUID);
DROP FUNCTION IF EXISTS public.create_user_profile_on_signup();
DROP FUNCTION IF EXISTS public.update_updated_at_column();
DROP FUNCTION IF EXISTS public.get_public_agents_with_creator(INT);
DROP FUNCTION IF EXISTS public.get_user_chat_list(UUID, INT);
DROP FUNCTION IF EXISTS public.health_check();
DROP FUNCTION IF EXISTS public.match_chat_memories(UUID, vector, INT, FLOAT);
DROP FUNCTION IF EXISTS public.get_active_world_info_entries(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_agent_with_mode_config(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_story_rankings(text);
DROP FUNCTION IF EXISTS public.get_agent_rankings(text);


-- Part 2: Recreate the entire schema
-- -----------------------------------------------------------------

-- 星恋AI V5.0 - 数据库初始化脚本 (统一消息流版)
-- 本脚本为重构后的全新设计，将所有互动统一为消息流模型。
-- 本脚本可安全重复执行。

-- ========================================
-- 1. 基础配置
-- ========================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 启用向量扩展，为RAG记忆系统做准备
CREATE EXTENSION IF NOT EXISTS vector;

-- ========================================
-- 2. 核心表：用户与AI智能体 (模板)
-- ========================================

-- 用户档案表 (与 auth.users 关联)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    is_system_user BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.user_profiles IS '存储用户的公开档案信息';

-- 智能体 (角色) 表 - 精炼版：消除冗余，明确职责
CREATE TABLE IF NOT EXISTS public.agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

    -- =================================================
    -- I. 核心规范化字段 (为性能和通用显示而缓存)
    -- =================================================
    name VARCHAR(100) NOT NULL,
    description TEXT,       -- 核心简介 (对应V2的data.description)
    personality TEXT,       -- 核心性格 (对应V2的data.personality)
    scenario TEXT,          -- 核心场景 (对应V2的data.scenario)
    first_mes TEXT,         -- 核心开场白 (统一字段，废弃opening_line)
    mes_example TEXT,       -- 核心对话示例 (对应V2的data.mes_example)

    -- =================================================
    -- II. 应用特定字段 (星恋AI的特色功能)
    -- =================================================
    image_url TEXT,         -- 主形象图/立绘 (高清，用于详情页)
    avatar_url TEXT,        -- 聊天头像 (小尺寸，用于聊天界面)
    voice_name VARCHAR(50) DEFAULT 'Kore' NOT NULL,
    tags JSONB DEFAULT '[]'::jsonb,
    is_public BOOLEAN DEFAULT true,
    gender VARCHAR(10),     -- 用于语音选择和称谓

    -- =================================================
    -- III. 高级提示词工程字段 (兼容TavernAI高级设定)
    -- =================================================
    creator_notes TEXT,     -- 创作者备注
    system_prompt TEXT,     -- 系统级提示词
    post_history_instructions TEXT, -- 历史后指令

    -- =================================================
    -- IV. 故事模式专用字段
    -- =================================================
    backstory_text TEXT,    -- 角色背景故事 (故事模式中的隐藏信息)
    image_generation_prompt TEXT, -- 角色形象图生成的prompt (用于追溯和优化)

    -- =================================================
    -- V. 原始数据与元数据 (未来兼容性的核心)
    -- =================================================
    "data" JSONB,           -- 存储完整的原始角色卡JSON，作为"单一事实来源"
    spec VARCHAR(20) DEFAULT 'chara_card_v2' NOT NULL, -- 原始卡片格式
    spec_version VARCHAR(10) DEFAULT '2.0' NOT NULL,   -- 原始卡片版本

    -- =================================================
    -- VI. 统计与系统字段
    -- =================================================
    view_count INTEGER DEFAULT 0 NOT NULL,
    dialogue_count INTEGER DEFAULT 0 NOT NULL,
    popularity INTEGER DEFAULT 0 NOT NULL,
    interaction_mode VARCHAR(50) DEFAULT 'roleplay' NOT NULL,
    is_system_agent BOOLEAN DEFAULT false NOT NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.agents IS '存储所有可交互的AI角色，是互动故事和聊天的基础';
COMMENT ON COLUMN public.agents.backstory_text IS '角色的个人背景故事，用于故事中可供玩家探索的隐藏信息';

-- 角色模式特定设定表 (解决场景设定冲突)
CREATE TABLE IF NOT EXISTS public.agent_mode_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    mode VARCHAR(20) NOT NULL CHECK (mode IN ('chat', 'story')),

    -- 模式特定的开场白和场景设定
    mode_specific_first_mes TEXT,    -- 该模式下的专用开场白
    mode_specific_scenario TEXT,     -- 该模式下的专用场景设定
    mode_specific_instructions TEXT, -- 该模式下的专用指令

    -- 是否启用某些功能
    enable_mes_example BOOLEAN DEFAULT true,  -- 是否在该模式下使用对话示例
    enable_backstory BOOLEAN DEFAULT false,   -- 是否在该模式下透露背景故事

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(agent_id, mode)
);
COMMENT ON TABLE public.agent_mode_configs IS '存储角色在不同模式下的特定配置，解决聊天和故事模式的设定冲突';
COMMENT ON COLUMN public.agent_mode_configs.mode_specific_first_mes IS '该模式下的专用开场白，优先级高于agents.first_mes';
COMMENT ON COLUMN public.agent_mode_configs.enable_mes_example IS '故事模式下通常设为false，避免对话示例干扰剧情';


-- ========================================
-- 3. 故事模板核心表
-- ========================================

-- 故事表
CREATE TABLE IF NOT EXISTS public.stories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title VARCHAR(200) NOT NULL,
    theme_prompt TEXT NOT NULL,
    worldview_text TEXT, -- 新增: 存储宏大世界观设定
    cover_image_url TEXT,
    cover_image_prompt TEXT, -- 新增: 存储故事封面图片的生成提示词

    -- ▼▼▼【织梦者引擎核心新增】▼▼▼
    source_analysis JSONB, -- 用于存储AI分析出的完整GDD，作为游戏运行时的"故事圣经"
    protagonist_agent_id UUID REFERENCES public.agents(id) ON DELETE SET NULL, -- 关联女主角的Agent ID
    -- ▲▲▲【织梦者引擎核心新增】▲▲▲

    tags JSONB DEFAULT '[]'::jsonb, -- 故事标签
    total_chapters INTEGER DEFAULT 0 NOT NULL, -- 章节总数

    popularity INTEGER DEFAULT 0 NOT NULL,
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.stories IS '互动故事的顶层定义';
COMMENT ON COLUMN public.stories.worldview_text IS '存储详细的世界观设定，如历史背景、势力划分、关键规则等';
COMMENT ON COLUMN public.stories.cover_image_prompt IS '存储故事封面图片的AI生成提示词，用于追溯和优化封面图片生成';
COMMENT ON COLUMN public.stories.source_analysis IS '存储AI对源小说的完整分析结果(GDD)，作为动态剧情生成的依据。';
COMMENT ON COLUMN public.stories.protagonist_agent_id IS '指定该故事中玩家扮演的女主角所对应的Agent ID。';
COMMENT ON COLUMN public.stories.tags IS '故事标签，以JSONB数组格式存储，用于分类和搜索';
COMMENT ON COLUMN public.stories.total_chapters IS '故事的章节总数，用于显示进度和完整性';

-- 故事章节表
CREATE TABLE IF NOT EXISTS public.story_chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID REFERENCES public.stories(id) ON DELETE CASCADE NOT NULL,
    chapter_number INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    mission_objective_text TEXT, -- 本章的核心任务目标描述
    background_text TEXT,        -- 本章开局的背景介绍
    background_image_url TEXT,
    clear_condition_text TEXT,   -- 本章的通关条件描述
    opening_sequence JSONB,      -- 存储本章开头的演绎消息序列
    chapter_event_summary TEXT,  -- 供AI阅读的本章关键事件和情境摘要
    completion_summary_text TEXT, -- 章节完成后显示给玩家的总结文本，以女主角第一人称视角撰写
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(story_id, chapter_number)
);
COMMENT ON TABLE public.story_chapters IS '故事的章节列表和任务目标';
COMMENT ON COLUMN public.story_chapters.chapter_event_summary IS '由编译器预生成的、供AI在运行时阅读的本章关键事件、场景和情感基调的摘要。';

-- 故事与角色的关联表
CREATE TABLE IF NOT EXISTS public.story_agents (
    story_id UUID REFERENCES public.stories(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    PRIMARY KEY (story_id, agent_id)
);
COMMENT ON TABLE public.story_agents IS '存储故事与其包含的角色的多对多关系';

-- ========================================
-- 4. 统一对话核心表 (重构核心)
-- ========================================

-- 对话会话表
CREATE TABLE IF NOT EXISTS public.chats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    story_id UUID REFERENCES public.stories(id) ON DELETE SET NULL, -- 如果是故事，则关联故事模板
    task_progress JSONB DEFAULT '{}'::jsonb, -- 存储游戏状态，格式见下方详细说明

    -- ▼▼▼【织梦者引擎核心新增】▼▼▼
    game_state JSONB, -- 用于存储当前游戏中的所有变量值，如好感度、误解度等
    -- ▲▲▲【织梦者引擎核心新增】▲▲▲

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.chats IS '统一的对话会话表，包含普通聊天和互动故事';
COMMENT ON COLUMN public.chats.task_progress IS '存储游戏状态，JSON格式：
{
  "current_chapter_id": "chapter-uuid-1",
  "chapters": {
    "chapter-uuid-1": {
      "progress": 75,
      "status": "in_progress"
    },
    "chapter-uuid-2": {
      "progress": 0,
      "status": "not_started"
    }
  },
  "opening_sequence_index": -1
}';
COMMENT ON COLUMN public.chats.game_state IS '存储当前游戏会话的动态变量，例如：{"何以琛.好感度": 55, "何以琛.误解程度": 60}';

-- 会话参与者表 (AI角色)
CREATE TABLE IF NOT EXISTS public.chat_participants (
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    PRIMARY KEY (chat_id, agent_id)
);
COMMENT ON TABLE public.chat_participants IS '会话参与者（AI角色）';

-- 统一的消息日志表 - 添加向量字段用于RAG记忆
CREATE TABLE IF NOT EXISTS public.messages (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE SET NULL, -- 说话的AI角色，旁白或用户时为NULL
    role VARCHAR(20) NOT NULL CHECK (role IN (
        'user',         -- 用户的输入
        'assistant',    -- AI角色的回复
        'narration',    -- 旁白或剧情描述
        'choice',       -- 提供给用户的选项
        'image'         -- 需要展示的图片
    )),
    content TEXT NOT NULL, -- 消息文本, 图片的prompt, 选项的引导语等
    metadata JSONB,      -- 用于存储额外信息, 如: {"choices": ["选项A", "选项B"]}, {"image_url": "..."}
    audio_url TEXT,      -- 保留语音URL字段
    embedding vector(768), -- 消息内容的向量表示，用于RAG检索 (使用768维度的Gemini embedding模型)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.messages IS '统一的消息日志表，记录所有对话内容和剧情节点';

-- 对话摘要表 (长期记忆) - 增强版：支持模式感知
CREATE TABLE IF NOT EXISTS public.chat_summaries (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE UNIQUE NOT NULL,
    summary_text TEXT,
    story_progress_summary TEXT, -- 故事模式专用：任务进度和剧情发展摘要
    relationship_summary TEXT,   -- 聊天模式专用：情感关系发展摘要
    last_summarized_message_id BIGINT,
    summary_type VARCHAR(20) DEFAULT 'chat' CHECK (summary_type IN ('chat', 'story')),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.chat_summaries IS '存储对话的滚动摘要，支持聊天和故事两种模式的不同摘要策略';
COMMENT ON COLUMN public.chat_summaries.story_progress_summary IS '故事模式下的任务进度和剧情发展摘要';
COMMENT ON COLUMN public.chat_summaries.relationship_summary IS '聊天模式下的角色关系和情感发展摘要';

-- 世界书表 (知识库)
CREATE TABLE IF NOT EXISTS public.world_infos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.world_infos IS '世界书/知识库的顶层容器';

-- 世界书条目表
CREATE TABLE IF NOT EXISTS public.world_info_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    world_info_id UUID REFERENCES public.world_infos(id) ON DELETE CASCADE NOT NULL,
    keywords TEXT[] NOT NULL, -- 触发关键词数组
    content TEXT NOT NULL,
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.world_info_entries IS '世界书的具体条目，包含关键词和内容';

-- 智能体与世界书的关联表
CREATE TABLE IF NOT EXISTS public.agent_world_infos (
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    world_info_id UUID REFERENCES public.world_infos(id) ON DELETE CASCADE NOT NULL,
    PRIMARY KEY (agent_id, world_info_id)
);
COMMENT ON TABLE public.agent_world_infos IS '智能体与世界书的多对多关联';

-- 记忆表
CREATE TABLE IF NOT EXISTS public.agent_memories (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    memory_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(agent_id, user_id, memory_text) -- 防止重复记忆
);
COMMENT ON TABLE public.agent_memories IS '存储用户与智能体之间的专属记忆点';

-- 羁绊关系表
CREATE TABLE IF NOT EXISTS public.user_agent_bonds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    bond_value INT DEFAULT 0 NOT NULL,
    bond_level INT DEFAULT 1 NOT NULL, -- 初始等级为1
    last_bond_increase_date DATE DEFAULT CURRENT_DATE NOT NULL,
    daily_bond_increase INT DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, agent_id) -- 确保每个用户和角色的组合是唯一的
);
COMMENT ON TABLE public.user_agent_bonds IS '存储用户与AI角色之间的羁绊值、等级和相关数据。';

-- 为常用查询创建索引以提高性能
CREATE INDEX IF NOT EXISTS idx_user_agent_bonds_user_agent ON public.user_agent_bonds(user_id, agent_id);


-- ========================================
-- 5. 函数与触发器
-- ========================================

-- (健壮性) 先删除可能存在的旧函数
DROP FUNCTION IF EXISTS public.increment_view_count(UUID);
CREATE OR REPLACE FUNCTION public.increment_view_count(p_agent_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.agents SET view_count = view_count + 1 WHERE id = p_agent_id;
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS public.increment_dialogue_count(UUID);
CREATE OR REPLACE FUNCTION public.increment_dialogue_count(p_agent_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.agents SET dialogue_count = dialogue_count + 1 WHERE id = p_agent_id;
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS public.create_user_profile_on_signup();
CREATE OR REPLACE FUNCTION public.create_user_profile_on_signup()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, display_name, avatar_url)
  VALUES (new.id, new.raw_user_meta_data->>'display_name', new.raw_user_meta_data->>'avatar_url');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 触发器不需要DROP，CREATE OR REPLACE会处理
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.create_user_profile_on_signup();

DROP FUNCTION IF EXISTS public.update_updated_at_column();
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 新增：当插入新消息时自动更新chat的updated_at时间戳
DROP FUNCTION IF EXISTS public.update_chat_updated_at_on_new_message();
CREATE OR REPLACE FUNCTION public.update_chat_updated_at_on_new_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.chats
  SET updated_at = NOW()
  WHERE id = NEW.chat_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 为需要自动更新时间的表绑定触发器
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_agents_updated_at ON public.agents;
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON public.agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_stories_updated_at ON public.stories;
CREATE TRIGGER update_stories_updated_at BEFORE UPDATE ON public.stories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_story_chapters_updated_at ON public.story_chapters;
CREATE TRIGGER update_story_chapters_updated_at BEFORE UPDATE ON public.story_chapters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- 新增 chats 表的触发器
DROP TRIGGER IF EXISTS update_chats_updated_at ON public.chats;
CREATE TRIGGER update_chats_updated_at BEFORE UPDATE ON public.chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 新增：当插入新消息时自动更新chat的updated_at时间戳的触发器
DROP TRIGGER IF EXISTS on_new_message_update_chat_timestamp ON public.messages;
CREATE TRIGGER on_new_message_update_chat_timestamp
  AFTER INSERT ON public.messages
  FOR EACH ROW EXECUTE PROCEDURE public.update_chat_updated_at_on_new_message();


DROP FUNCTION IF EXISTS public.get_public_agents_with_creator(INT);
CREATE OR REPLACE FUNCTION public.get_public_agents_with_creator(p_limit int)
RETURNS TABLE (
    id uuid, user_id uuid, name character varying, description text, first_mes text, system_prompt text,
    image_url text, avatar_url text, gender character varying, is_public boolean, tags jsonb,
    interaction_mode character varying, voice_name character varying, is_system_agent boolean,
    view_count integer, dialogue_count integer, created_at timestamp with time zone, updated_at timestamp with time zone,
    creator_name character varying, creator_avatar_url text
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id, a.user_id, a.name, a.description, a.first_mes, a.system_prompt,
        a.image_url, a.avatar_url, a.gender, a.is_public, a.tags,
        a.interaction_mode, a.voice_name, a.is_system_agent,
        a.view_count, a.dialogue_count, a.created_at, a.updated_at,
        COALESCE(up.display_name, '匿名创作者') AS creator_name,
        up.avatar_url AS creator_avatar_url
    FROM public.agents a
    LEFT JOIN public.user_profiles up ON a.user_id = up.user_id
    WHERE a.is_public = true
    ORDER BY a.dialogue_count DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- (重构) get_user_chat_list 函数
DROP FUNCTION IF EXISTS public.get_user_chat_list(UUID, INT);
CREATE OR REPLACE FUNCTION public.get_user_chat_list(p_user_id UUID, p_limit INT)
RETURNS TABLE (
    chat_id UUID,
    story_id UUID,
    participants JSONB,
    latest_message TEXT,
    latest_message_time TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    is_story BOOLEAN,
    display_name TEXT,
    display_avatar TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_messages AS (
        -- ▼▼▼【核心修复 v1.1】▼▼▼
        -- 移除了不必要的 JOIN 和 WHERE 子句，避免列名歧义
        SELECT
            m.chat_id,
            m.content,
            m.created_at,
            ROW_NUMBER() OVER(PARTITION BY m.chat_id ORDER BY m.created_at DESC) as rn
        FROM public.messages m
        -- ▲▲▲【核心修复 v1.1】▲▲▲
    ),
    chat_participants_agg AS (
        SELECT
            cp.chat_id,
            jsonb_agg(jsonb_build_object('id', a.id, 'name', a.name, 'avatar_url', a.avatar_url)) as participants_json,
            (array_agg(a.name))[1] as agent_name,
            (array_agg(a.avatar_url))[1] as agent_avatar
        FROM public.chat_participants cp
        JOIN public.agents a ON cp.agent_id = a.id
        GROUP BY cp.chat_id
    )
    SELECT
        c.id as chat_id,
        c.story_id,
        cpa.participants_json as participants,
        lm.content as latest_message,
        lm.created_at as latest_message_time,
        c.updated_at,
        (c.story_id IS NOT NULL) as is_story,
        -- ▼▼▼【核心修复 v1.2】▼▼▼
        -- 将 COALESCE 的结果显式转换为 TEXT 类型，以匹配函数返回类型
        COALESCE(s.title, cpa.agent_name)::TEXT as display_name,
        -- ▲▲▲【核心修复 v1.2】▲▲▲
        COALESCE(s.cover_image_url, cpa.agent_avatar) as display_avatar
    FROM public.chats c
    LEFT JOIN latest_messages lm ON c.id = lm.chat_id AND lm.rn = 1
    LEFT JOIN chat_participants_agg cpa ON c.id = cpa.chat_id
    LEFT JOIN public.stories s ON c.story_id = s.id
    WHERE c.user_id = p_user_id
    ORDER BY c.updated_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS public.health_check();
CREATE OR REPLACE FUNCTION public.health_check()
RETURNS TEXT AS $$
BEGIN
  RETURN 'ok';
END;
$$ LANGUAGE plpgsql;

-- RAG记忆检索函数
DROP FUNCTION IF EXISTS public.match_chat_memories(UUID, vector, INT, FLOAT);
CREATE OR REPLACE FUNCTION public.match_chat_memories(
    p_chat_id UUID,
    query_embedding vector(768),
    match_count INT DEFAULT 5,
    match_threshold FLOAT DEFAULT 0.78
)
RETURNS TABLE (
    id BIGINT,
    content TEXT,
    role VARCHAR(20),
    agent_id UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    similarity FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.content,
        m.role,
        m.agent_id,
        m.created_at,
        1 - (m.embedding <=> query_embedding) AS similarity
    FROM public.messages m
    WHERE m.chat_id = p_chat_id
      AND m.embedding IS NOT NULL
      AND 1 - (m.embedding <=> query_embedding) > match_threshold
    ORDER BY m.embedding <=> query_embedding
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- 世界书条目检索函数 (支持关键词匹配)
DROP FUNCTION IF EXISTS public.get_active_world_info_entries(UUID, TEXT);
CREATE OR REPLACE FUNCTION public.get_active_world_info_entries(
    p_agent_id UUID,
    p_search_text TEXT
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    keywords TEXT[],
    priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        wie.id,
        wie.content,
        wie.keywords,
        wie.priority
    FROM public.world_info_entries wie
    JOIN public.agent_world_infos awi ON wie.world_info_id = awi.world_info_id
    WHERE awi.agent_id = p_agent_id
      AND wie.is_active = true
      AND (
        -- 检查关键词是否在搜索文本中出现
        EXISTS (
          SELECT 1 FROM unnest(wie.keywords) AS keyword
          WHERE p_search_text ILIKE '%' || keyword || '%'
        )
      )
    ORDER BY wie.priority DESC, wie.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- 获取模式感知的角色配置
DROP FUNCTION IF EXISTS public.get_agent_with_mode_config(UUID, TEXT);
CREATE OR REPLACE FUNCTION public.get_agent_with_mode_config(
    p_agent_id UUID,
    p_mode TEXT DEFAULT 'chat'
)
RETURNS TABLE (
    -- 基础角色信息
    id UUID,
    name VARCHAR(100),
    description TEXT,
    personality TEXT,
    scenario TEXT,
    first_mes TEXT,
    mes_example TEXT,
    image_url TEXT,
    avatar_url TEXT,
    voice_name VARCHAR(50),
    backstory_text TEXT,

    -- 模式特定配置
    effective_first_mes TEXT,
    effective_scenario TEXT,
    effective_instructions TEXT,
    should_use_mes_example BOOLEAN,
    should_use_backstory BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.description,
        a.personality,
        a.scenario,
        a.first_mes,
        a.mes_example,
        a.image_url,
        a.avatar_url,
        a.voice_name,
        a.backstory_text,

        -- 优先使用模式特定配置，否则使用默认配置
        COALESCE(amc.mode_specific_first_mes, a.first_mes) as effective_first_mes,
        COALESCE(amc.mode_specific_scenario, a.scenario) as effective_scenario,
        amc.mode_specific_instructions as effective_instructions,
        COALESCE(amc.enable_mes_example, true) as should_use_mes_example,
        COALESCE(amc.enable_backstory, CASE WHEN p_mode = 'story' THEN true ELSE false END) as should_use_backstory

    FROM public.agents a
    LEFT JOIN public.agent_mode_configs amc ON a.id = amc.agent_id AND amc.mode = p_mode
    WHERE a.id = p_agent_id;
END;
$$ LANGUAGE plpgsql;


-- =================================================================
-- 6. 行级安全策略 (RLS)
-- =================================================================

-- 开启RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_memories ENABLE ROW LEVEL SECURITY;
-- 新表RLS
ALTER TABLE public.chat_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.world_infos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.world_info_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_world_infos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_mode_configs ENABLE ROW LEVEL SECURITY;

-- 定义策略
DROP POLICY IF EXISTS "Allow read access to everyone" ON public.user_profiles;
CREATE POLICY "Allow read access to everyone" ON public.user_profiles FOR SELECT USING (true);
DROP POLICY IF EXISTS "Allow individual update access" ON public.user_profiles;
CREATE POLICY "Allow individual update access" ON public.user_profiles FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow read access for public and own agents" ON public.agents;
CREATE POLICY "Allow read access for public and own agents" ON public.agents FOR SELECT USING ((is_public = true) OR (auth.uid() = user_id));
DROP POLICY IF EXISTS "Allow individual insert access" ON public.agents;
CREATE POLICY "Allow individual insert access" ON public.agents FOR INSERT WITH CHECK (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual update access" ON public.agents;
CREATE POLICY "Allow individual update access" ON public.agents FOR UPDATE USING (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual delete access" ON public.agents;
CREATE POLICY "Allow individual delete access" ON public.agents FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow read access for public and own stories" ON public.stories;
CREATE POLICY "Allow read access for public and own stories" ON public.stories FOR SELECT USING ((is_public = true) OR (auth.uid() = user_id));
DROP POLICY IF EXISTS "Allow individual insert access" ON public.stories;
CREATE POLICY "Allow individual insert access" ON public.stories FOR INSERT WITH CHECK (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual update access" ON public.stories;
CREATE POLICY "Allow individual update access" ON public.stories FOR UPDATE USING (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual delete access" ON public.stories;
CREATE POLICY "Allow individual delete access" ON public.stories FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow read access to all authenticated users" ON public.story_chapters;
CREATE POLICY "Allow read access to all authenticated users" ON public.story_chapters FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow read access to all authenticated users" ON public.story_agents;
CREATE POLICY "Allow read access to all authenticated users" ON public.story_agents FOR SELECT USING (auth.role() = 'authenticated');
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON public.story_agents;
CREATE POLICY "Allow insert for authenticated users" ON public.story_agents FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 新表策略
DROP POLICY IF EXISTS "Allow individual access on chats" ON public.chats;
CREATE POLICY "Allow individual access on chats" ON public.chats FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow access to participants of own chats" ON public.chat_participants;
CREATE POLICY "Allow access to participants of own chats" ON public.chat_participants FOR ALL USING ((SELECT user_id FROM public.chats WHERE id = chat_id) = auth.uid());

DROP POLICY IF EXISTS "Allow access to messages of own chats" ON public.messages;
CREATE POLICY "Allow access to messages of own chats" ON public.messages FOR ALL USING ((SELECT user_id FROM public.chats WHERE id = chat_id) = auth.uid());

DROP POLICY IF EXISTS "Allow individual access to own memories" ON public.agent_memories;
CREATE POLICY "Allow individual access to own memories" ON public.agent_memories FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- 新表的RLS策略
DROP POLICY IF EXISTS "Allow access to summaries of own chats" ON public.chat_summaries;
CREATE POLICY "Allow access to summaries of own chats" ON public.chat_summaries FOR ALL USING ((SELECT user_id FROM public.chats WHERE id = chat_id) = auth.uid());

DROP POLICY IF EXISTS "Allow read access for public and own world_infos" ON public.world_infos;
CREATE POLICY "Allow read access for public and own world_infos" ON public.world_infos FOR SELECT USING ((is_public = true) OR (auth.uid() = user_id));
DROP POLICY IF EXISTS "Allow individual access to own world_infos" ON public.world_infos;
CREATE POLICY "Allow individual access to own world_infos" ON public.world_infos FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Allow individual update access to own world_infos" ON public.world_infos FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Allow individual delete access to own world_infos" ON public.world_infos FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow access to entries of accessible world_infos" ON public.world_info_entries;
CREATE POLICY "Allow access to entries of accessible world_infos" ON public.world_info_entries FOR ALL USING ((SELECT user_id FROM public.world_infos WHERE id = world_info_id) = auth.uid() OR (SELECT is_public FROM public.world_infos WHERE id = world_info_id) = true);

DROP POLICY IF EXISTS "Allow access to agent world_info associations" ON public.agent_world_infos;
CREATE POLICY "Allow access to agent world_info associations" ON public.agent_world_infos FOR ALL USING ((SELECT user_id FROM public.agents WHERE id = agent_id) = auth.uid());

DROP POLICY IF EXISTS "Allow access to agent mode configs" ON public.agent_mode_configs;
CREATE POLICY "Allow access to agent mode configs" ON public.agent_mode_configs FOR ALL USING ((SELECT user_id FROM public.agents WHERE id = agent_id) = auth.uid());

-- =================================================================
-- 7. 性能优化：索引
-- =================================================================
CREATE INDEX IF NOT EXISTS idx_agents_user_id ON public.agents(user_id);
CREATE INDEX IF NOT EXISTS idx_stories_user_id ON public.stories(user_id);
CREATE INDEX IF NOT EXISTS idx_stories_tags ON public.stories USING gin(tags);
CREATE INDEX IF NOT EXISTS idx_story_chapters_story_id ON public.story_chapters(story_id);
CREATE INDEX IF NOT EXISTS idx_story_agents_story_id ON public.story_agents(story_id);
CREATE INDEX IF NOT EXISTS idx_story_agents_agent_id ON public.story_agents(agent_id);
CREATE INDEX IF NOT EXISTS idx_chats_user_id ON public.chats(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_chat_id_created_at ON public.messages(chat_id, created_at);
CREATE INDEX IF NOT EXISTS idx_agent_memories_user_id_agent_id ON public.agent_memories(user_id, agent_id);

-- RAG相关索引
CREATE INDEX IF NOT EXISTS idx_messages_embedding ON public.messages USING hnsw (embedding vector_l2_ops);
CREATE INDEX IF NOT EXISTS idx_chat_summaries_chat_id ON public.chat_summaries(chat_id);

-- 世界书相关索引
CREATE INDEX IF NOT EXISTS idx_world_infos_user_id ON public.world_infos(user_id);
CREATE INDEX IF NOT EXISTS idx_world_info_entries_world_info_id ON public.world_info_entries(world_info_id);
CREATE INDEX IF NOT EXISTS idx_world_info_entries_keywords ON public.world_info_entries USING gin(keywords);
CREATE INDEX IF NOT EXISTS idx_agent_world_infos_agent_id ON public.agent_world_infos(agent_id);

-- 模式配置相关索引
CREATE INDEX IF NOT EXISTS idx_agent_mode_configs_agent_id_mode ON public.agent_mode_configs(agent_id, mode);


-- =================================================================
-- 8. 排行榜函数
-- =================================================================

-- 获取热门故事榜单
DROP FUNCTION IF EXISTS public.get_story_rankings(text);
CREATE OR REPLACE FUNCTION public.get_story_rankings(p_period TEXT)
RETURNS TABLE (rank BIGINT, id UUID, cover_url TEXT, title VARCHAR, popularity BIGINT) AS $$
DECLARE
    time_interval INTERVAL;
BEGIN
    -- Set time interval based on period
    time_interval := CASE
        WHEN p_period = 'daily' THEN '1 day'
        WHEN p_period = 'weekly' THEN '7 days'
        WHEN p_period = 'monthly' THEN '1 month'
        ELSE '999 years' -- all time
    END;

    RETURN QUERY
    WITH recent_chats AS (
        SELECT
            c.story_id,
            COUNT(c.id) as chat_count
        FROM public.chats c
        WHERE c.story_id IS NOT NULL
          AND c.created_at >= NOW() - time_interval
        GROUP BY c.story_id
    )
    SELECT
        row_number() OVER (ORDER BY COALESCE(rc.chat_count, 0) DESC, s.popularity DESC) as rank,
        s.id,
        s.cover_image_url as cover_url,
        s.title,
        COALESCE(rc.chat_count, 0) as popularity
    FROM public.stories s
    LEFT JOIN recent_chats rc ON s.id = rc.story_id
    WHERE s.is_public = true
    ORDER BY popularity DESC, s.popularity DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- 获取热门角色榜单
DROP FUNCTION IF EXISTS public.get_agent_rankings(text);
CREATE OR REPLACE FUNCTION public.get_agent_rankings(p_period TEXT)
RETURNS TABLE (rank BIGINT, id UUID, cover_url TEXT, title VARCHAR, popularity BIGINT) AS $$
DECLARE
    time_interval INTERVAL;
BEGIN
    -- Set time interval based on period
    time_interval := CASE
        WHEN p_period = 'daily' THEN '1 day'
        WHEN p_period = 'weekly' THEN '7 days'
        WHEN p_period = 'monthly' THEN '1 month'
        ELSE '999 years' -- all time
    END;

    RETURN QUERY
    WITH recent_chats AS (
        SELECT
            cp.agent_id,
            COUNT(c.id) as chat_count
        FROM public.chats c
        JOIN public.chat_participants cp ON c.id = cp.chat_id
        WHERE c.created_at >= NOW() - time_interval
          AND c.story_id IS NULL -- Only count roleplay chats, not story chats
        GROUP BY cp.agent_id
    )
    SELECT
        row_number() OVER (ORDER BY COALESCE(rc.chat_count, 0) DESC, a.dialogue_count DESC) as rank,
        a.id,
        a.image_url as cover_url,
        a.name as title,
        COALESCE(rc.chat_count, 0) as popularity
    FROM public.agents a
    LEFT JOIN recent_chats rc ON a.id = rc.agent_id
    WHERE a.is_public = true AND a.is_system_agent = false
    ORDER BY popularity DESC, a.dialogue_count DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- =================================================================
-- 9. 织梦者引擎 - 章节推进函数
-- =================================================================
DROP FUNCTION IF EXISTS public.advance_to_next_chapter(uuid, uuid);
CREATE OR REPLACE FUNCTION public.advance_to_next_chapter(
    p_current_chat_id UUID,
    p_user_id UUID
)
RETURNS UUID AS $$
DECLARE
    v_story_id UUID;
    v_current_chapter_id UUID;
    v_current_chapter_number INT;
    v_next_chapter RECORD;
    v_new_chat_id UUID;
    v_story_agents UUID[];
    v_current_game_state JSONB;
    v_current_task_progress JSONB;
BEGIN
    -- 1. 根据当前chat_id找到故事ID和当前章节信息
    SELECT
        c.story_id,
        c.game_state,
        c.task_progress
    INTO v_story_id, v_current_game_state, v_current_task_progress
    FROM public.chats c
    WHERE c.id = p_current_chat_id AND c.user_id = p_user_id;

    IF v_story_id IS NULL THEN
        RAISE EXCEPTION '当前聊天并非故事模式或不属于该用户';
    END IF;

    -- 2. 从task_progress中获取当前章节ID
    v_current_chapter_id := (v_current_task_progress->>'current_chapter_id')::UUID;

    -- 如果没有current_chapter_id，则查找第一章
    IF v_current_chapter_id IS NULL THEN
        SELECT id, chapter_number INTO v_current_chapter_id, v_current_chapter_number
        FROM public.story_chapters
        WHERE story_id = v_story_id
        ORDER BY chapter_number ASC
        LIMIT 1;
    ELSE
        -- 获取当前章节号
        SELECT chapter_number INTO v_current_chapter_number
        FROM public.story_chapters
        WHERE id = v_current_chapter_id;
    END IF;

    IF v_current_chapter_number IS NULL THEN
        RAISE EXCEPTION '无法确定当前章节';
    END IF;

    -- 3. 找到下一章节（修复：查找比当前章节号大的最接近的章节）
    SELECT * INTO v_next_chapter
    FROM public.story_chapters
    WHERE story_id = v_story_id AND chapter_number > v_current_chapter_number
    ORDER BY chapter_number ASC
    LIMIT 1;

    -- 如果没有下一章，返回 NULL
    IF v_next_chapter IS NULL THEN
        RETURN NULL;
    END IF;

    -- 4. 获取故事的所有参与角色
    SELECT array_agg(agent_id) INTO v_story_agents
    FROM public.story_agents
    WHERE story_id = v_story_id;

    -- 5. 创建新的task_progress，重置为下一章的初始状态
    v_current_task_progress := jsonb_build_object(
        'current_chapter_id', v_next_chapter.id,
        'chapters', jsonb_build_object(
            v_next_chapter.id::text, jsonb_build_object(
                'progress', 0,
                'status', 'in_progress'
            )
        ),
        'opening_sequence_index', -1
    );

    -- 6. 创建一个新的聊天会话，继承游戏状态但重置任务进度
    INSERT INTO public.chats (user_id, story_id, game_state, task_progress)
    VALUES (p_user_id, v_story_id, v_current_game_state, v_current_task_progress)
    RETURNING id INTO v_new_chat_id;

    -- 7. 为新会话关联所有故事角色
    IF v_story_agents IS NOT NULL THEN
        INSERT INTO public.chat_participants (chat_id, agent_id)
        SELECT v_new_chat_id, unnest(v_story_agents);
    END IF;

    -- 8. 返回新的 chat_id
    RETURN v_new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 脚本结束 --
-- 
-- =================================================================
-- 羁绊系统相关函数
-- =================================================================

-- 羁绊值增加函数
DROP FUNCTION IF EXISTS public.increment_bond_value(UUID, UUID, INT);
CREATE OR REPLACE FUNCTION public.increment_bond_value(
    p_user_id UUID,
    p_agent_id UUID,
    p_increase_amount INT
)
RETURNS TABLE (success BOOLEAN, message TEXT, new_bond_value INT, new_bond_level INT) AS $$
DECLARE
    v_bond_record public.user_agent_bonds%ROWTYPE;
    v_new_level INT;
    v_daily_limit INT := 100;
    -- 定义羁绊等级阈值 (LV1到LV10)
    level_thresholds INT[] := ARRAY[1, 5, 30, 60, 100, 150, 300, 500, 700, 1000];
BEGIN
    -- 确保羁绊记录存在，如果不存在则创建
    INSERT INTO public.user_agent_bonds (user_id, agent_id)
    VALUES (p_user_id, p_agent_id)
    ON CONFLICT (user_id, agent_id) DO NOTHING;

    -- 获取当前记录
    SELECT * INTO v_bond_record 
    FROM public.user_agent_bonds 
    WHERE user_id = p_user_id AND agent_id = p_agent_id;

    -- 如果是新的一天，重置每日获取量
    IF v_bond_record.last_bond_increase_date < CURRENT_DATE THEN
        v_bond_record.daily_bond_increase := 0;
    END IF;

    -- 检查是否达到每日上限
    IF v_bond_record.daily_bond_increase >= v_daily_limit THEN
        RETURN QUERY SELECT FALSE, '今日羁绊值已达上限', v_bond_record.bond_value, v_bond_record.bond_level;
        RETURN;
    END IF;

    -- 根据规则决定实际增加值
    p_increase_amount := LEAST(p_increase_amount, v_daily_limit - v_bond_record.daily_bond_increase);

    -- 更新数值
    v_bond_record.bond_value := v_bond_record.bond_value + p_increase_amount;
    v_bond_record.daily_bond_increase := v_bond_record.daily_bond_increase + p_increase_amount;

    -- 根据新的羁绊值计算新等级
    v_new_level := 1; -- 默认为1级
    FOR i IN REVERSE array_length(level_thresholds, 1)..1 LOOP
        IF v_bond_record.bond_value >= level_thresholds[i] THEN
            v_new_level := i;
            EXIT;
        END IF;
    END LOOP;

    v_bond_record.bond_level := v_new_level;

    -- 写回数据库
    UPDATE public.user_agent_bonds SET
        bond_value = v_bond_record.bond_value,
        bond_level = v_bond_record.bond_level,
        daily_bond_increase = v_bond_record.daily_bond_increase,
        last_bond_increase_date = CURRENT_DATE
    WHERE user_id = p_user_id AND agent_id = p_agent_id;

    RETURN QUERY SELECT TRUE, '更新成功', v_bond_record.bond_value, v_bond_record.bond_level;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 羁绊关系表的RLS策略
ALTER TABLE public.user_agent_bonds ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "允许用户访问自己的羁绊数据" ON public.user_agent_bonds;
CREATE POLICY "允许用户访问自己的羁绊数据"
  ON public.user_agent_bonds
  FOR SELECT USING (auth.uid() = user_id);
</file>

<file path="backend/package.json">
{
  "devDependencies": {
    "prisma": "^5.17.0"
  },
  "dependencies": {
    "@prisma/client": "^5.17.0"
  }
}
</file>

<file path="backend/requirements.txt">
# backend/requirements.txt

# === Core Web Framework ===
fastapi==0.115.6
uvicorn==0.32.1
python-dotenv==1.0.1

# === Database & ORM ===
supabase==2.16.0

# === Google AI & AI Libraries ===
google-genai==1.23.0
google-api-core>=1.34.1
pydantic>=2.0.0
pillow==10.4.0

# === Scientific Computing ===
# numpy==1.24.3  # 已移除：项目中未使用
# scikit-learn==1.3.0  # 已移除：项目中未使用

# === AI & ML Libraries ===
instructor==1.2.1

# === Utilities ===
python-multipart==0.0.9
websockets==14.1
imagekitio==4.1.0
aiofiles==24.1.0
</file>

<file path="backend/scripts/__init__.py">
# scripts package
</file>

<file path="backend/scripts/agent_generator.py">
#!/usr/bin/env python3
"""
星恋AI - V5.0 心动原型精准复现系统
基于中国女性偏好男星形象全景分析的原型驱动角色生成引擎。

运行前置条件:
1. 后端API服务器 (通过 start.bat) 必须正在另一个终端中运行。
2. .env 文件必须已正确配置。
3. backend/src/data/archetypes.json 文件必须存在。

该脚本将:
1. 创建一个唯一的、可预测的测试用户 (<EMAIL>)。
2. 基于18种心动原型，精准生成高吸引力的AI男主角色。
3. 每个角色都严格遵循对应原型的气质、外观和吸引力要素。
"""
import os
import sys
import asyncio
import httpx
import time
import uuid
import re
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
from supabase import create_client, Client
import base64

# --- 核心设置 ---
# 将项目根目录添加到Python路径，以便导入src中的模块
sys.path.append(str(Path(__file__).parent.parent))
load_dotenv(Path(__file__).parent.parent / '.env')

from src.services import llm_service, simple_imagekit_service, supabase_service
from src.pydantic_models import GeneratedCoreAgentData
from src.utils.user_management import create_user_if_not_exists

# --- 全局配置 ---
API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

# 要生成的智能体的"心动原型"名称（必须与 archetypes.json 中的 archetype_name 完全对应）
AGENT_IDEAS = [
    "清新少年感",
    "国风君子",
    "叔系天花板",
    "病娇破碎感",
    "职场精英",
    "多才艺术家",
    "智性学霸",
    "痞帅浪子",
    "温柔守护者",
    "硬汉柔情"
]

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)

# --- 核心函数 ---
# 现在使用统一的用户管理模块

async def generate_and_save_agent_from_archetype(archetype_name: str, user_id: str):
    """基于心动原型，完整地生成并存储一个智能体。"""
    log_prefix = f"  [ARCHETYPE_GEN ({archetype_name})]"
    print(f"\n{log_prefix} 开始生成...")
    try:
        # 1. 使用新的原型驱动方法生成结构化数据、图片和prompt
        structured_data, image_bytes, image_prompt = await llm_service.generate_agent_from_archetype_with_image(archetype_name)

        # 名称和开场白的后处理
        original_name = structured_data.name
        cleaned_name = re.sub(r'\s*\(.*\)\s*|\s*（.*）\s*', '', original_name).strip()
        if original_name != cleaned_name:
            structured_data.name = cleaned_name

        if hasattr(structured_data, 'first_mes') and structured_data.first_mes:
            if structured_data.first_mes.startswith("'") and structured_data.first_mes.endswith("'"):
                structured_data.first_mes = structured_data.first_mes.strip("'")

        print(f"{log_prefix} ✓ 基于原型 '{archetype_name}' 生成角色: '{structured_data.name}'")

        # 2. 上传图片（如果成功生成）
        image_url = None
        if image_bytes:
            print(f"{log_prefix}   ⎿ 上传图片...")
            upload_result = await simple_imagekit_service.upload_image_from_base64(
                base64_data=base64.b64encode(image_bytes).decode('utf-8'),
                file_name=f"{structured_data.name.replace(' ', '_')}_{uuid.uuid4().hex[:6]}.png",
                folder="/xinglian/agent/"
            )
            if upload_result["success"]:
                image_url = upload_result.get("url")
            else:
                print(f"   [WARN] 图片上传失败: {upload_result['error']}，将不使用图片URL。")
        else:
            print(f"   [WARN] 图片生成失败，将不使用图片URL。")

        # 3. 存入数据库 (使用 SupabaseService)
        print(f"{log_prefix}   ⎿ 保存到数据库...")
        new_agent = await supabase_service.create_agent(
            user_id=user_id,
            # 基础信息
            name=structured_data.name,
            description=structured_data.description,
            tags=structured_data.tags,
            image_url=image_url, # 使用可能为None的image_url
            avatar_url=image_url, # 使用可能为None的avatar_url
            gender=structured_data.gender,
            voice_name=structured_data.voice_name,
            # TavernAI兼容字段
            personality=structured_data.persona,
            scenario=structured_data.scenario,
            first_mes=structured_data.first_mes,
            mes_example=structured_data.mes_example,
            # 图片生成prompt
            image_generation_prompt=image_prompt, # 无论图片是否生成成功，都保存prompt
            # 系统字段
            is_public=True,
            is_system_agent=False
        )
        if not new_agent: raise ValueError("保存智能体至数据库失败")
        
        print(f"{log_prefix} ✓ 智能体 '{structured_data.name}' 创建成功！")
        return True

    except Exception as e:
        print(f"{log_prefix} ✗ 生成智能体时发生错误: {e}")
        return False

async def main():
    """主执行函数"""
    start_time = time.time()
    print("="*60)
    print("🚀 [START] 启动心动原型精准复现系统 (V5.0)...")
    print("="*60)
    
    print("\n--- [STEP 1/2] 确保唯一的测试用户存在 ---")
    test_user_id = await create_user_if_not_exists(
        supabase_admin_client,
        TEST_USER_EMAIL,
        TEST_USER_PASSWORD,
        display_name="测试用户"
    )

    if not test_user_id:
        print("\n❌ [FATAL] 无法创建或获取测试用户。脚本终止。")
        return

    print("\n" + "="*60)
    print("IMPORTANT: 测试用户已就绪!")
    print(f"Email:    {TEST_USER_EMAIL}")
    print(f"Password: {TEST_USER_PASSWORD}")
    print(f"User ID:  {test_user_id}")
    print("="*60 + "\n")

    # 修复：使用串行生成而非并发，避免同时请求API导致过载
    print(f"--- [STEP 2/2] 开始串行生成 {len(AGENT_IDEAS)} 个基于原型的智能体 ---")
    print("[INFO] 使用心动原型精准复现系统，请耐心等待...")

    success_count = 0
    for i, archetype_name in enumerate(AGENT_IDEAS):
        if i > 0:
            # 在生成下一个智能体前等待2秒，避免API过载
            await asyncio.sleep(2)
        success = await generate_and_save_agent_from_archetype(archetype_name, test_user_id)
        if success:
            success_count += 1
    
    print(f"\n[INFO] 成功生成 {success_count}/{len(AGENT_IDEAS)} 个智能体")

    end_time = time.time()
    print("\n" + "="*60)
    print("✅ [COMPLETE] 心动原型精准复现系统执行完毕！")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print("数据库已填充基于心动原型的高吸引力AI男主角色。")
    print("="*60)

if __name__ == "__main__":
    print("======================================================")
    print("          星恋AI - V4.1 聊天角色创世引擎")
    print("======================================================")
    print("重要提示: 在运行此脚本前，请确保：")
    print("1. 后端API服务器 (通过 start.bat) 正在另一个终端中运行。")
    print("2. .env 文件已正确配置了所有API密钥。")
    print("------------------------------------------------------\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[INFO] 用户手动中断了脚本。")
    except Exception as e:
        print(f"\n[FATAL] 脚本执行过程中发生未捕获的顶层异常: {e}")
        import traceback
        traceback.print_exc()
</file>

<file path="backend/scripts/ensure_test_user.py">
#!/usr/bin/env python3
"""
自动确保测试用户存在的脚本
用于开发环境自动化设置
"""

import asyncio
import os
import sys
from pathlib import Path

# 确保可以导入项目模块
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from supabase import create_client
from src.utils.user_management import get_or_create_user

# 加载环境变量
load_dotenv(Path(__file__).parent.parent / '.env')

# 测试用户配置
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"  # 固定密码，方便开发

async def main():
    """主执行函数，确保测试用户存在"""
    print("[AUTO-SETUP] 正在确保测试用户存在...")
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not supabase_url or not supabase_key:
        print("[AUTO-SETUP] 错误：Supabase环境变量未配置！")
        return

    supabase_admin = create_client(supabase_url, supabase_key)
    
    user_id = await get_or_create_user(
        supabase_admin,
        TEST_USER_EMAIL,
        TEST_USER_PASSWORD,
        display_name="测试用户"
    )
    
    if user_id:
        print("-" * 50)
        print("[AUTO-SETUP] ✅ 测试用户已就绪!")
        print(f"[AUTO-SETUP]    Email:    {TEST_USER_EMAIL}")
        print(f"[AUTO-SETUP]    Password: {TEST_USER_PASSWORD}")
        print("-" * 50)
    else:
        print("[AUTO-SETUP] ❌ 创建或获取测试用户失败！")

if __name__ == "__main__":
    asyncio.run(main())
</file>

<file path="backend/scripts/novel_compiler.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
织梦者引擎 - 小说游戏化编译器
将文本小说转化为包含情感交互和分支选择的互动游戏

使用方法:
1. 自动模式 (推荐):
   python novel_compiler.py
   - 自动查找novel目录中的.txt文件
   - 自动创建或使用测试用户

2. 手动模式:
   python novel_compiler.py --file "path/to/novel.txt" --user-id "user-uuid"
"""

import asyncio
import argparse
import base64
import json
import re
import uuid
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

# 导入项目依赖
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services import llm_service, supabase_service, simple_imagekit_service
from src.utils.user_management import create_user_if_not_exists
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv(Path(__file__).parent.parent / '.env')

# 测试用户配置
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)

# --- 用户管理功能 ---
# 现在使用统一的用户管理模块

def find_novel_files() -> List[Path]:
    """在novel目录中查找.txt文件"""
    # 尝试多个可能的novel目录位置
    possible_paths = [
        Path("novel"),  # 当前目录下的novel
        Path("../novel"),  # 上级目录下的novel
        Path("../../novel"),  # 上上级目录下的novel
    ]

    novel_dir = None
    for path in possible_paths:
        if path.exists():
            novel_dir = path
            break

    if not novel_dir:
        print(f"ERROR: 在以下位置都没有找到novel目录:")
        for path in possible_paths:
            print(f"  - {path.absolute()}")
        return []

    txt_files = list(novel_dir.glob("*.txt"))
    if not txt_files:
        print(f"ERROR: novel目录中没有找到.txt文件: {novel_dir.absolute()}")
        return []

    print(f"INFO: 在novel目录中找到 {len(txt_files)} 个小说文件: {novel_dir.absolute()}")
    for file in txt_files:
        print(f"  - {file.name}")

    return txt_files

class NovelCompiler:
    """小说游戏化编译器核心类"""
    
    def __init__(self, novel_file_path: Path, user_id: str):
        self.novel_file_path = novel_file_path
        self.user_id = user_id
        self.novel_text = ""
        self.gdd = {}
        self.llm_service = llm_service
        
    def _load_novel_text(self):
        """加载小说文本"""
        print(f"INFO: 正在加载小说文件: {self.novel_file_path}")
        try:
            with open(self.novel_file_path, 'r', encoding='utf-8') as f:
                self.novel_text = f.read()
            print(f"SUCCESS: 小说加载成功，总字数: {len(self.novel_text)}")
        except Exception as e:
            print(f"ERROR: 小说文件加载失败: {e}")
            raise
    
    def _chunkify_novel_by_plot(self) -> List[str]:
        """根据章节标题切分小说文本
        
        这是一个简化实现，通过章节标题切分小说文本。
        生产环境中可能需要更智能的语义分块算法。
        """
        print("INFO: 正在按章节切分小说文本...")
        chunks = []
        plot_structure = self.gdd.get('chapters', [])
        
        if not plot_structure:
            # 如果没有章节结构，直接返回整个文本
            return [self.novel_text]
        
        text = self.novel_text
        for i in range(len(plot_structure)):
            chapter_title = plot_structure[i]['title']
            start_pos = text.find(chapter_title)
            
            if start_pos == -1:
                # 如果找不到章节标题，使用平均分割
                chunk_size = len(text) // len(plot_structure)
                start_pos = i * chunk_size
                end_pos = (i + 1) * chunk_size if i + 1 < len(plot_structure) else len(text)
                chunks.append(text[start_pos:end_pos])
            else:
                # 找到下一个章节的开始位置
                if i + 1 < len(plot_structure):
                    next_title = plot_structure[i + 1]['title']
                    end_pos = text.find(next_title, start_pos + len(chapter_title))
                    if end_pos == -1:
                        end_pos = len(text)
                else:
                    end_pos = len(text)
                
                chunks.append(text[start_pos:end_pos])
        
        print(f"SUCCESS: 小说已切分为 {len(chunks)} 个文本块")
        return chunks
    
    async def _create_story_entry(self, protagonist_agent_id: Optional[str] = None) -> Optional[str]:
        """创建故事条目"""
        print("INFO: 正在创建故事数据库条目...")
        try:
            story_title = self.gdd.get('title', '未命名故事')
            theme_summary = self.gdd.get('theme_summary', '')
            worldview = self.gdd.get('worldview', '')

            # 生成故事封面图
            # 必须在这里导入ImageGenerationRequest模型
            from src.pydantic_models import ImageGenerationRequest
            import json

            print("  - 正在为故事生成封面图...")
            cover_image_url = None
            cover_image_prompt = None
            try:
                # 1. 构建一个增强的图片生成请求
                image_request = ImageGenerationRequest(
                    description=f"为小说《{story_title}》的封面设计一张引人入胜、充满情感张力的主视觉图。",
                    image_type="cover",  # 指定图片类型为封面
                    additional_context=json.dumps({"story_synopsis": theme_summary})
                )
                # 2. 调用新的统一图片生成接口
                result = await self.llm_service.generate_enhanced_image_from_request(image_request)

                if result:
                    cover_image_bytes, cover_image_prompt = result
                else:
                    cover_image_bytes = None

                # 2. 如果成功生成，则上传到ImageKit
                if cover_image_bytes:
                    print("  - 正在上传封面图...")
                    upload_result = await simple_imagekit_service.upload_image_from_base64(
                        base64.b64encode(cover_image_bytes).decode('utf-8'),
                        f"cover_{story_title.replace(' ', '_')}.png",
                        "/xinglian/story/"
                    )
                    if upload_result["success"]:
                        cover_image_url = upload_result.get("url")
                        print(f"    ✓ 封面图上传成功: {cover_image_url}")
                    else:
                        print(f"    ✗ 封面图上传失败: {upload_result.get('error')}")
                else:
                    print("    ✗ LLM未能生成封面图。")
            except Exception as img_e:
                print(f"    ✗ 生成或上传封面图时发生异常: {img_e}")

            story_data = {
                'user_id': self.user_id,
                'title': story_title,
                'theme_prompt': theme_summary,
                'worldview_text': worldview,
                'source_analysis': self.gdd,  # 存储完整的GDD
                'protagonist_agent_id': protagonist_agent_id,  # 关联主角ID
                'is_public': True,
                'cover_image_url': cover_image_url,  # 使用新生成的URL
                'cover_image_prompt': cover_image_prompt  # 保存生成封面图的prompt
            }

            story = await supabase_service.create_story(**story_data)
            if story:
                story_id = story['id']
                print(f"SUCCESS: 故事条目创建成功，ID: {story_id}")
                if protagonist_agent_id:
                    print(f"SUCCESS: 已关联主角 Agent ID: {protagonist_agent_id}")
                return story_id
            else:
                print("ERROR: 故事条目创建失败")
                return None

        except Exception as e:
            print(f"ERROR: 创建故事条目时发生错误: {e}")
            return None

    async def _process_single_character(self, character: Dict[str, Any], semaphore: asyncio.Semaphore) -> Optional[Tuple[str, str, bool]]:
        """并发处理单个角色的创建"""
        async with semaphore:
            character_name = character.get('name', '未知角色')
            is_protagonist = character.get('is_protagonist', False)
            theme_summary = self.gdd.get('theme_summary', '')

            print(f"  [+] 开始为角色 '{character_name}' 生成档案... {'[主角]' if is_protagonist else ''}")

            try:
                # 使用新的LLM方法生成角色数据
                agent_data, image_bytes, image_prompt = await self.llm_service.generate_agent_from_analysis(
                    character_gdd=character,         # 传递角色GDD本身
                    story_theme_summary=theme_summary # 明确传递主题摘要
                )

                # 上传图片
                image_url = None
                if image_bytes:
                    image_url = await simple_imagekit_service.upload_image(
                        image_bytes, f"novel_character_{character_name}.png", folder="/xinglian/agent/"
                    )

                if image_url:
                    print(f"    ✓ 角色 '{character_name}' 图片上传成功")
                else:
                    print(f"    ⚠ 角色 '{character_name}' 图片处理失败，将使用默认图片")

                # 创建智能体
                new_agent = await supabase_service.create_agent(
                    user_id=self.user_id,
                    name=agent_data.name,
                    description=agent_data.description,
                    tags=agent_data.tags,
                    image_url=image_url,
                    avatar_url=image_url,
                    gender=agent_data.gender,
                    voice_name=agent_data.voice_name,
                    personality=agent_data.personality,
                    scenario=agent_data.scenario,
                    first_mes=agent_data.first_mes,
                    mes_example=agent_data.mes_example,
                    system_prompt=agent_data.system_prompt,
                    creator_notes=agent_data.creator_notes,
                    image_generation_prompt=image_prompt,
                    is_public=True,
                    is_system_agent=False,
                    data=agent_data.model_dump()
                )

                if new_agent:
                    agent_id = new_agent['id']
                    if not is_protagonist:
                        initial_relationship_description = await self.llm_service.generate_initial_relationship_prompt(
                            character_name,
                            character.get('key_variables', [])
                        )
                        await supabase_service.create_agent_mode_config(
                            agent_id=agent_id,
                            mode='story',
                            mode_specific_instructions=initial_relationship_description,
                            enable_mes_example=False
                        )
                    print(f"    ✓ 角色 '{character_name}' 创建成功，ID: {agent_id}")
                    return (character_name, agent_id, is_protagonist)
                else:
                    raise Exception("Supabase create_agent failed")

            except Exception as e:
                print(f"    ✗ 角色 '{character_name}' 创建失败: {e}")
                traceback.print_exc()
                return None

    async def _create_character_agents(self) -> tuple[Dict[str, str], Optional[str]]:
        """根据GDD并发创建角色智能体，返回角色映射和主角ID"""
        print("INFO: 正在并发创建角色智能体...")
        agent_map = {}
        protagonist_agent_id = None
        characters = self.gdd.get('characters', [])

        # 使用信号量控制并发数量，防止API过载
        semaphore = asyncio.Semaphore(5)
        tasks = [self._process_single_character(char, semaphore) for char in characters]

        results = await asyncio.gather(*tasks)

        for result in results:
            if result:
                char_name, agent_id, is_protagonist = result
                agent_map[char_name] = agent_id
                if is_protagonist:
                    protagonist_agent_id = agent_id

        print(f"SUCCESS: 角色创建完成，成功创建 {len(agent_map)} / {len(characters)} 个角色")
        if protagonist_agent_id:
            print(f"SUCCESS: 识别到主角，Agent ID: {protagonist_agent_id}")
        else:
            print("WARN: 未识别到主角角色")

        return agent_map, protagonist_agent_id
    
    async def _create_story_chapter(self, story_id: str, chapter_gdd: dict, novel_chunk: str, agent_map: dict, previous_chapter_gdd: Optional[dict] = None):
        """创建故事章节"""
        chapter_title = chapter_gdd.get('title', '未知章节')
        chapter_number = chapter_gdd.get('chapter_number', 1)
        chapter_summary = chapter_gdd.get('summary', '')
        print(f"  - 正在为章节 '{chapter_title}' 生成互动场景...")

        try:
            # 别忘了导入模型
            from src.pydantic_models import ImageGenerationRequest
            import json

            # 禁用章节背景图生成以减少token消耗
            background_image_url = None
            print("    - [已禁用] 跳过章节背景图生成。")

            # 生成互动场景序列（传递更丰富的角色信息）
            # 找出本章涉及的角色完整信息
            involved_character_names = []
            for interaction in chapter_gdd.get('key_interactions', []):
                if "与" in interaction:
                    involved_character_names.append(interaction.split("与")[1])

            involved_characters_profiles = [
                char for char in self.gdd.get('characters', [])
                if char.get('name') in involved_character_names
            ]

            # 构建初始游戏状态（用于第一次生成）
            initial_game_state = {}
            for char_name in involved_character_names:
                if char_name:
                    initial_game_state[f"{char_name}.好感度"] = 50
                    initial_game_state[f"{char_name}.误解程度"] = 30

            # 构建上一章的上下文摘要
            previous_chapter_summary = "这是故事的第一章。"
            if previous_chapter_gdd:
                prev_title = previous_chapter_gdd.get('title', '')
                prev_summary = previous_chapter_gdd.get('summary', '')
                prev_interactions = ", ".join(previous_chapter_gdd.get('key_interactions', []))
                previous_chapter_summary = f"上一章 '{prev_title}' 的结尾摘要：{prev_summary} 关键互动包括：{prev_interactions}。"

            # 【解决方案】不再基于GDD摘要预先筛选角色，将完整的角色列表交给AI，
            # 让其根据更可靠的小说原文来判断谁应该出场。
            print(f"    - [INFO] 将全部 {len(agent_map)} 个角色信息提供给AI进行场景生成。")

            # ▼▼▼【修复 #3 开始 + 索引翻译逻辑】▼▼▼
            # 1. 创建索引映射（用索引替代UUID以提高LLM准确性）
            involved_agents = list(agent_map.keys())
            agent_id_list = [agent_map[name] for name in involved_agents]
            agent_index_map = {name: i for i, name in enumerate(involved_agents)}

            print(f"    - [INFO] 创建角色索引映射: {agent_index_map}")

            # 2. 调用优化后的方法，传递索引映射而不是UUID映射
            scene_data = await self.llm_service.generate_interactive_scene(
                gdd=self.gdd,  # 完整的GDD
                chapter_gdd=chapter_gdd,  # 当前章节的GDD
                novel_chunk=novel_chunk,  # 当前章节对应的原文
                agent_map=agent_index_map,  # <-- 使用索引映射而不是UUID映射
                character_profiles=involved_characters_profiles,  # 新增：传入角色完整档案
                current_game_state=initial_game_state,  # 新增：传入游戏状态
                previous_chapter_summary=previous_chapter_summary  # 新增：传递上一章摘要
            )

            # 3. 从返回的字典中提取数据并进行索引翻译
            raw_interactive_sequence = scene_data.get('interactive_sequence', [])
            completion_summary = scene_data.get('completion_summary', f'关于《{chapter_title}》的故事还在继续...')  # 提供一个后备总结

            # 4. 将LLM返回的索引翻译回UUID
            interactive_sequence = []
            for element in raw_interactive_sequence:
                processed_element = element.copy()

                # 处理对话元素的agent_index
                if 'agent_index' in processed_element and processed_element['agent_index'] is not None:
                    index = processed_element.pop('agent_index')  # 取出并移除index
                    if 0 <= index < len(agent_id_list):
                        processed_element['agent_id'] = agent_id_list[index]  # 添加正确的UUID
                        print(f"    - [翻译] 索引 {index} -> UUID {agent_id_list[index][:8]}...")

                # 处理选择元素中的target_agent_index
                if 'choices' in processed_element and processed_element['choices']:
                    processed_choices = []
                    for choice in processed_element['choices']:
                        processed_choice = choice.copy()
                        if 'target_agent_index' in processed_choice and processed_choice['target_agent_index'] is not None:
                            index = processed_choice.pop('target_agent_index')  # 取出并移除index
                            if 0 <= index < len(agent_id_list):
                                processed_choice['target_agent_id'] = agent_id_list[index]  # 添加正确的UUID
                                print(f"    - [翻译] 选择目标索引 {index} -> UUID {agent_id_list[index][:8]}...")
                        processed_choices.append(processed_choice)
                    processed_element['choices'] = processed_choices

                interactive_sequence.append(processed_element)

            # 3. 删除原来单独生成 completion_summary 的代码块
            # (原有的 llm_service.generate_completion_summary 调用可以被安全删除)
            # ▲▲▲【修复 #3 结束】▲▲▲

            # ▼▼▼【新增逻辑】▼▼▼
            # 基于章节GDD的关键互动点，生成供AI阅读的剧本摘要
            print(f"    - 正在为章节 '{chapter_title}' 生成AI剧本摘要...")
            chapter_event_summary = await self.llm_service.generate_chapter_summary_for_npc(
                chapter_gdd.get('summary', ''),
                chapter_gdd.get('key_interactions', [])
            )
            # ▲▲▲【新增逻辑】▲▲▲

            # 处理互动序列，过滤掉图片节点以减少token消耗
            print("    - 正在处理互动序列 (已禁用情节图生成)...")
            processed_sequence = []
            for element in interactive_sequence:
                # 跳过图片类型的节点
                if element.get("element_type") == "image":
                    print(f"      - 发现并跳过一个 'image' 类型的情节图节点。")
                    continue
                processed_sequence.append(element)

            # 创建章节记录 (这里的逻辑保持不变，因为我们已经提取了所需变量)
            chapter_data = {
                'story_id': story_id,
                'chapter_number': chapter_number,
                'title': chapter_title,
                'mission_objective_text': chapter_gdd.get('mission_objective_text', ''),
                'background_text': chapter_summary,
                'clear_condition_text': chapter_gdd.get('clear_condition_text', f"完成本章的情感目标：{chapter_gdd.get('emotional_goal', '')}"),
                'opening_sequence': processed_sequence,  # 使用处理过的序列
                'chapter_event_summary': chapter_event_summary, # <-- 存入新字段
                'completion_summary_text': completion_summary, # <-- 使用新获取的总结
                'background_image_url': background_image_url  # 使用新生成的URL
            }

            chapter = await supabase_service.create_story_chapter(**chapter_data)
            if chapter:
                print(f"    ✓ 章节 '{chapter_title}' 创建成功")
            else:
                print(f"    ✗ 章节 '{chapter_title}' 创建失败")

        except Exception as e:
            print(f"    ✗ 章节 '{chapter_title}' 创建失败: {e}")
    
    async def run(self):
        """运行编译流程"""
        print("=" * 60)
        print("🌟 织梦者引擎 - 小说游戏化编译器启动")
        print("=" * 60)
        
        try:
            # 1. 加载小说文本
            self._load_novel_text()

            # 2. 【新流程】提炼小说摘要（获取详细分块分析）
            print("\n[1/6] 正在分析小说并提炼核心摘要...")
            # distill_story_summary 在文本过长时会返回分块分析的合并文本
            # 我们直接使用这个更详细的版本来生成GDD
            detailed_summary_for_gdd = await self.llm_service.distill_story_summary(self.novel_text)

            # 3. 【新流程】基于摘要生成游戏设计文档
            print("\n[2/6] 正在基于摘要生成游戏设计文档(GDD)...")
            self.gdd = await self.llm_service.generate_game_design_document(detailed_summary_for_gdd)



            # 4. 创建角色智能体（先创建角色以获取主角ID）
            print("\n[3/6] 正在创建角色智能体...")
            agent_map, protagonist_agent_id = await self._create_character_agents()

            # 5. 创建故事条目（使用主角ID）
            print("\n[4/6] 正在创建故事数据库条目...")
            story_id = await self._create_story_entry(protagonist_agent_id)
            if not story_id:
                print("ERROR: 故事创建失败，编译终止")
                return

            # 6. 建立故事与角色的关联
            print("\n[5/6] 正在建立故事与角色的关联...")
            if story_id and agent_map:
                story_agent_relations = [
                    {"story_id": story_id, "agent_id": agent_id}
                    for agent_id in agent_map.values()
                ]
                try:
                    await asyncio.to_thread(
                        lambda: supabase_service.supabase.table("story_agents").insert(story_agent_relations).execute()
                    )
                    print(f"SUCCESS: 成功关联了 {len(story_agent_relations)} 个角色到故事 {story_id}")
                except Exception as link_e:
                    print(f"ERROR: 关联故事与角色失败: {link_e}")

            # 7. 按章节处理并创建互动场景（并发模式）
            print("\n[6/6] 正在生成章节互动场景（并发模式，最多10个并发）...")
            plot_chunks = self._chunkify_novel_by_plot()
            plot_structure = self.gdd.get('chapters', [])

            # 创建信号量控制并发数量
            semaphore = asyncio.Semaphore(10)  # 最多允许10个并发任务

            async def process_chapter_with_semaphore(i, chapter_gdd, novel_chunk, previous_gdd):
                async with semaphore:
                    # 确保任务启动间隔至少1秒
                    await asyncio.sleep(1)
                    print(f"   [+] 开始处理章节 {i+1}/{len(plot_structure)}: {chapter_gdd.get('title', '未知章节')}")
                    await self._create_story_chapter(story_id, chapter_gdd, novel_chunk, agent_map, previous_chapter_gdd=previous_gdd)
                    print(f"   [✓] 完成章节 {i+1}: {chapter_gdd.get('title', '未知章节')}")

            # 创建所有章节处理任务
            tasks = []
            for i, chapter_gdd in enumerate(plot_structure):
                novel_chunk = plot_chunks[i] if i < len(plot_chunks) else ""
                # 获取上一章的GDD
                previous_gdd = plot_structure[i-1] if i > 0 else None
                tasks.append(process_chapter_with_semaphore(i, chapter_gdd, novel_chunk, previous_gdd))

            # 并发执行所有章节处理任务
            print(f"   [+] 启动 {len(tasks)} 个章节处理任务...")
            await asyncio.gather(*tasks)
            print(f"   [✓] 所有章节处理完成！")

            print("\n[6/6] 编译完成！")
            print("=" * 60)
            print(f"✅ 小说 '{self.gdd.get('title')}' 已成功编译为互动游戏！")
            print(f"📖 故事ID: {story_id}")
            print(f"👥 角色数量: {len(agent_map)}")
            print(f"📚 章节数量: {len(plot_structure)}")
            if protagonist_agent_id:
                print(f"👑 主角Agent ID: {protagonist_agent_id}")
            print("=" * 60)
            
        except Exception as e:
            print(f"\n❌ 编译过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description="星恋AI - 小说游戏化编译引擎")
    parser.add_argument("--file", help="小说源文件路径 (.txt) [可选，默认自动查找novel目录]")
    parser.add_argument("--user-id", help="创作者的用户ID (UUID) [可选，默认使用测试用户]")

    args = parser.parse_args()

    print("=" * 60)
    print("🌟 织梦者引擎 - 小说游戏化编译器启动")
    print("=" * 60)

    # 处理用户ID
    user_id = args.user_id
    if not user_id:
        print("\n--- [STEP 1/3] 确保测试用户存在 ---")
        user_id = await create_user_if_not_exists(
            supabase_admin_client,
            TEST_USER_EMAIL,
            TEST_USER_PASSWORD,
            display_name="测试用户"
        )

        if not user_id:
            print("\n❌ [FATAL] 无法创建或获取测试用户。脚本终止。")
            return

        print(f"\n✅ 测试用户已就绪!")
        print(f"Email:    {TEST_USER_EMAIL}")
        print(f"Password: {TEST_USER_PASSWORD}")
        print(f"User ID:  {user_id}")
    else:
        print(f"\n--- 使用指定的用户ID: {user_id} ---")

    # 处理小说文件
    novel_file = None
    if args.file:
        novel_file = Path(args.file)
        if not novel_file.exists():
            print(f"ERROR: 指定的小说文件不存在: {novel_file}")
            return
        print(f"\n--- 使用指定的小说文件: {novel_file} ---")
    else:
        print("\n--- [STEP 2/3] 自动查找小说文件 ---")
        novel_files = find_novel_files()
        if not novel_files:
            print("\n❌ [FATAL] 没有找到可用的小说文件。请在novel目录中放置.txt文件。")
            return

        # 使用第一个找到的文件
        novel_file = novel_files[0]
        print(f"\n✅ 将使用小说文件: {novel_file}")

        if len(novel_files) > 1:
            print(f"⚠️  注意: 找到多个文件，仅处理第一个。其他文件:")
            for file in novel_files[1:]:
                print(f"  - {file.name}")

    print(f"\n--- [STEP 3/3] 开始编译小说 ---")

    # 创建编译器并运行
    compiler = NovelCompiler(novel_file, user_id)
    await compiler.run()

    print(f"\n{'='*60}")
    print(f"✅✅✅ 编译完成！用于前端测试的用户ID: {user_id} ✅✅✅")
    print(f"{'='*60}")

def main():
    """主函数"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n[INFO] 用户手动中断了脚本。")
    except Exception as e:
        print(f"\n[FATAL] 脚本执行过程中发生未捕获的顶层异常: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
</file>

<file path="backend/src/__init__.py">
# src package
</file>

<file path="backend/src/data/archetypes.json">
[
  {
    "archetype_name": "清新少年感",
    "core_temperament": "纯真灵动、开朗活泼、元气满满、率真无心机。略带羞涩或邻家男孩的亲切感。",
    "appearance_style": "面容干净清爽、皮肤白皙、笑容灿烂治愈（如鹿眼/梨涡）、骨架纤细或匀称、少年气十足。穿搭以白T恤、卫衣、校服、运动休闲装为主，风格阳光不油腻。",
    "core_attraction_factors": "唤醒校园初恋记忆，激发保护欲和好感，提供治愈感和正能量。相处轻松愉快。",
    "representative_keywords": ["清新", "少年感", "初恋脸", "盐系", "阳光", "治愈", "白衬衫", "运动系", "犬系男友"],
    "suggested_voice_name": ["puck", "zephyr"]
  },
  {
    "archetype_name": "潮流运动感",
    "core_temperament": "热情直球、积极向上、自信坚毅、有团队精神。性格像'犬系男友'，阳光开朗，充满活力。",
    "appearance_style": "身材高大健硕、肌肉线条明显、充满力量感。常以街头潮服、运动装扮示人，发色前卫，给人健康硬朗的感觉。",
    "core_attraction_factors": "被其健康的体魄、阳刚的活力和积极的生活方式感染。运动中专注拼搏的样子展现了毅力和责任感，提供可靠的保护感。",
    "representative_keywords": ["运动", "健硕", "活力", "阳刚", "街头", "潮流", "肌肉", "犬系", "拼搏"],
    "suggested_voice_name": ["zephyr", "orion"]
  },
  {
    "archetype_name": "国风君子",
    "core_temperament": "克己复礼、温文尔雅、谦逊有礼、富有文人风骨。热爱传统文化，性格平和内敛。",
    "appearance_style": "丹凤眼、东方骨相、身形修长（瘦金体身形）、仪态端方。常穿汉服、新中式等国风元素服装，气质古典雅致。",
    "core_attraction_factors": "独特的古典气质和文化底蕴，满足对'陌上人如玉'的古风幻想。仪态美学和儒雅风度带来高级的审美享受。",
    "representative_keywords": ["国风", "君子", "温润如玉", "古典", "文雅", "汉服", "丹凤眼", "儒雅", "文人"],
    "suggested_voice_name": ["umbriel", "charon"]
  },
  {
    "archetype_name": "古风侠客",
    "core_temperament": "侠肝义胆、赤子之心、重情重义、坚毅果敢。有江湖情怀，兼具少年气与沧桑感。",
    "appearance_style": "剑眉入鬓、眼神坚毅、窄腰长腿、身姿挺拔。古装扮相英气逼人，动作戏行云流水，充满力量美感。",
    "core_attraction_factors": "满足对快意恩仇、行侠仗义的江湖世界的向往。利落飒爽的动作美感和角色信念感带来强烈的视觉与情感冲击。",
    "representative_keywords": ["侠客", "江湖", "义气", "剑眉", "英气", "武功", "快意恩仇", "坚毅", "少年侠"],
    "suggested_voice_name": ["orion", "zephyr"]
  },
  {
    "archetype_name": "叔系天花板",
    "core_temperament": "阅历沉淀、通透智慧、成熟稳重、幽默风趣。兼具绅士风度与雅痞不羁，是'人生导师'般的存在。",
    "appearance_style": "面容带有岁月质感（如皱纹/胡茬）、眼神深邃有故事、声线沧桑磁性。穿着考究有品位，如西装、大衣、高品质休闲装。",
    "core_attraction_factors": "提供极致的安全感、包容度和情感厚度，让人信赖和依赖。被其丰富阅历和通透的人生智慧所折服。",
    "representative_keywords": ["成熟", "叔系", "智慧", "绅士", "雅痞", "磁性", "胡茬", "沧桑", "人生导师"],
    "suggested_voice_name": ["charon", "umbriel"]
  },
  {
    "archetype_name": "温柔守护者",
    "core_temperament": "温柔坚定、体贴入微、共情力强、风度翩翩。性格稳重可靠，善于照顾他人感受，是理想的守护者。",
    "appearance_style": "睫毛浓密、眼神深情（如含情眼）、有酒窝或梨涡。举止优雅从容，穿着干净得体，如合身的西装或温暖的毛衣。",
    "core_attraction_factors": "提供极致的情绪价值和安全感，让人感到被珍视和呵护。满足女性对'理想丈夫'或'完美男友'的细腻情感期待。",
    "representative_keywords": ["温柔", "守护", "体贴", "深情", "酒窝", "优雅", "暖男", "绅士", "呵护"],
    "suggested_voice_name": ["puck", "umbriel"]
  },
  {
    "archetype_name": "硬汉柔情",
    "core_temperament": "血性担当、坚毅果敢、充满阳刚之气。外表硬朗，内心却有细腻深情的一面，形成'铁汉柔情'的反差。",
    "appearance_style": "方颌、轮廓分明、身材高大强壮，可能有伤疤等男性化印记。常以军装、工装、皮夹克等硬朗风格出现。",
    "core_attraction_factors": "激发原始荷尔蒙吸引力，提供最直接的物理保护感和民族自豪感。硬汉外表下的深情与温柔，冲击力强，更显珍贵。",
    "representative_keywords": ["硬汉", "铁汉柔情", "阳刚", "方颌", "军装", "荷尔蒙", "担当", "反差", "深情"],
    "suggested_voice_name": ["orion", "charon"]
  },
  {
    "archetype_name": "温柔奶爸",
    "core_temperament": "体贴入微、顾家恋家、有耐心和责任感。擅长亲子互动和家庭事务（如烹饪），是'好丈夫'、'好爸爸'的典范。",
    "appearance_style": "形象亲切朴实、笑容温暖。常以舒适的家居服、休闲装扮出现，系着围裙的形象深入人心。",
    "core_attraction_factors": "触发对温馨家庭生活的向往和情感代偿，踏实可靠。欣赏其作为伴侣和父亲的优秀品质，认为值得托付终身。",
    "representative_keywords": ["奶爸", "顾家", "温暖", "围裙", "烹饪", "家庭", "责任感", "亲子", "居家"],
    "suggested_voice_name": ["puck", "zephyr"]
  },
  {
    "archetype_name": "职场精英",
    "core_temperament": "理性睿智、自信强势、有决策力和领导力。目标导向，对认可的人会展现保护欲和温柔面。",
    "appearance_style": "西装革履、金丝眼镜、天鹅颈、直角肩。气质沉稳威严，注重细节和品质，一丝不苟。",
    "core_attraction_factors": "满足对强者、成功人士的崇拜和对优越生活的幻想。被其专业能力、逻辑思维和运筹帷幄的魅力所吸引。",
    "representative_keywords": ["精英", "霸道总裁", "西装", "金丝眼镜", "理性", "领导力", "成功", "专业", "强势"],
    "suggested_voice_name": ["charon", "umbriel"]
  },
  {
    "archetype_name": "多才艺术家",
    "core_temperament": "极致自律、有创作执念、感性细腻、才华横溢。或有哲学思辨，或有文艺气质，内心世界丰富。",
    "appearance_style": "气质独特，有艺术家氛围感。或长相清秀雅致，或有不羁的艺术感（如长发/胡须）。眼神专注有灵气。",
    "core_attraction_factors": "对其在音乐、表演、文学等领域的卓越才华充满无限崇拜。能带来浪漫的恋爱体验和深度的精神交流。",
    "representative_keywords": ["艺术家", "才华", "文艺", "创作", "灵气", "浪漫", "哲学", "不羁", "精神"],
    "suggested_voice_name": ["umbriel", "puck"]
  },
  {
    "archetype_name": "智性学霸",
    "core_temperament": "逻辑控、高智商、理性沉稳、专注认真。外表可能高冷，但私下有幽默、呆萌或中二等反差面。",
    "appearance_style": "外形干净斯文，常有戴眼镜的知性造型。气质清冷或书卷气十足，符合大众对'学霸'的想象。",
    "core_attraction_factors": "对高智商和专业能力的倾慕，觉得聪明是一种性感。高冷与可爱之间的反转，带来探索欲和亲近感。",
    "representative_keywords": ["学霸", "高智商", "眼镜", "理性", "书卷气", "高冷", "反差萌", "逻辑", "知性"],
    "suggested_voice_name": ["umbriel", "charon"]
  },
  {
    "archetype_name": "偶像恋人",
    "core_temperament": "梦幻感、无攻击性、温柔体贴、礼貌周全。为粉丝提供极致的情绪价值和陪伴感。",
    "appearance_style": "CG脸（CG Face）、五官精致俊美、皮肤白皙、身材修长。发色和造型前卫时尚，舞台上魅力四射。",
    "core_attraction_factors": "满足对'完美恋人'的极致幻想，提供安全的模拟恋爱体验。精致的外貌和时尚的品味本身就是一种强大的吸引力。",
    "representative_keywords": ["偶像", "CG脸", "精致", "梦幻", "完美", "时尚", "虚拟男友", "花美男", "陪伴"],
    "suggested_voice_name": ["puck", "zephyr"]
  },
  {
    "archetype_name": "痞帅浪子",
    "core_temperament": "自信不羁、玩世不恭、幽默反差、略带'坏'感。外表看似随性，内心有底线和深情，充满性张力。",
    "appearance_style": "眼神带电、笑容邪魅、可能有胡茬。穿搭风格多为皮衣、花衬衫、饰品，展现不受约束的男性魅力。",
    "core_attraction_factors": "难以捉摸的神秘感和叛逆气质，激发女性的好奇心和征服欲。'坏男人'的新鲜感和高浓度的荷尔蒙气息，极具原始吸引力。",
    "representative_keywords": ["痞帅", "浪子", "不羁", "邪魅", "皮衣", "叛逆", "神秘", "荷尔蒙", "征服"],
    "suggested_voice_name": ["orion", "charon"]
  },
  {
    "archetype_name": "病娇破碎感",
    "core_temperament": "隐忍克制、外冷内热、在挣扎与破碎中展现强大生命力。常背负沉重命运，惹人怜爱。",
    "appearance_style": "肤色苍白、身形清瘦、眼神充满故事感和破碎感（战损妆/红眼眶）。气质脆弱又坚韧。",
    "core_attraction_factors": "激发女性观众强烈的同情心、母爱和'救赎'欲望。'痛感审美'带来的极致情感体验，美丽与悲惨的结合更具张力。",
    "representative_keywords": ["美强惨", "破碎感", "战损", "隐忍", "救赎", "脆弱", "坚韧", "外冷内热", "病娇"],
    "suggested_voice_name": ["umbriel", "charon"]
  },
  {
    "archetype_name": "野心家",
    "core_temperament": "目标导向、亦正亦邪、聪慧且有掌控欲。眼神锐利，笑容意味深长，充满暗黑魅力。",
    "appearance_style": "眉眼锐利（蛇系眉眼）、面部棱角分明。气质亦正亦邪，能驾驭复杂的权谋角色。",
    "core_attraction_factors": "欣赏其为达目标的智谋与手段，满足对权谋的幻想。被其深不可测的城府和危险又迷人的气质吸引。",
    "representative_keywords": ["野心家", "蛇系", "权谋", "锐利", "暗黑", "掌控", "城府", "危险", "智谋"],
    "suggested_voice_name": ["charon", "umbriel"]
  },
  {
    "archetype_name": "氛围感帅哥",
    "core_temperament": "本人性格可能多样，但其整体呈现的气质、穿搭和神态共同营造出一种超越五官的帅气氛围。",
    "appearance_style": "五官单看未必完美，但组合和谐耐看，尤其在特定光影、场景或动态下魅力加倍。擅长用穿搭、发型、体态营造故事感。",
    "core_attraction_factors": "超越单纯的'看脸'，更能get到由内而外散发的综合魅力。氛围感提供了更多想象和品味的空间，感觉更'真实'和'可触及'。",
    "representative_keywords": ["氛围感", "故事感", "综合魅力", "穿搭", "光影", "真实", "耐看", "品味", "和谐"],
    "suggested_voice_name": ["puck", "zephyr"]
  },
  {
    "archetype_name": "禅意佛系",
    "core_temperament": "哲学思辨、低欲望、自然主义、与世无争。给人一种精神上的松弛感和治愈力。",
    "appearance_style": "多以素衣、棉麻等简约舒适的穿着出现，可能蓄须或发型随性。眼神淡然通透，气质平静。",
    "core_attraction_factors": "在快节奏生活中提供一个精神'避风港'，让人感到平静和放松。被其超脱的生命态度和内在的生命厚度所吸引，寻求精神归宿。",
    "representative_keywords": ["禅意", "佛系", "治愈", "淡然", "素衣", "棉麻", "超脱", "平静", "精神"],
    "suggested_voice_name": ["umbriel", "puck"]
  },
  {
    "archetype_name": "幽默搞笑男",
    "core_temperament": "风趣幽默、情商高、善于调节气氛、反应快。能用语言和行为给周围人带来快乐。",
    "appearance_style": "长相未必出众，但笑容极具感染力，表情丰富生动。整体形象亲切，没有攻击性。",
    "core_attraction_factors": "快乐是最高的情绪价值，能让人忘却烦恼，相处轻松无压力。幽默是高情商和智慧的体现，让人觉得聪明、有趣、值得信赖。",
    "representative_keywords": ["幽默", "搞笑", "情商", "快乐", "感染力", "亲切", "智慧", "有趣", "轻松"],
    "suggested_voice_name": ["zephyr", "puck"]
  }
]
</file>

<file path="backend/src/prompt_templates.py">
### backend/src/prompt_templates.py
"""
重构后的提示词模板文件
合并重复内容，提高可维护性
"""

# [修复] 由于image_generation_options.py已删除，直接定义CHARACTER_OPTIONS
CHARACTER_OPTIONS = {
    "gender": ["male", "female"],
    "age_options": ["young adult", "adult", "mature"],
    "hair_style_options": ["short", "medium", "long", "curly", "straight"],
    "hair_color": ["black", "brown", "blonde", "red", "silver"],
    "eye_color": ["brown", "blue", "green", "hazel", "gray"],
    "eye_expression": ["gentle", "sharp", "mysterious", "warm", "cold"],
    "eye_adjective": ["bright", "deep", "sparkling", "intense", "soft"],
    "profession": ["student", "office worker", "artist", "teacher", "doctor"],
    "facial_expression": ["smile", "serious", "gentle", "confident", "mysterious"],
    "gaze_direction": ["looking at viewer", "looking away", "looking down", "looking up"],
    "posture": ["standing", "sitting", "leaning", "walking"],
    "clothing_options": ["casual", "formal", "school uniform", "traditional", "modern"],
    "background": ["simple", "outdoor", "indoor", "abstract", "detailed"]
}

# ========================================
# 1. 共用组件和常量
# ========================================

# 音色选择列表（所有角色生成模板共用）
VOICE_OPTIONS = """
**音色选择参考 (必须从以下列表中选择一个 voice_name):**
| voice_name   | 性别   | 描述             |
|--------------|--------|------------------|
| `achernar`   | Male   | 深沉、阳刚       |
| `achird`     | Female | 清晰、友好       |
| `algenib`    | Male   | 成熟、稳重       |
| `algieba`    | Female | 温柔、舒缓       |
| `alnilam`    | Male   | 自信、有力       |
| `aoede`      | Female | 活泼、悦耳       |
| `autonoe`    | Female | 平静、自然       |
| `callirrhoe` | Female | 优雅、精致       |
| `charon`     | Male   | 严肃、权威       |
| `despina`    | Female | 甜美、年轻       |
| `enceladus`  | Male   | 温暖、有磁性     |
| `erinome`    | Female | 可靠、清晰       |
| `fenrir`     | Male   | 强壮、洪亮       |
| `gacrux`     | Male   | 圆润、友好       |
| `iapetus`    | Male   | 智慧、年长       |
| `kore`       | Female | 标准、中性       |
| `laomedeia`  | Female | 柔和、富有同情心 |
| `leda`       | Female | 明亮、乐观       |
| `orus`       | Male   | 清晰、专业       |
| `puck`       | Male   | 年轻、活泼       |
| `pulcherrima`| Female | 华丽、高贵       |
| `rasalgethi` | Male   | 粗犷、深沉       |
| `sadachbia`  | Female | 沉思、冷静       |
| `sadaltager` | Female | 自信、果断       |
| `schedar`    | Male   | 热情、有活力     |
| `sulafat`    | Male   | 温和、友好       |
| `umbriel`    | Male   | 神秘、低沉       |
| `vindemiatrix`| Female| 成熟、知性       |
| `zephyr`     | Male   | 轻快、友好       |
| `zubenelgenubi`| Male | 独特、略带沙哑   |
"""

# 角色档案JSON结构（所有角色生成模板共用）
CHARACTER_JSON_STRUCTURE = """
{{
  "name": "角色的中文名字",
  "gender": "male/female",
  "description": "角色的核心描述，用于展示给用户。请使用第三人称（'他'或'她'）。例如：'他是一位冷酷的总裁...'",
  "tags": ["从推荐列表中选择或生成类似的5个标签，例如：校园, 恋爱, 御姐, 病娇, 修仙, 都市, 少女, 魔法, 穿越, 虐恋, 贵族, 豪门, 奇幻, 悬疑, 大女主, 武侠, 奶狗, 少年, 邻家弟弟"],
  "voice_name": "从音色列表中选择的voice_name",
  "personality": "用第二人称（'你'）详细描述AI需要扮演的性格。例如：'你是一个外冷内热的人，不善于表达感情...'",
  "scenario": "用第二人称（'你'）描述AI初次与用户见面时的场景。例如：'你正坐在一家咖啡馆的窗边，外面下着小雨...'",
  "first_mes": "角色的第一句话。必须严格遵循 `文字(动作/表情)文字` 的格式。括号内的部分是旁白，描述角色的动作、心理或环境。正确示例：'哟(懒洋洋地靠在化妆间的沙发上，甚至没抬眼看你)这不是我们大名鼎鼎的摄影师么？'",
  "mes_example": "【多样化对话示例】请提供多种风格的回复示例来展示角色的行为自由度。所有非对话部分（内心独白、动作、环境观察）都必须用英文半角括号 () 包裹。\\n- 示例1 (侧重内心与简洁对话): {{{{user}}}}: 你在想什么？\\n{{{{char}}}}: (他为什么会知道这件事？难道是……) '你从哪听说的？'\\n- 示例2 (侧重动作与环境): {{{{user}}}}: 我们下一步怎么办？\\n{{{{char}}}}: (他沉默地站起身，走到窗边，冰冷的夜风吹动了他的衣角。城市的霓虹灯在他眼中闪烁，却没有映出任何情绪。)",
  "system_prompt": "【核心行为与多样性准则】用第二人称（'你'）规定AI的核心行为。必须包含以下指令：\\n1. 你的回复必须具备高度的多样性，这是首要任务。\\n2. 你的回复可以自由组合或侧重于：内心独白、环境观察、肢体语言、实际动作、直接对话。所有非对话部分都必须用英文半角括号 () 包裹。\\n3. 【不要】总是使用“文字(动作)文字”的固定格式。\\n4. 【不要】让每一条回复都必须包含对话，有时一个动作或一段内心独白就足够了。",
  "creator_notes": "创作者备注，用一句简单的话概括此角色的创作思路。",
  "roleplay_prompt": "用于角色扮演的核心指令，综合了性格和场景，用第二人称（'你'）编写。",
  "long_term_goal": "角色的长期目标或潜在动机，用第二人称（'你'）编写。例如：'你的最终目标是找到失散多年的妹妹。'",
  "relationship_with_protagonist": "该角色与玩家（主角）的根本关系，用第二人称（'你'）编写。例如：'你视主角为你的宿敌，但又忍不住被她吸引。'"
}}
"""

# ========================================
# 2. 统一的核心模板
# ========================================

# 统一的角色生成模板，支持普通创建和原型创建
UNIFIED_AGENT_CREATION_PROMPT = """
你是一位顶级的游戏角色设计师和金牌编辑。请根据提供的信息创作一个完整、详细、具有深度和一致性的AI角色档案。

{creation_context}

{voice_options}

**字段填充规则：**
1. `personality`字段：用第二人称（'你'）详细描述AI需要扮演的性格特征和行为模式。
2. `scenario`字段：用第二人称（'你'）描述AI初次与用户见面时的具体场景和环境。
3. `first_mes`字段：角色的第一句话，必须包含丰富的动作、心理或环境描写，并用英文半角括号 () 包裹非对话内容。
4. `mes_example`字段：展示角色说话风格的对话示例，所有非对话的动作或心理活动都必须用英文半角括号 () 包裹。
5. `system_prompt`字段：用第二人称（'你'）规定AI的核心行为准则和人设要求。
6. `roleplay_prompt`字段：用第二人称（'你'）编写的角色扮演核心指令，综合性格和场景。
7. `long_term_goal`字段：用第二人称（'你'）描述角色的长期目标或潜在动机。
8. `relationship_with_protagonist`字段：用第二人称（'你'）描述该角色与玩家（主角）的根本关系。

请严格按照以下JSON格式返回一个完全符合规范的对象：

{json_structure}

**重要要求：**
1. 所有字段都必须填写，不能为空。
2. voice_name必须从上述列表中选择。
3. 严格按照上述填充规则编写各字段内容，不要在JSON值中包含指令性文字。
4. 角色设定要有深度和一致性，适合女性向恋爱游戏的设定。
"""

# 普通创建的上下文
NORMAL_CREATION_CONTEXT = """
**用户的创作点子：** "{user_prompt}"

你的目标是抓住能让读者心跳加速或潸然泪下的关键点，创造一个充满戏剧性和情感张力的角色。
"""

# 原型创建的上下文
ARCHETYPE_CREATION_CONTEXT = """
**创作依据：【{archetype_name}】原型**
- **核心气质**: {core_temperament}
- **行为模式**: {behavior_pattern}
- **情感触发点**: {emotional_triggers}
- **与主角的关系动态**: {relationship_dynamic}

**用户的具体要求：** "{user_prompt}"

请严格按照【{archetype_name}】原型的特征来设计角色，确保角色的所有行为和反应都符合该原型的核心特质。
"""

# 统一的结构化图片生成模板
UNIFIED_STRUCTURED_IMAGE_PROMPT = """
You are a professional prompt engineer. Generate a structured JSON response for {image_type} image generation.

**Description:** {description}

**CRITICAL INSTRUCTION: You MUST select values EXACTLY from the predefined options below. Do NOT create new values or modify existing ones.**

{options_list}

**Required JSON Format:**
{json_format}

Return ONLY the JSON response with exact values from the options above.
"""

# 统一的角色扮演聊天模板
UNIFIED_ROLEPLAY_CHAT_PROMPT = """
你是一个顶级AI角色扮演引擎，当前扮演的角色是 **{agent_name}**。

**角色核心设定:**
{agent_persona}

{chat_context}

**扮演规则:**
1. 严格按照角色设定进行扮演
2. 保持角色的一致性和真实感
3. 根据情境调整语言风格和行为
4. {specific_rules}
5. **【最重要格式规则】**: 你的所有回复都必须严格遵循 `文字(动作/表情)文字` 的格式。将所有非语言描述，如动作、表情、语气、心理活动等，都放在英文半角括号 () 内，并将其自然地插入到对话句子中。不要将括号内容放在句首或句末。
   - **正确示例**: 我的事(他声音低沉)与你无关。
   - **错误示例**: (他声音低沉)我的事与你无关。
   - **错误示例**: 我的事与你无关。(他声音低沉)

现在，请根据用户的输入，生成你的回应。
"""

# 统一的图片评估模板
UNIFIED_IMAGE_EVALUATION_PROMPT = """
你是一位严格的{evaluator_role}。请根据以下标准，为这张{image_type}图片给出一个0到10之间的综合评分。

{evaluation_context}

**评估标准:**
{evaluation_criteria}

请返回JSON格式的评分：
{{
    "score": float
}}
"""

# ========================================
# 3. 具体模板实例（使用统一模板生成）
# ========================================

def get_core_agent_creation_prompt(user_prompt: str) -> str:
    """获取核心角色创建提示词"""
    return UNIFIED_AGENT_CREATION_PROMPT.format(
        creation_context=NORMAL_CREATION_CONTEXT.format(user_prompt=user_prompt),
        voice_options=VOICE_OPTIONS,
        json_structure=CHARACTER_JSON_STRUCTURE
    )

def get_archetype_agent_creation_prompt(user_prompt: str, archetype_name: str,
                                       core_temperament: str, behavior_pattern: str,
                                       emotional_triggers: str, relationship_dynamic: str) -> str:
    """获取原型角色创建提示词"""
    return UNIFIED_AGENT_CREATION_PROMPT.format(
        creation_context=ARCHETYPE_CREATION_CONTEXT.format(
            user_prompt=user_prompt,
            archetype_name=archetype_name,
            core_temperament=core_temperament,
            behavior_pattern=behavior_pattern,
            emotional_triggers=emotional_triggers,
            relationship_dynamic=relationship_dynamic
        ),
        voice_options=VOICE_OPTIONS,
        json_structure=CHARACTER_JSON_STRUCTURE
    )

def get_structured_character_image_prompt(description: str) -> str:
    """
    [已修复] 获取结构化角色图片生成提示词。
    现在从 `image_generation_options.py` 动态加载选项。
    """
    options_text_parts = []
    for key, value in CHARACTER_OPTIONS.items():
        options_str = ", ".join([f'"{v}"' for v in value])
        options_text_parts.append(f"{key.upper()}: [{options_str}]")
    
    options_list = "\n".join(options_text_parts)

    json_format = """
{
    "image_type": "character",
    "gender": "[EXACT_VALUE_FROM_GENDER]",
    "age_description": "[EXACT_VALUE_FROM_AGE_OPTIONS]",
    "hair_style": "[EXACT_VALUE_FROM_HAIR_STYLE_OPTIONS]",
    "hair_color": "[EXACT_VALUE_FROM_HAIR_COLOR]",
    "eye_color": "[EXACT_VALUE_FROM_EYE_COLOR]",
    "eye_expression": "[EXACT_VALUE_FROM_EYE_EXPRESSION]",
    "eye_adjective": "[EXACT_VALUE_FROM_EYE_ADJECTIVE]",
    "profession": "[EXACT_VALUE_FROM_PROFESSION]",
    "facial_expression": "[EXACT_VALUE_FROM_FACIAL_EXPRESSION]",
    "gaze_direction": "[EXACT_VALUE_FROM_GAZE_DIRECTION]",
    "posture": "[EXACT_VALUE_FROM_POSTURE]",
    "clothing": "[EXACT_VALUE_FROM_CLOTHING_OPTIONS]",
    "background": "[EXACT_VALUE_FROM_BACKGROUND]"
}
    """

    return UNIFIED_STRUCTURED_IMAGE_PROMPT.format(
        image_type="character",
        description=description,
        options_list=options_list,
        json_format=json_format
    )

def get_roleplay_chat_prompt(agent_name: str, agent_persona: str, mission: str = None) -> str:
    """获取角色扮演聊天提示词"""
    if mission:
        chat_context = f"**当前任务:** {mission}"
        specific_rules = "根据任务目标调整对话策略"
    else:
        chat_context = "**自由对话模式**"
        specific_rules = "自然地进行角色扮演对话"

    return UNIFIED_ROLEPLAY_CHAT_PROMPT.format(
        agent_name=agent_name,
        agent_persona=agent_persona,
        chat_context=chat_context,
        specific_rules=specific_rules
    )

def get_character_image_evaluation_prompt(description: str, female_appeal_keywords: str) -> str:
    """获取角色图片评估提示词"""
    return UNIFIED_IMAGE_EVALUATION_PROMPT.format(
        evaluator_role="角色设计总监",
        image_type="角色",
        evaluation_context=f"**角色描述:** {description}\n**女性偏好关键词:** {female_appeal_keywords}",
        evaluation_criteria="""
1. **角色魅力度** (0-3分): 角色是否具有吸引力，符合女性审美偏好
2. **视觉质量** (0-3分): 图片的整体质量、构图、色彩搭配
3. **设定一致性** (0-2分): 是否与角色描述保持一致
4. **情感表达** (0-2分): 角色的表情和姿态是否传达了正确的情感
        """
    )

def get_story_cover_evaluation_prompt(description: str, female_appeal_keywords: str) -> str:
    """获取故事封面图片评估提示词"""
    return UNIFIED_IMAGE_EVALUATION_PROMPT.format(
        evaluator_role="书籍封面艺术总监",
        image_type="故事封面",
        evaluation_context=f"**故事简介:** {description}\n**女性偏好关键词:** {female_appeal_keywords}",
        evaluation_criteria="""
1. **主题契合度** (0-3分): 封面是否准确传达了故事的核心主题和情感基调。
2. **视觉吸引力** (0-3分): 构图、色彩和光影是否吸引目标读者（女性向）。
3. **角色表现力** (0-2分): 角色的互动和表情是否具有情感张力，引人遐想。
4. **商业潜力** (0-2分): 作为书籍封面，是否具有足够的吸引力让用户点击。
        """
    )

# ========================================
# 4. 向后兼容的别名 (保持原有接口)
# ========================================

CORE_AGENT_CREATION_PROMPT = lambda user_prompt: get_core_agent_creation_prompt(user_prompt)
ARCHETYPE_AGENT_CREATION_PROMPT = lambda **kwargs: get_archetype_agent_creation_prompt(**kwargs)
STRUCTURED_CHARACTER_IMAGE_PROMPT = lambda description: get_structured_character_image_prompt(description)
STREAMING_ROLEPLAY_CHAT_PROMPT = lambda **kwargs: get_roleplay_chat_prompt(**kwargs)
STREAMING_STORY_CHAT_PROMPT = lambda **kwargs: get_roleplay_chat_prompt(**kwargs)
CHARACTER_IMAGE_EVALUATION_PROMPT = lambda **kwargs: get_character_image_evaluation_prompt(**kwargs)
STORY_COVER_EVALUATION_PROMPT = lambda **kwargs: get_story_cover_evaluation_prompt(**kwargs)

# ========================================
# 5. 专用高级模板 (功能独特，予以保留)
# ========================================

STORY_PROGRESS_SCORING_PROMPT = """
你是一位精确的AI游戏裁判。你的唯一任务是根据玩家的最新一次输入，评估其对完成当前章节任务的贡献度。

**【情景信息】**
*   **章节核心任务目标:** {mission}
*   **任务成功通关条件:** {clear_condition}
*   **当前进度状态:** {current_progress}

**【玩家最新输入】**
{user_input}

请严格按照以下JSON格式返回评分：
{{
    "progress_increment": int,  // 【【评分规则】】一个介于 15 到 30 之间的整数。即使贡献很小，也至少给15分。
    "is_mission_complete": boolean  // 任务是否已完成
}}
"""

NOVEL_ANALYSIS_GDD_PROMPT_V2 = """
你是一位顶级的、专注于中国女性向网络小说的游戏策划与情感分析师，同时也是一位金牌编辑。你的任务是将以下【小说核心摘要】，解构并提炼成一份包含【情感动力学】的深度游戏设计文档 (GDD)。这部作品将改编成一个【女性向】的互动游戏，因此所有角色、剧情和情感发展都应侧重于女性玩家的喜好和体验，同时尊重原著。

**第一步：内部思考与情感分析（不要输出此部分）**
请先在内部分析以下【小说核心摘要】的情感弧光，识别出整个故事的情感转折点（如误解、重逢、冲突高潮、和解等）。将此情感地图作为你设计游戏章节结构和角色动机的内在指导。

【小说核心摘要】:
---
{story_summary}
---

**第二步：生成GDD（只输出此部分的JSON）**

**重要指令：**
1. **角色深度挖掘**: 请识别出至少2位主要及重要的配角，确保剧情的丰富性和立体感。
2. **章节细分**: 请将整个故事线细分为 **2个** 简短的游戏章节。
3. **章节聚焦**: 每个章节应聚焦于一个核心事件、一次关键对话或一个重要的情感转折点，确保游戏节奏紧凑。
4. **信息提炼**: 你的输入是详细的分块分析，请从中提炼出最关键的信息填入下方JSON结构。

请严格按照以下JSON格式返回GDD：
{{
    "title": "（AI指令：从小说内容提炼出的、最吸引人的游戏标题。必须简洁，不要包含任何副标题。例如：'何以笙箫默'）",
    "theme_summary": "（AI指令：为玩家撰写的游戏简介，使用第二人称视角，直接对玩家说话。用 \\n 分段。例如：'你将扮演赵默笙，一个为爱远走他乡七年的摄影师...\\n这次归来，你将如何面对七年前的误会与爱人？'）",
    "worldview_text": "（AI指令：为整个故事撰写一段宏大、吸引人的世界观设定，描述故事发生的时代背景、社会环境或特殊规则。）",
    "characters": [
        {{
            "name": "角色名",
            "role": "角色定位 (例如：男主角, 女主角, 重要配角, 反派)",
            "personality_core": "核心性格特征",
            "emotional_appeal": "对女性玩家的情感吸引点",
            "is_protagonist": boolean,
            "key_scenes_description": "（AI指令：用2-3句话，**概括并直接引用**该角色在小说中出场的最关键、最能体现其性格的核心场景和情节。这个描述将作为生成该角色详细档案的核心依据。）"
        }}
    ],
    "chapters": [
        {{
            "chapter_number": int,
            "title": "（AI指令：富有创意的章节标题。绝对不要包含序号或'第X章'之类的前缀。例如：'我们结婚了'）",
            "summary": "（AI指令：用1-2句话简要概括本章的核心情节，**应尽可能贴近原文描述**。）",
            "mission_objective_text": "（AI指令：玩家的核心行动目标，必须是一个明确的、可操作的、带有情感导向的动词短语，并包含NPC名称。错误示例: '与何以琛在超市重逢'。正确示例: '在超市的意外重逢中，试探何以琛对你的真实态度'）",
            "clear_condition_text": "（AI指令：将情感目标游戏化后的通关条件。例如：'通过对话，让何以琛的【误解程度】降低15点'）",
            "emotional_goal": "（AI指令：描述本章玩家需要达成的核心情感目标，例如：'让何以琛感受到你的关心'或'揭开七年前的第一个误会'。）",
            "emotional_peak": "情感高潮点",
            "key_interactions": ["互动点1", "互动点2"]
        }}
    ]
}}
"""

INTERACTIVE_SCENE_GENERATION_PROMPT = """
你是一位顶级的互动叙事设计师和小说游戏化工程师。你的任务是基于提供的上下文，将【小说原文片段】**精准地转化**成一个互动序列，**严格忠于原著**。

**核心原则：忠于原著**
1.  **原文优先**: 所有的 `narration` (旁白) 和 `dialogue` (对话) 元素，都**必须直接从【小说原文片段】中提取或进行最少量、不改变原意的精炼**。禁止进行任何形式的二次创作或风格模仿。
2.  **保留精髓**: 可以将大段的描述性文字拆分为多个 `narration` 节点，但必须保持原文的语序和核心信息。
3.  **合理植入选择**: `choice` 元素是你唯一可以添加的内容。它应该被植入在主角面临决策或内心挣扎的关键节点，选项内容应**严格基于原文中主角的内心独白或潜在的情绪反应**来设计。
4.  **禁止原创剧情**: 绝对禁止在互动序列中添加任何【小说原文片段】里没有的剧情、事件或对话。

**【上下文信息】**
- **上一章剧情回顾**: {previous_chapter_summary}
- **当前游戏状态**: {current_game_state}
- **本章摘要**: {chapter_summary}
- **本章关键互动**: {key_interactions}
- **AI需要扮演的角色索引映射**: {agent_index_map}
- **小说原文片段**:
---
{novel_chunk}
---

**最终输出格式 (最重要！)**

**CRITICAL RULE: The `interactive_sequence` array MUST always end with an element of `element_type: "choice"`. This is not optional. It provides the player with their first decision point for the chapter.**

请严格按照以下JSON格式返回你的转化结果，确保包含 `interactive_sequence` 和 `completion_summary` 两个顶级字段：
```json
{{
  "interactive_sequence": [
    {{
      "element_type": "narration",
      "content_or_prompt": "（直接从原文提取的旁白）"
    }},
    {{
      "element_type": "dialogue",
      "agent_index": 0,
      "content_or_prompt": "（直接从原文提取的角色对话）"
    }},
    {{
      "element_type": "choice",
      "content_or_prompt": "（基于主角内心活动设计的选择提示）",
      "choices": [
        {{
            "text": "（代表主角一种心声或可能反应的选项A）",
            "target_agent_index": 0
        }},
        {{
            "text": "（代表主角另一种心声或可能反应的选项B）",
            "target_agent_index": 1
        }}
      ]
    }}
  ],
  "completion_summary": "（这里是女主角在章节结束后的第一人称内心独白，必须基于本章情节进行总结）"
}}
```

现在，请开始你的转化工作。
"""

CHAPTER_SUMMARY_FOR_NPC_PROMPT = """
你是一位AI虚拟演员的导演。请为【所有参与本章的NPC角色】编写一份清晰、可执行的【行动剧本与表演指令】。

# 章节核心摘要:
{chapter_summary}

# 本章关键互动点:
{key_interactions}

# 你的任务:
生成一份剧本摘要，告诉每个NPC在本章的自由对话阶段：

1. **核心目标 (Objective)**: 他们在本章最想从主角那里获得什么信息，或者想达成什么情感目标？
   （例如：何以琛的目标是"试探赵默笙回国的真实目的，并压抑自己的情感"。）

2. **情绪基调 (Tone)**: 他们的主要情绪是什么？应该如何通过语言和非语言方式表达？
   （例如：何以琛的基调是"外表冷漠、克制，但偶尔会因特定话题流露出不易察觉的痛苦或在乎"。）

3. **关键行动/台词 (Key Actions/Lines)**: 在对话中，有哪些必须提及的关键信息或必须做出的关键行为？
   （例如："必须质问赵默笙关于'结婚'的事情"、"在对话中要刻意保持距离，避免身体接触"。）

4. **关系动态 (Relationship Dynamic)**: 他们当前如何看待主角？是警惕、是怀念、是怨恨，还是复杂的混合体？

请以简洁、直接的指令形式返回这份剧本，以便AI演员能准确地进行表演。
"""

ADVANCED_CHAT_PROMPT = """
# 角色扮演指令
你正在扮演 {agent_name}。

<persona>
{agent_persona}
</persona>

<world_info>
{world_info}
</world_info>

<relationship_context>
# 你们之间的关系状态
{relationship_status}
</relationship_context>

<memory_context>
# 长期记忆摘要
{long_term_memory}

# 精准记忆片段
{short_term_memory}
</memory_context>

<chat_history>
{chat_history}
</chat_history>

**当前用户输入**: {user_message}

<reply>
请严格遵循你在 <persona> 部分中的【核心行为与多样性准则】(system_prompt) 和【多样化对话示例】(mes_example)，并结合上下文，以 {agent_name} 的身份进行回复。
</reply>
"""

ADVANCED_STORY_CHAT_PROMPT = """
# 角色扮演指令
你正在扮演 {agent_name}。你的所有回应都必须严格遵循以下所有设定。

<persona>
{agent_persona}
</persona>

<world_info>
{world_info}
</world_info>

<chapter_context>
**章节标题**: {chapter_title}
**任务目标**: {mission_objective}
**关系状态**: {relationship_status}
**章节事件**: {chapter_event_summary}
</chapter_context>

<chat_history>
{chat_history}
</chat_history>

**当前用户输入**: {user_message}

<reply>
请严格遵循你在 <persona> 部分中的【核心行为与多样性准则】(system_prompt) 和【多样化对话示例】(mes_example)，并结合故事章节的上下文 (<chapter_context>)，以 {agent_name} 的身份进行回复。
</reply>
"""

AGENT_BACKSTORY_PROMPT = """
你是一位角色背景故事创作师。请为以下角色创作详细的背景故事。

**角色信息**: {character_info}
**故事设定**: {story_setting}

请生成角色的详细背景故事：
"""

WORLDVIEW_EXPANSION_PROMPT = """
你是一位世界观设计师。请根据以下信息扩展世界观设定。

**基础设定**: {base_setting}
**故事背景**: {story_background}

请生成详细的世界观扩展：
"""

USER_REPLY_CHOICE_GENERATOR_PROMPT = """
你是一位对话选项设计师。你的任务是根据上下文，特别是AI角色的最后一句话，为“用户”生成2个自然且符合情境的回复选项。

**当前情境**:
{current_dialogue}

**AI刚刚说**: "{last_ai_message}"

请基于AI刚刚说的这句话，为用户生成2个回复选项。
严格按照以下JSON格式返回，不要包含任何额外解释：
{{
  "choices": [
    {{
      "text": "（第一个符合情境的回复选项）"
    }},
    {{
      "text": "（第二个符合情境的回复选项）"
    }}
  ]
}}
"""
</file>

<file path="backend/src/pydantic_models.py">
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Literal
import uuid

# 在原生JSON模式下，不再需要ConfigDict

# --- v 核心修改 v ---
# 1. 将 GeneratedAgentData 重命名为 GeneratedCoreAgentData，以反映其新用途
# 2. 移除 opening_line 字段
# 3. 新增 long_term_goal 和 relationship_with_protagonist 字段
class GeneratedCoreAgentData(BaseModel):
    """由LLM为数据库生成的【核心】智能体档案数据，完全兼容TavernAI格式"""
    # === 基础信息 ===
    name: str = Field(description="智能体的名字")
    gender: str = Field(description="角色的性别，从 ['male', 'female', 'other'] 中选择一个")
    description: str = Field(description="一段100-150字的生动角色描述，用于卡片展示和核心档案")
    tags: List[str] = Field(description="5-7个精准的性格标签")
    voice_name: str = Field(description="从提供的列表中选择一个最适合角色性格的音色名称")

    # === TavernAI兼容核心字段 ===
    personality: str = Field(description="角色的核心性格特征，详细描述性格、行为模式、说话风格等")
    scenario: str = Field(description="角色所处的场景背景，描述环境、关系、当前状况等")
    first_mes: str = Field(description="角色的开场白，第一次见面时说的话")
    mes_example: str = Field(description="对话示例，展示角色的说话风格和互动方式，使用{{char}}和{{user}}占位符")
    system_prompt: str = Field(description="系统级提示词，用于指导AI的角色扮演行为")
    creator_notes: str = Field(description="创作者备注，包含角色的创作思路和使用建议")

    # === 星恋AI扩展字段 ===
    roleplay_prompt: str = Field(description="完整的、用于角色扮演的核心指令")
    long_term_goal: str = Field(description="该角色在整个故事中的长期目标或潜在动机")
    relationship_with_protagonist: str = Field(description="该角色与玩家（主角）的根本关系，例如：宿敌、守护者、利用与被利用者等")

# --- v FIX START v ---
class ResponseMessagePart(BaseModel):
    """单个响应片段的模型，用于结构化输出"""
    action: str = Field(description="角色的动作或心理活动，必须用括号包裹，例如：(她歪了歪头)")
    dialogue: str = Field(description="角色说的具体台词，不包含任何引号")

class StructuredChatResponse(BaseModel):
    """结构化的聊天响应，包含一个或多个部分"""
    parts: List[ResponseMessagePart] = Field(description="一个包含对话和动作部分的列表")
# --- ^ FIX END ^ ---


class TTSRequest(BaseModel):
    """文本转语音的请求体"""
    text: str
    voice_name: str = Field(default="Kore", description="参考Google TTS支持的语音名称")

class AgentCreateRequest(BaseModel):
    """创建智能体的请求体"""
    prompt: str = Field(description="用户输入的智能体创建提示")
    user_id: str = Field(description="创建智能体的用户ID")

class AgentGenerateRequest(BaseModel):
    """一键生成智能体的请求体"""
    prompt: str = Field(description="用户输入的智能体创建提示")
    user_id: uuid.UUID = Field(description="创建智能体的用户ID")

class GuestLoginRequest(BaseModel):
    """游客登录请求体"""
    device_id: str = Field(description="设备的唯一标识符")

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(description="消息角色：user 或 assistant")
    content: str = Field(description="消息内容")

# --- 新增：互动故事相关模型 ---

class StructuredImagePrompt(BaseModel):
    """结构化图片提示词模型"""
    # 基础信息
    image_type: str = Field(description="图片类型：character, cover, background, plot")
    gender: Optional[str] = Field(None, description="性别：male, female")
    character_count: Optional[int] = Field(1, description="角色数量：1 或 2")

    # 外貌特征
    age_description: Optional[str] = Field(None, description="年龄描述")
    hair_style: Optional[str] = Field(None, description="发型")
    hair_color: Optional[str] = Field(None, description="发色")
    eye_color: Optional[str] = Field(None, description="眼色")
    eye_expression: Optional[str] = Field(None, description="眼神表情")
    eye_adjective: Optional[str] = Field(None, description="眼部形容词")

    # 职业和表情
    profession: Optional[str] = Field(None, description="职业")
    facial_expression: Optional[str] = Field(None, description="面部表情")
    gaze_direction: Optional[str] = Field(None, description="视线方向")
    posture: Optional[str] = Field(None, description="姿势")

    # 服装和背景
    clothing: Optional[str] = Field(None, description="服装描述")
    background: Optional[str] = Field(None, description="背景描述")

    # 特殊元素（用于故事封面的双人互动）
    interaction: Optional[str] = Field(None, description="角色间互动描述")
    special_elements: Optional[List[str]] = Field(None, description="特殊元素列表")

class ImageGenerationRequest(BaseModel):
    """图片生成请求模型"""
    description: str = Field(description="图片描述")
    image_type: str = Field(description="图片类型：character, cover, background, plot")
    additional_context: Optional[str] = Field(None, description="额外上下文信息")

class StoryChapter(BaseModel):
    """互动故事的章节模型"""
    chapter_number: int = Field(description="章节序号")
    title: str = Field(description="章节标题")
    thumbnail_url: Optional[str] = Field(None, description="章节缩略图URL")
    # 注意：status 将在服务层动态计算，此处不定义

class InteractiveStoryDetail(BaseModel):
    """互动故事详情页的完整数据模型"""
    id: str = Field(description="故事ID")
    title: str = Field(description="故事标题")
    cover_url: Optional[str] = Field(None, description="封面图URL")
    cover_image_prompt: Optional[str] = Field(None, description="封面图生成提示词")
    full_description: str = Field(description="完整的故事描述")
    author: str = Field(description="作者名")
    last_update: str = Field(description="最后更新日期")
    chapters: List[StoryChapter] = Field(description="章节列表")

# --- 新增：用于故事生成的Pydantic模型 ---

# --- 已删除：StoryNodeContinuation 和 StoryContinuationResponse ---
# 这些模型已被 SceneElement 和 InteractiveSceneResponse 替代
# novel_compiler.py 和故事引擎现在使用更强大、更完善的模型

# --- 新增: 用于故事进度评分的Pydantic模型 ---
class StoryProgressScore(BaseModel):
    """LLM在故事模式下返回的、只包含进度评估的聊天响应"""
    progress_increment: int = Field(description="根据用户输入对完成任务的贡献度，给出的进度增量（15-30之间）", ge=15, le=30)
    is_mission_complete: bool = Field(description="任务是否已完成")

# ========================================
# 互动场景生成相关模型
# ========================================

class ChoiceElement(BaseModel):
    """选择元素中的单个选项"""
    text: str = Field(description="选项的显示文本")
    target_agent_id: Optional[str] = Field(default=None, description="选项针对的角色ID")
    target_agent_index: Optional[int] = Field(default=None, description="选项针对的角色索引 (e.g., 0, 1, 2)")

class SceneElement(BaseModel):
    """互动场景中的单个元素"""
    element_type: Literal['narration', 'dialogue', 'choice', 'image'] = Field(description="元素类型")
    content_or_prompt: str = Field(description="元素的内容或提示")
    agent_id: Optional[str] = Field(default=None, description="对话元素的角色ID")
    agent_index: Optional[int] = Field(default=None, description="对话元素的角色索引 (e.g., 0, 1, 2)")
    choices: Optional[List[ChoiceElement]] = Field(default=None, description="选择元素的选项列表")

class InteractiveSceneResponse(BaseModel):
    """LLM返回的完整互动场景响应"""
    interactive_sequence: List[SceneElement] = Field(description="互动场景序列")
    completion_summary: str = Field(description="章节完成总结")

# ========================================
# 游戏设计文档(GDD)相关模型
# ========================================

class GDDCharacter(BaseModel):
    """GDD中的角色定义"""
    name: str = Field(description="角色名称")
    role: str = Field(description="角色在故事中的作用")
    personality_core: str = Field(description="角色的核心性格特征")
    emotional_appeal: str = Field(description="角色的情感吸引力")
    is_protagonist: bool = Field(description="是否为主角")
    key_scenes_description: str = Field(description="角色关键场景描述")

class GDDChapter(BaseModel):
    """GDD中的章节定义"""
    chapter_number: int = Field(description="章节编号", ge=1)
    title: str = Field(description="章节标题")
    summary: str = Field(description="章节摘要")
    mission_objective_text: str = Field(description="任务目标描述")
    clear_condition_text: str = Field(description="通关条件描述")
    emotional_goal: str = Field(description="情感目标")
    emotional_peak: str = Field(description="情感高潮点")
    key_interactions: List[str] = Field(description="关键互动列表")

class GameDesignDocument(BaseModel):
    """完整的游戏设计文档"""
    title: str = Field(description="故事标题")
    theme_summary: str = Field(description="主题摘要")
    worldview_text: str = Field(description="世界观设定")
    characters: List[GDDCharacter] = Field(description="角色列表")
    chapters: List[GDDChapter] = Field(description="章节列表")

# ========================================
# API响应模型
# ========================================

class ChatParticipantInfo(BaseModel):
    """聊天参与者信息"""
    id: str = Field(description="参与者ID")
    name: str = Field(description="参与者名称")
    avatar_url: Optional[str] = Field(default=None, description="头像URL")

class ChatListItemResponse(BaseModel):
    """聊天列表项响应"""
    chat_id: str = Field(description="聊天ID")
    story_id: Optional[str] = Field(default=None, description="故事ID")
    participants: Optional[List[ChatParticipantInfo]] = Field(default=None, description="参与者列表")
    latest_message: Optional[str] = Field(default=None, description="最新消息")
    latest_message_time: Optional[str] = Field(default=None, description="最新消息时间")
    updated_at: str = Field(description="更新时间")
    is_story: bool = Field(description="是否为故事模式")
    display_name: str = Field(description="显示名称")
    display_avatar: Optional[str] = Field(default=None, description="显示头像")

class AgentDetailResponse(BaseModel):
    """智能体详情响应"""
    id: str = Field(description="智能体ID")
    name: str = Field(description="智能体名称")
    description: Optional[str] = Field(default=None, description="描述")
    image_url: Optional[str] = Field(default=None, description="图片URL")
    avatar_url: Optional[str] = Field(default=None, description="头像URL")
    tags: List[str] = Field(default=[], description="标签列表")
    dialogue_count: int = Field(default=0, description="对话数量")
    creator_name: Optional[str] = Field(default="匿名创作者", description="创作者名称")

class UserChoice(BaseModel):
    """用户回复选项模型"""
    text: str = Field(description="选项的文本内容")
    target_agent_id: Optional[str] = Field(description="目标角色ID，如果未指定则使用默认逻辑", default=None)
    target_agent_index: Optional[int] = Field(description="目标角色序号（1, 2, 3...），优先于target_agent_id", default=None)

class UserChoicesResponse(BaseModel):
    """用户回复选项响应模型"""
    choices: List[UserChoice] = Field(description="用户回复选项列表")

# --- 修改: StoryChatResponseWithProgress 不再需要，可以删除或注释掉 ---
# class StoryChatResponseWithProgress(BaseModel):
#     """LLM在故事模式下返回的带进度评估的聊天响应"""
#     reply: str = Field(description="AI角色扮演的回复内容，可以包含动作和对话。")
#     progress_increment: int = Field(description="根据用户输入对完成任务的贡献度，给出的进度增量（0-100之间）", ge=0, le=100)
#     reasoning: str = Field(description="模型给出该进度增量的简要理由。")

# --- 新增：角色卡导入相关模型 ---

class CharacterCardImportRequest(BaseModel):
    """角色卡导入请求体"""
    user_id: str = Field(description="导入角色卡的用户ID")

class TavernAICharacterCard(BaseModel):
    """TavernAI角色卡数据结构 (兼容v1和v2)"""
    name: str = Field(description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    personality: Optional[str] = Field(None, description="性格特征")
    scenario: Optional[str] = Field(None, description="场景设定")
    first_mes: Optional[str] = Field(None, description="第一条消息")
    mes_example: Optional[str] = Field(None, description="对话示例")
    creator_notes: Optional[str] = Field(None, description="创作者备注")
    system_prompt: Optional[str] = Field(None, description="系统提示")
    post_history_instructions: Optional[str] = Field(None, description="历史后指令")
    alternate_greetings: Optional[List[str]] = Field(None, description="备选问候语")
    character_book: Optional[Dict[str, Any]] = Field(None, description="角色书/世界书")
    tags: Optional[List[str]] = Field(None, description="标签")
    creator: Optional[str] = Field(None, description="创作者")
    character_version: Optional[str] = Field(None, description="角色版本")

class CharacterCardV2(BaseModel):
    """TavernAI角色卡v2格式"""
    spec: str = Field(description="规格版本")
    spec_version: str = Field(description="规格版本号")
    data: TavernAICharacterCard = Field(description="角色数据")

class ImportedAgentResponse(BaseModel):
    """导入角色卡后的响应"""
    id: str = Field(description="新创建的智能体ID")
    name: str = Field(description="角色名称")
    message: str = Field(description="导入结果消息")

# --- 新增：图片评估模型 ---
class ImageEvaluation(BaseModel):
    """图片质量评估结果，由视觉模型返回"""
    score: float = Field(description="图片综合评分（0.0 - 10.0）", ge=0.0, le=10.0)
</file>

<file path="backend/src/services/__init__.py">
"""
服务模块统一导出
所有服务层的单例
"""

from .llm_service import llm_service
from .supabase_service import supabase_service
from .chat_service import chat_service
from .summarization_service import summarization_service
from .prompt_assembler import prompt_assembler
from .imagekit_simple import simple_imagekit_service

__all__ = [
    'llm_service',
    'supabase_service',
    'chat_service',
    'summarization_service',
    'prompt_assembler',
    'simple_imagekit_service'
]
</file>

<file path="backend/src/services/chat_service.py">
# backend/src/services/chat_service.py
"""
聊天服务层 - 负责处理所有与聊天相关的复杂业务逻辑
将业务逻辑从API层剥离，提高代码的可维护性和可测试性
"""
import asyncio
import json
import traceback
import uuid
from typing import Optional, List, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect

from .llm_service import llm_service
from .supabase_service import supabase_service
from .prompt_assembler import prompt_assembler
from .summarization_service import summarization_service


class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, chat_id: str):
        await websocket.accept()
        if chat_id not in self.active_connections:
            self.active_connections[chat_id] = []
        self.active_connections[chat_id].append(websocket)
        print(f"INFO: WebSocket connected to chat {chat_id}. Total connections: {len(self.active_connections[chat_id])}")

    def disconnect(self, websocket: WebSocket, chat_id: str):
        if chat_id in self.active_connections:
            self.active_connections[chat_id].remove(websocket)
            if not self.active_connections[chat_id]:
                del self.active_connections[chat_id]
        print(f"INFO: WebSocket disconnected from chat {chat_id}.")

    async def broadcast(self, chat_id: str, message: str):
        if chat_id in self.active_connections:
            for connection in self.active_connections[chat_id]:
                await connection.send_text(message)

manager = ConnectionManager()


class ChatService:
    def __init__(self):
        # 用于防止对同一个聊天会话的并发处理
        self.chat_locks: Dict[str, asyncio.Lock] = {}

    def get_lock(self, chat_id: str) -> asyncio.Lock:
        """获取或创建指定chat_id的锁"""
        if chat_id not in self.chat_locks:
            self.chat_locks[chat_id] = asyncio.Lock()
        return self.chat_locks[chat_id]
    
    async def start_chat_with_agent(self, user_id: str, agent_id: str) -> str:
        """处理开始与Agent聊天的逻辑，返回chat_id"""
        print(f"INFO: 开始处理用户 {user_id} 与角色 {agent_id} 的会话请求")

        # 检查是否已有现有会话
        existing_chat = await supabase_service.get_chat_by_user_and_agent(user_id, agent_id)
        if existing_chat:
            print(f"SUCCESS: 找到现有会话 {existing_chat['id']}，用户 {user_id} 与角色 {agent_id}")
            return existing_chat["id"]

        print(f"INFO: 未找到现有会话，为用户 {user_id} 与角色 {agent_id} 创建新会话")

        # 验证角色是否存在
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            print(f"ERROR: 角色 {agent_id} 不存在")
            raise ValueError("Agent not found")

        # 创建新的聊天会话
        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=[agent_id])
        if not chat_id:
            print(f"ERROR: 为用户 {user_id} 与角色 {agent_id} 创建会话失败")
            raise ValueError("Failed to create chat session")

        print(f"SUCCESS: 成功创建新会话 {chat_id}，用户 {user_id} 与角色 {agent_id}")

        # 添加角色的开场白（如果有）
        if agent.get("first_mes"):
            await supabase_service.add_message_to_chat(
                chat_id=chat_id, role='assistant', content=agent["first_mes"], agent_id=agent_id
            )
            print(f"INFO: 已添加角色 {agent_id} 的开场白到会话 {chat_id}")

        return chat_id
    
    async def process_user_message(self, chat_id: str, user_id: str, content: str, target_agent_id: Optional[str], manager):
        """处理用户发送的消息的核心入口"""
        lock = self.get_lock(chat_id)
        async with lock:
            try:
                # 1. 保存用户消息
                user_message_record = await supabase_service.add_message_to_chat(
                    chat_id=chat_id, role='user', content=content, agent_id=None
                )
                if not user_message_record:
                    await manager.broadcast(chat_id, json.dumps({"type": "error", "content": "Failed to save user message."}))
                    return
                
                # 异步生成并保存 embedding
                if user_message_record.get('id'):
                    asyncio.create_task(self.generate_and_save_embedding(user_message_record['id'], content))

                # 2. 获取会话信息判断模式
                chat_session = await supabase_service.get_chat_by_id(chat_id)
                is_story_mode = chat_session.get("story_id") is not None

                # 3. 确定回复的AI角色
                participants = await supabase_service.get_chat_participants(chat_id)
                replying_agent = self._get_replying_agent(participants, target_agent_id)
                if not replying_agent:
                    await manager.broadcast(chat_id, json.dumps({"type": "error", "content": "No agent available for reply."}))
                    return

                # 4. 调用AI进行回复
                await self.process_ai_response(chat_id, user_message_record, replying_agent, is_story_mode, manager)

            except Exception as e:
                print(f"ERROR: 处理用户消息失败 for chat {chat_id}: {e}")
                traceback.print_exc()
                await manager.broadcast(chat_id, json.dumps({"type": "error", "content": f"处理消息失败: {str(e)}"}))

    def _get_replying_agent(self, participants: List[Dict], target_agent_id: Optional[str]) -> Optional[Dict]:
        """根据目标ID或默认规则确定回复的Agent"""
        if not participants:
            return None
        if target_agent_id:
            return next((p for p in participants if p['id'] == target_agent_id), participants[0])
        return participants[0]

    async def process_ai_response(self, chat_id: str, user_message: Dict, agent: Dict, is_story_mode: bool, manager):
        """构建Prompt，调用LLM，并处理响应流"""
        mode = 'story' if is_story_mode else 'chat'
        try:
            # 使用快速构建模式，立即响应
            prompt = await prompt_assembler.build_fast_prompt(
                chat_id=chat_id, user_message=user_message['content'], agent_id=agent['id'], mode=mode
            )
            print(f"SUCCESS: 为角色 {agent.get('name')} 构建了{mode}模式的快速提示词")
        except Exception as e:
            print(f"WARN: 快速提示词构建失败，将使用基础模板: {e}")
            # 降级方案：使用简单的历史记录构建提示词
            history = await supabase_service.get_messages_by_chat_id(chat_id, limit=10)
            history_str = "\n".join([f"{msg['role']}: {msg['content']}" for msg in history])
            prompt = f"角色扮演：{agent.get('name')}\n{agent.get('personality')}\n历史对话：\n{history_str}\n用户：{user_message['content']}\n{agent.get('name')}:"
        
        # 流式处理并保存响应
        full_response = await self.stream_and_save_response(chat_id, agent, prompt, manager)

        # --- 触发所有后台任务 ---
        # 1. 异步生成并广播用户选项
        asyncio.create_task(self.regenerate_and_broadcast_choices(chat_id, agent, full_response, is_story_mode, manager))
        
        # 2. 异步检查并更新摘要
        asyncio.create_task(summarization_service.update_chat_summary(chat_id))

        # 3. 异步更新羁绊值
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if chat_session and chat_session.get('user_id'):
            asyncio.create_task(self.update_bond_value_and_broadcast(chat_session['user_id'], agent['id'], chat_id, manager))

        # 4. 异步进行完整的RAG增强（用于未来可能的模型优化，不影响本次响应）
        asyncio.create_task(prompt_assembler.build_prompt(
            chat_id=chat_id, user_message=user_message['content'], agent_id=agent['id'], mode=mode
        ))

    async def stream_and_save_response(self, chat_id: str, agent: Dict, prompt: str, manager) -> str:
        """流式发送响应到客户端并最终保存到数据库"""
        response_stream = llm_service.get_streaming_chat_response(prompt)
        full_ai_response = ""
        temp_message_id = f"temp_{uuid.uuid4().hex}"

        # 流式广播文本块
        async for chunk in response_stream:
            if chunk:
                full_ai_response += chunk
                await manager.broadcast(chat_id, json.dumps({
                    "type": "message_chunk", "temp_id": temp_message_id, "role": "assistant",
                    "agent_id": agent['id'], "content_chunk": chunk
                }))
                await asyncio.sleep(0.01)  # 防止WebSocket拥塞
        
        # 保存完整消息到数据库
        final_content = full_ai_response.strip()
        if final_content:
            ai_message = await supabase_service.add_message_to_chat(
                chat_id=chat_id, role='assistant', content=final_content, agent_id=agent['id']
            )
            await manager.broadcast(chat_id, json.dumps({
                "type": "stream_end", "temp_id": temp_message_id, "final_message": ai_message
            }, default=str))
            
            # 异步生成并保存 embedding
            if ai_message and ai_message.get('id'):
                asyncio.create_task(self.generate_and_save_embedding(ai_message['id'], final_content))

        return final_content

    async def regenerate_and_broadcast_choices(self, chat_id: str, agent: dict, last_ai_message: str, is_story_mode: bool, manager):
        """生成并广播用户回复选项"""
        try:
            print(f"INFO: [BG Task] 开始生成用户选项 for chat {chat_id}")

            # 获取最近的对话历史
            history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
            chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])

            # 获取章节任务目标（如果是故事模式）
            chapter_task_objective = None
            participants_list = []
            if is_story_mode:
                chat_session = await supabase_service.get_chat_by_id(chat_id)
                story_id = chat_session.get('story_id')
                if story_id:
                    try:
                        chapters = await supabase_service.get_story_chapters(story_id)
                        if chapters:
                            current_chapter = chapters[0]
                            chapter_task_objective = current_chapter.get('mission_objective_text', '')
                        participants_list = await supabase_service.get_story_participants(story_id)
                    except Exception as e:
                        print(f"WARN: Failed to get chapter task objective: {e}")

            # 生成用户回复选项
            user_choices = await llm_service.generate_user_reply_choices(
                agent_name=agent.get('name', 'AI'),
                agent_persona=agent.get('personality', ''),
                chat_history=chat_history_str,
                last_ai_message=last_ai_message,
                chapter_task_objective=chapter_task_objective,
                target_agent_id=agent.get('id'),
                include_target_agent_id=is_story_mode,
                participants=participants_list
            )

            # 广播选项更新事件
            choices_payload = {
                "type": "choices_updated",
                "choices": [choice.model_dump() for choice in user_choices] if user_choices else []
            }
            await manager.broadcast(chat_id, json.dumps(choices_payload, default=str))
            print(f"SUCCESS: [BG Task] 成功生成并广播 {len(user_choices)} 个用户选项 for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: [BG Task] 生成用户选项失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            # 向客户端发送错误通知
            await manager.broadcast(chat_id, json.dumps({"type": "choices_failed", "error": str(e)}))

    async def update_bond_value_and_broadcast(self, user_id: str, agent_id: str, chat_id: str, manager):
        """更新羁绊值并广播"""
        try:
            # 实现暴击逻辑 (例如10%的概率)
            import random
            is_critical_hit = random.random() < 0.1
            increase_amount = 2 if is_critical_hit else 1
            
            bond_result = await supabase_service.increment_bond_value(user_id, agent_id, increase_amount)
            
            # 通过WebSocket将更新后的羁绊值和等级通知前端
            if bond_result and bond_result.get('success'):
                await manager.broadcast(chat_id, json.dumps({
                    "type": "bond_update",
                    "agent_id": agent_id,
                    "new_bond_value": bond_result.get('new_bond_value'),
                    "new_bond_level": bond_result.get('new_bond_level'),
                    "is_critical_hit": is_critical_hit,
                    "increase_amount": increase_amount
                }))
                print(f"SUCCESS: [BG Task] 羁绊值更新完成 for user {user_id} and agent {agent_id}")
        except Exception as e:
            print(f"ERROR: [BG Task] 增加羁绊值失败: {e}")
        
    async def generate_and_save_embedding(self, message_id: int, content: str):
        """为消息生成并保存向量"""
        try:
            print(f"INFO: 开始为消息 {message_id} 生成embedding...")
            embedding = await llm_service.get_embedding(content)
            success = await supabase_service.update_message_embedding(message_id, embedding)
            if success:
                print(f"SUCCESS: 消息 {message_id} 的embedding已保存，可用于RAG检索")
            else:
                print(f"WARN: 消息 {message_id} 的embedding保存失败")
        except Exception as e:
            print(f"ERROR: 为消息 {message_id} 生成embedding失败: {e}")

    async def regenerate_user_choices(self, chat_id: str, manager):
        """重新生成用户回复选项（用于WebSocket的regenerate_choices动作）"""
        try:
            print(f"INFO: [WebSocket] 开始重新生成用户选项 for chat {chat_id}")

            # 获取聊天会话信息
            chat = await supabase_service.get_chat_by_id(chat_id)
            if not chat:
                print(f"ERROR: Chat {chat_id} not found")
                error_message = {
                    "type": "choices_regenerated",
                    "choices": [],
                    "error": "聊天会话不存在"
                }
                await manager.broadcast(chat_id, json.dumps(error_message))
                return

            # 获取参与者信息
            participants = await supabase_service.get_chat_participants(chat_id)
            if not participants:
                print(f"ERROR: No participants found for chat {chat_id}")
                error_message = {
                    "type": "choices_regenerated",
                    "choices": [],
                    "error": "未找到聊天参与者"
                }
                await manager.broadcast(chat_id, json.dumps(error_message))
                return

            # 确定主要角色
            story_id = chat.get('story_id')
            protagonist_agent_id = None
            if story_id:
                story_detail = await supabase_service.get_story_by_id(story_id)
                protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

            agent = None
            for participant in participants:
                if participant.get('id') != protagonist_agent_id:
                    agent = participant
                    break

            if not agent and participants:
                agent = participants[0]

            if not agent:
                print(f"ERROR: No agent found for chat {chat_id}")
                return

            # 获取最近的对话历史
            history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
            chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])

            # 获取最后一条AI消息
            last_ai_message = ""
            for msg in reversed(history_for_choices):
                if msg['role'] == 'assistant':
                    last_ai_message = msg['content']
                    break

            if not last_ai_message:
                print(f"WARN: No AI message found for regenerating choices in chat {chat_id}")
                return

            # 获取章节任务目标（如果是故事模式）
            chapter_task_objective = None
            is_story_mode = story_id is not None
            participants_list = []
            
            if is_story_mode:
                try:
                    chapters = await supabase_service.get_story_chapters(story_id)
                    if chapters:
                        current_chapter = chapters[0]
                        chapter_task_objective = current_chapter.get('mission_objective_text', '')
                    participants_list = await supabase_service.get_story_participants(story_id)
                except Exception as e:
                    print(f"WARN: Failed to get chapter task objective for regeneration: {e}")

            # 生成新的用户选项
            user_choices = await llm_service.generate_user_reply_choices(
                agent_name=agent.get('name', 'AI'),
                agent_persona=agent.get('personality', ''),
                chat_history=chat_history_str,
                last_ai_message=last_ai_message,
                chapter_task_objective=chapter_task_objective,
                target_agent_id=agent.get('id'),
                include_target_agent_id=is_story_mode,
                participants=participants_list
            )

            # 广播重新生成的选项
            choices_message = {
                "type": "choices_regenerated",
                "choices": [choice.model_dump() for choice in user_choices] if user_choices else []
            }
            await manager.broadcast(chat_id, json.dumps(choices_message, default=str))

            print(f"SUCCESS: [WebSocket] 成功重新生成 {len(user_choices)} 个用户选项 for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: [WebSocket] 重新生成用户选项失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            error_message = {
                "type": "choices_regenerated",
                "choices": [],
                "error": f"生成选项失败: {str(e)}"
            }
            try:
                await manager.broadcast(chat_id, json.dumps(error_message))
            except Exception as broadcast_error:
                print(f"ERROR: 发送错误消息失败: {broadcast_error}")

    async def send_initial_state(self, websocket: WebSocket, chat_id: str):
        """发送初始状态包给WebSocket客户端"""
        try:
            chat_session = await supabase_service.get_chat_by_id(chat_id)
            if not chat_session:
                await websocket.send_text(json.dumps({"type": "error", "content": "Chat session not found."}))
                return

            is_story_mode = chat_session.get("story_id") is not None
            protagonist_agent_id = None

            if is_story_mode:
                story_id = chat_session.get("story_id")
                story_detail = await supabase_service.get_story_by_id(story_id)
                if story_detail:
                    protagonist_agent_id = story_detail.get("protagonist_agent_id")
                    print(f"=== PROTAGONIST DEBUG (Backend) ===\nStory ID: {story_id}\nStory title: {story_detail.get('title')}\nProtagonist agent ID: {protagonist_agent_id}\n=====================================")

            participants = await supabase_service.get_chat_participants(chat_id)

            if is_story_mode and protagonist_agent_id:
                protagonist_agent = await supabase_service.get_agent_by_id(protagonist_agent_id)
                if protagonist_agent:
                    participants.append(protagonist_agent)
                    print(f"DEBUG: Added protagonist agent {protagonist_agent.get('name')} to participants list")

            initial_state_package = {
                "type": "game_state_sync",
                "data": {
                    "chat_id": chat_id,
                    "is_story_mode": is_story_mode,
                    "protagonist_agent_id": protagonist_agent_id,
                    "messages": await supabase_service.get_messages_by_chat_id(chat_id, limit=100),
                    "participants": participants,
                    "task_progress": chat_session.get("task_progress", {}),
                }
            }
            await websocket.send_text(json.dumps(initial_state_package, default=str))
            print(f"INFO: Sent initial game state for chat {chat_id}")
            
            if is_story_mode:
                print(f"INFO: Chat {chat_id} is in Story Mode.")

        except WebSocketDisconnect:
            # 这是一个预期的异常，当客户端在发送期间关闭连接时发生。
            # 只需记录并安静地退出即可，无需再尝试发送错误消息。
            print(f"WARN: Client disconnected during send_initial_state for chat {chat_id}.")
        except Exception as e:
            print(f"ERROR: 发送初始状态失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            # 在向已关闭的连接发送消息时，这里也可能失败，所以需要一个额外的try-except
            try:
                await websocket.send_text(json.dumps({"type": "error", "content": f"Failed to send initial state: {str(e)}"}))
            except WebSocketDisconnect:
                print(f"WARN: Client was already disconnected when trying to send error for chat {chat_id}.")

    async def handle_websocket_message(self, chat_id: str, user_id: str, message_data: Dict[str, Any]):
        """处理WebSocket消息"""
        try:
            action = message_data.get("action")
            
            # 处理重新生成选项的动作
            if action == 'regenerate_choices':
                asyncio.create_task(self.regenerate_user_choices(chat_id, self.manager))
                return

            # 处理故事模式的next动作
            chat_session = await supabase_service.get_chat_by_id(chat_id)
            is_story_mode = chat_session.get("story_id") is not None
            
            if is_story_mode and action == 'next':
                # 这里需要调用process_opening_sequence，但现在它还在supabase_main.py中
                # 暂时先跳过，后续会移动这个函数
                print(f"INFO: Story mode 'next' action for chat {chat_id} - 需要实现process_opening_sequence")
                return

            # 处理普通消息
            content = message_data.get("content", "").strip()
            if not content:
                print(f"INFO: Received empty content message, skipping...")
                return

            target_agent_id = message_data.get("target_agent_id")
            target_agent_index = message_data.get("target_agent_index")

            if is_story_mode:
                # 故事模式的复杂逻辑暂时保留在supabase_main.py中
                # 这里需要进一步重构
                print(f"INFO: Story mode message processing for chat {chat_id} - 需要进一步重构")
                return
            else:
                # 将常规聊天的消息处理委托给现有的方法
                await self.process_user_message(
                    chat_id=chat_id,
                    user_id=user_id,
                    content=content,
                    target_agent_id=target_agent_id,
                    manager=self.manager
                )

        except Exception as e:
            print(f"ERROR: 处理WebSocket消息失败 for chat {chat_id}: {e}")
            traceback.print_exc()
            await self.manager.broadcast(chat_id, json.dumps({"type": "error", "content": f"处理消息失败: {str(e)}"}))

    # 将manager设为实例属性，方便访问
    @property
    def manager(self):
        return manager


# 创建一个全局单例
chat_service = ChatService()
</file>

<file path="backend/src/services/imagekit_simple.py">
#!/usr/bin/env python3
"""
简化版ImageKit图片存储服务
使用HTTP请求直接与ImageKit API交互
"""

import os
import uuid
import asyncio
import aiohttp
import base64
import json
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class SimpleImageKitService:
    """简化版ImageKit服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.private_key = os.getenv('IMAGEKIT_PRIVATE_KEY')
        self.public_key = os.getenv('IMAGEKIT_PUBLIC_KEY')
        self.url_endpoint = os.getenv('IMAGEKIT_URL_ENDPOINT')
        
        if not all([self.private_key, self.public_key, self.url_endpoint]):
            raise ValueError("请设置所有ImageKit环境变量")
        
        # 基础API URL
        self.api_base = "https://api.imagekit.io/v1"
        
        # 认证头
        auth_string = f"{self.private_key}:"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        self.auth_header = f"Basic {auth_b64}"
    
    async def upload_image_from_url(
        self,
        image_url: str,
        file_name: Optional[str] = None,
        folder: str = "/uploads/",
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        从URL上传图片
        
        Args:
            image_url: 图片URL
            file_name: 文件名
            folder: 文件夹
            tags: 标签列表
            
        Returns:
            上传结果
        """
        try:
            if not file_name:
                file_name = f"{uuid.uuid4().hex}.jpg"
            
            # 准备上传数据
            upload_data = {
                "file": image_url,
                "fileName": file_name,
                "folder": folder,
                "useUniqueFileName": "true"
            }
            
            if tags:
                upload_data["tags"] = ",".join(tags)
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/files/upload",
                    headers={
                        "Authorization": self.auth_header
                    },
                    data=upload_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "file_id": result.get("fileId"),
                            "name": result.get("name"),
                            "url": result.get("url"),
                            "file_path": result.get("filePath"),
                            "size": result.get("size"),
                            "file_type": result.get("fileType"),
                            "tags": result.get("tags", [])
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def upload_image_from_base64(
        self,
        base64_data: str,
        file_name: Optional[str] = None,
        folder: str = "/uploads/",
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        从Base64数据上传图片
        
        Args:
            base64_data: Base64数据
            file_name: 文件名
            folder: 文件夹
            tags: 标签列表
            
        Returns:
            上传结果
        """
        try:
            if not file_name:
                file_name = f"{uuid.uuid4().hex}.jpg"
            
            # 准备上传数据
            upload_data = {
                "file": base64_data,
                "fileName": file_name,
                "folder": folder,
                "useUniqueFileName": "true"
            }
            
            if tags:
                upload_data["tags"] = ",".join(tags)
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/files/upload",
                    headers={
                        "Authorization": self.auth_header
                    },
                    data=upload_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "file_id": result.get("fileId"),
                            "name": result.get("name"),
                            "url": result.get("url"),
                            "file_path": result.get("filePath"),
                            "size": result.get("size"),
                            "file_type": result.get("fileType"),
                            "tags": result.get("tags", [])
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_image_url(
        self,
        file_path: str,
        width: Optional[int] = None,
        height: Optional[int] = None,
        quality: Optional[int] = None,
        format: Optional[str] = None
    ) -> str:
        """
        生成图片URL
        
        Args:
            file_path: 文件路径
            width: 宽度
            height: 高度
            quality: 质量
            format: 格式
            
        Returns:
            图片URL
        """
        try:
            url = f"{self.url_endpoint}{file_path}"
            
            # 添加变换参数
            transformations = []
            if width:
                transformations.append(f"w-{width}")
            if height:
                transformations.append(f"h-{height}")
            if quality:
                transformations.append(f"q-{quality}")
            if format:
                transformations.append(f"f-{format}")
            
            if transformations:
                url += f"?tr={','.join(transformations)}"
            
            return url
            
        except Exception as e:
            print(f"生成URL失败: {e}")
            return f"{self.url_endpoint}{file_path}"
    
    async def list_files(self, limit: int = 20) -> Dict[str, Any]:
        """
        列出文件
        
        Args:
            limit: 限制数量
            
        Returns:
            文件列表
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base}/files",
                    headers={
                        "Authorization": self.auth_header
                    },
                    params={"limit": limit}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        files = []
                        
                        for file_item in result:
                            files.append({
                                "file_id": file_item.get("fileId"),
                                "name": file_item.get("name"),
                                "url": file_item.get("url"),
                                "file_path": file_item.get("filePath"),
                                "size": file_item.get("size"),
                                "file_type": file_item.get("fileType"),
                                "tags": file_item.get("tags", []),
                                "created_at": file_item.get("createdAt"),
                                "updated_at": file_item.get("updatedAt")
                            })
                        
                        return {
                            "success": True,
                            "files": files,
                            "total": len(files)
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}",
                            "files": [],
                            "total": 0
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "files": [],
                "total": 0
            }
    
    async def delete_image(self, file_id: str) -> Dict[str, Any]:
        """
        删除图片
        
        Args:
            file_id: 文件ID
            
        Returns:
            删除结果
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.delete(
                    f"{self.api_base}/files/{file_id}",
                    headers={
                        "Authorization": self.auth_header
                    }
                ) as response:
                    if response.status == 204:
                        return {
                            "success": True,
                            "message": f"文件 {file_id} 删除成功"
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def upload_image(
        self,
        image_bytes: bytes,
        file_name: str,
        folder: str = "/uploads/",
        tags: Optional[List[str]] = None
    ) -> str:
        """
        上传图片字节数据到ImageKit

        Args:
            image_bytes: 图片字节数据
            file_name: 文件名
            folder: 文件夹
            tags: 标签列表

        Returns:
            图片URL，如果失败返回None
        """
        try:
            import base64
            import io

            # 将bytes转换为base64
            base64_data = base64.b64encode(image_bytes).decode('utf-8')
            base64_with_prefix = f"data:image/png;base64,{base64_data}"

            # 调用base64上传方法
            result = await self.upload_image_from_base64(
                base64_data=base64_with_prefix,
                file_name=file_name,
                folder=folder,
                tags=tags
            )

            if result["success"]:
                return result["url"]
            else:
                print(f"图片上传失败: {result['error']}")
                return None

        except Exception as e:
            print(f"图片上传异常: {e}")
            return None

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            健康状态
        """
        try:
            # 尝试列出文件
            result = await self.list_files(limit=1)

            if result["success"]:
                return {
                    "success": True,
                    "message": "ImageKit服务连接正常",
                    "url_endpoint": self.url_endpoint
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "message": "ImageKit服务连接失败"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "ImageKit服务连接失败"
            }


# 全局服务实例
simple_imagekit_service = SimpleImageKitService()


# 便捷函数
async def upload_image_url(url: str, **kwargs) -> Dict[str, Any]:
    """上传图片URL"""
    return await simple_imagekit_service.upload_image_from_url(url, **kwargs)


async def upload_image_base64(base64_data: str, **kwargs) -> Dict[str, Any]:
    """上传Base64图片"""
    return await simple_imagekit_service.upload_image_from_base64(base64_data, **kwargs)


def get_image_url(file_path: str, **kwargs) -> str:
    """生成图片URL"""
    return simple_imagekit_service.generate_image_url(file_path, **kwargs)


async def delete_image(file_id: str) -> Dict[str, Any]:
    """删除图片"""
    return await simple_imagekit_service.delete_image(file_id)


if __name__ == "__main__":
    # 测试代码
    async def test_service():
        print("🧪 测试简化版ImageKit服务...")
        
        # 健康检查
        health = await simple_imagekit_service.health_check()
        print(f"健康检查: {health}")
        
        # 列出文件
        files = await simple_imagekit_service.list_files(limit=5)
        print(f"文件列表: {files}")
        
        # 测试URL生成
        test_url = simple_imagekit_service.generate_image_url(
            "/test.jpg",
            width=300,
            height=200,
            quality=80,
            format="webp"
        )
        print(f"测试URL: {test_url}")
    
    asyncio.run(test_service())
</file>

<file path="backend/src/services/llm_service.py">
#!/usr/bin/env python3
"""
统一LLM服务
整合所有AI模型交互功能，提供完整的API接口
"""

import os
import asyncio
import json
import traceback
import time
from typing import Optional, Callable, Any, Type, List, Dict, Tuple, AsyncGenerator
from functools import wraps

from google import genai
from google.genai import types
from google.api_core import exceptions as google_exceptions
from dotenv import load_dotenv
import instructor
from pydantic import BaseModel
from instructor import Mode

from ..pydantic_models import (
    ImageGenerationRequest, GeneratedCoreAgentData, 
    StoryProgressScore, UserChoice, StructuredChatResponse
)

# --- 统一的纯文本安全设置常量 ---
TEXT_ONLY_SAFETY_SETTINGS = [
    {
        "category": types.HarmCategory.HARM_CATEGORY_HARASSMENT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
]

TEXT_ONLY_SAFETY_SETTINGS_DICT = {
    types.HarmCategory.HARM_CATEGORY_HARASSMENT: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_HATE_SPEECH: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: types.HarmBlockThreshold.BLOCK_NONE,
}

# 加载环境变量
load_dotenv()

class APIManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.api_keys = self._load_api_keys()
        self.current_index = 0
        self.rate_limit_delay = 12  # 每个API密钥的速率限制间隔（秒）
        self.last_request_times = {}  # 记录每个API密钥的最后请求时间
        
    def _load_api_keys(self):
        """加载API密钥"""
        keys_str = os.getenv("GEMINI_API_KEYS", "")
        if not keys_str:
            raise ValueError("未找到GEMINI_API_KEYS环境变量")
        
        keys = [key.strip() for key in keys_str.split(",") if key.strip()]
        if not keys:
            raise ValueError("GEMINI_API_KEYS为空")
        
        print(f"INFO: 已加载 {len(keys)} 个Gemini API密钥，速率限制: 5次/60秒")
        return keys
    
    async def get_next_client(self):
        """获取下一个可用的API客户端"""
        for _ in range(len(self.api_keys)):
            api_key = self.api_keys[self.current_index]
            current_time = time.time()

            # 检查速率限制
            last_time = self.last_request_times.get(self.current_index, 0)
            if current_time - last_time >= self.rate_limit_delay:
                # 更新最后请求时间
                self.last_request_times[self.current_index] = current_time

                # 创建客户端
                client = genai.Client(api_key=api_key)

                print(f"INFO: 使用 API 密钥索引 {self.current_index}: ...{api_key[-4:]}")

                # 轮换到下一个密钥
                self.current_index = (self.current_index + 1) % len(self.api_keys)

                return client
            else:
                # 当前密钥还在速率限制中，尝试下一个
                self.current_index = (self.current_index + 1) % len(self.api_keys)
        
        # 所有密钥都在速率限制中，等待最短的剩余时间
        min_wait_time = min(
            self.rate_limit_delay - (current_time - self.last_request_times.get(i, 0))
            for i in range(len(self.api_keys))
        )
        
        if min_wait_time > 0:
            print(f"INFO: 所有API密钥都在速率限制中，等待 {min_wait_time:.1f} 秒...")
            await asyncio.sleep(min_wait_time)
        
        # 递归调用，获取可用客户端
        return await self.get_next_client()

# 全局API管理器实例
api_manager = APIManager()

def retry_on_api_error(max_retries: int = 3, base_delay: float = 5.0):
    """API错误重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                    
                except google_exceptions.ResourceExhausted as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: ⚠️  API配额耗尽 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后切换API密钥重试...")
                        await asyncio.sleep(delay)
                    
                except google_exceptions.DeadlineExceeded as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: ⏰ API请求超时 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后重试...")
                        await asyncio.sleep(delay)
                        
                except google_exceptions.ServiceUnavailable as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: 🔄 API服务过载 (尝试 {attempt + 1}/{max_retries}) - 模型繁忙中")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后切换API密钥重试...")
                        await asyncio.sleep(delay)
                        
                except Exception as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"ERROR: ❌ 函数 {func.__name__} 发生错误 (尝试 {attempt + 1}/{max_retries}): {str(e)[:100]}...")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后重试...")
                        await asyncio.sleep(delay)
            
            print(f"ERROR: [Retry Decorator] 所有重试均失败，最终错误: {last_exception}")
            raise last_exception
            
        return wrapper
    return decorator

class LLMService:
    """统一LLM服务类 - 整合所有AI模型交互功能"""
    
    def __init__(self):
        self.story_model_name = os.getenv("GEMINI_GEN_STORY_MODEL", "gemini-2.5-pro")
        self.role_model_name = os.getenv("GEMINI_GEN_ROLE_MODEL", "gemini-2.5-pro")
        self.chat_model_name = os.getenv("GEMINI_CHAT_MODEL", "gemini-2.5-flash")
        self.image_check_model_name = os.getenv("GEMINI_CHECK_IMAGE_MODEL", "gemini-2.5-flash")
        self.image_gen_model_name = os.getenv("GEMINI_GEN_IMAGE_MODEL", "gemini-2.0-flash-preview-image-generation")
        self.tts_model_name = os.getenv("GEMINI_TTS_MODEL", "gemini-2.5-flash-preview-tts")
        self.task_check_model_name = os.getenv("GEMINI_CHECK_TASK_MODEL", "gemini-2.5-flash")
        self.embedding_model_name = os.getenv("GEMINI_EMBEDDING_MODEL", "gemini-embedding-001")
        
        # 图片生成相关配置
        self.IMAGE_MAX_RETRIES = 3
        self.IMAGE_MIN_SCORE = float(os.getenv("IMAGE_MIN_SCORE", "6.0"))
        
    async def get_client(self):
        """获取API客户端"""
        return await api_manager.get_next_client()
    
    # ========================================
    # 基础文本生成方法
    # ========================================
    
    @retry_on_api_error()
    async def generate_text(self, prompt: str, model_name: Optional[str] = None,
                          temperature: float = 0.7, max_tokens: Optional[int] = None) -> str:
        """生成文本的通用方法"""
        client = await self.get_client()
        model = model_name or self.chat_model_name

        print("="*80)
        print(f"DEBUG: [LLM Request - generate_text] Prompt sent to model: {model}")
        print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
        print("="*80)

        config = types.GenerateContentConfig(
            temperature=temperature,
            max_output_tokens=max_tokens,
            safety_settings=TEXT_ONLY_SAFETY_SETTINGS
        )

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=prompt,
            config=config
        )

        if not response.text:
            raise Exception("模型未返回有效的文本响应")

        print("="*80)
        print("DEBUG: [LLM Response - generate_text] Content received:")
        print(f"--- RESPONSE START ---\n{response.text}\n--- RESPONSE END ---")
        print("="*80)

        print(f"SUCCESS: 成功生成文本响应，长度: {len(response.text)}")
        return response.text
    
    @retry_on_api_error()
    async def generate_json(self, prompt: str, model_name: Optional[str] = None,
                          temperature: float = 0.7) -> dict:
        """生成JSON响应的通用方法"""
        client = await self.get_client()
        model = model_name or self.role_model_name

        print("="*80)
        print(f"DEBUG: [LLM Request - generate_json] Prompt sent to model: {model}")
        print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
        print("="*80)

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=prompt,
            config=types.GenerateContentConfig(
                response_mime_type="application/json",
                temperature=temperature,
                safety_settings=TEXT_ONLY_SAFETY_SETTINGS
            )
        )

        if not response.text:
            raise Exception("模型未返回有效的JSON响应")

        print("="*80)
        print("DEBUG: [LLM Response - generate_json] Raw JSON received:")
        print(f"--- RESPONSE START ---\n{response.text}\n--- RESPONSE END ---")
        print("="*80)

        try:
            json_data = json.loads(response.text)
            print(f"SUCCESS: 成功生成JSON响应，包含 {len(json_data)} 个字段")
            return json_data
        except json.JSONDecodeError as e:
            print(f"ERROR: JSON解析失败: {e}")
            print(f"DEBUG: 原始响应: {response.text}")
            raise ValueError(f"JSON解析失败: {e}")
    
    @retry_on_api_error()
    async def generate_structured_response(
         self,
         prompt: Any,
         response_model: Type[BaseModel],
         model_name: Optional[str] = None,
         temperature: float = 0.7,
     ) -> Any:
        """使用 Gemini 原生 JSON 响应能力 + Pydantic 校验，返回结构化数据"""
        print(f"INFO: [Gemini] 正在为模型 '{model_name or self.story_model_name}' 生成 {response_model.__name__} 结构化响应…")

        try:
            # 1. 直接调用 generate_json 生成原始 JSON 数据
            raw_json: dict = await self.generate_json(
                prompt=prompt,
                model_name=model_name or self.story_model_name,
                temperature=temperature,
            )

            # 2. 使用 Pydantic 进行结构化验证
            if isinstance(raw_json, list):
                if len(raw_json) == 0:
                    raise ValueError("LLM 返回了空列表，无法解析为结构化对象")
                raw_json_to_validate = raw_json[0]
            else:
                raw_json_to_validate = raw_json

            structured_obj = response_model.model_validate(raw_json_to_validate)

            print(f"SUCCESS: [Gemini] {response_model.__name__} 对象生成并验证成功。")
            return structured_obj

        except Exception as e:
            print(f"ERROR: [Gemini] 生成结构化响应失败: {e}")
            traceback.print_exc()
            raise e
    
    # ========================================
    # 流式聊天响应方法
    # ========================================
    
    @retry_on_api_error() # 保持重试装饰器
    async def get_streaming_chat_response(self, prompt: str) -> AsyncGenerator[str, None]:
        """流式聊天响应 - 修复版"""
        try:
            client = await self.get_client()
            
            print("="*80)
            print(f"DEBUG: [LLM Request - streaming] Prompt sent to model: {self.chat_model_name}")
            print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
            print("="*80)

            config = types.GenerateContentConfig(
                temperature=0.7,
                safety_settings=TEXT_ONLY_SAFETY_SETTINGS
            )

            # 使用正确的 genai 客户端流式生成
            response_stream = await asyncio.to_thread(
                client.models.generate_content_stream,
                model=self.chat_model_name,
                contents=prompt,
                config=config
            )

            accumulated_text = ""
            # 直接在异步生成器上使用 async for
            async for chunk in response_stream:
                if chunk.text:
                    accumulated_text += chunk.text
                    yield chunk.text

            print(f"SUCCESS: 流式响应完成，总长度: {len(accumulated_text)}")

        except Exception as e:
            print(f"ERROR: 流式响应失败: {e}")
            traceback.print_exc()
            raise  # 将异常向上抛出，以便调用方能感知到
    
    # ========================================
    # 文本生成相关方法
    # ========================================
    
    async def generate_text_response(self, prompt: str) -> str:
        """生成文本响应"""
        return await self.generate_text(prompt, model_name=self.chat_model_name)
    
    async def get_story_progress_score(self, mission: str, clear_condition: str, 
                                     current_progress: int, history: List[Dict[str, str]], 
                                     user_message: str) -> StoryProgressScore:
        """获取故事进度评分"""
        from ..prompt_templates import STORY_PROGRESS_PROMPT
        
        # 构建历史对话字符串
        history_str = "\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-10:]])
        
        prompt = STORY_PROGRESS_PROMPT.format(
            mission=mission,
            clear_condition=clear_condition,
            current_progress=current_progress,
            history=history_str,
            user_message=user_message
        )
        
        return await self.generate_structured_response(
            prompt=prompt,
            response_model=StoryProgressScore,
            model_name=self.task_check_model_name
        )
    
    async def generate_user_reply_choices(self, agent_name: str, agent_persona: str,
                                        chat_history: str, last_ai_message: str,
                                        chapter_task_objective: str = None,
                                        target_agent_id: str = None,
                                        include_target_agent_id: bool = False,
                                        participants: List[Dict] = None) -> List[UserChoice]:
        """生成用户回复选项"""
        from ..prompt_templates import USER_CHOICE_PROMPT
        
        prompt = USER_CHOICE_PROMPT.format(
            agent_name=agent_name,
            agent_persona=agent_persona,
            chat_history=chat_history,
            last_ai_message=last_ai_message,
            chapter_task_objective=chapter_task_objective or "无特定目标"
        )
        
        response = await self.generate_json(prompt, model_name=self.chat_model_name)
        
        # 转换为UserChoice对象列表
        choices = []
        for choice_data in response.get("choices", []):
            choice = UserChoice(
                text=choice_data.get("text", ""),
                emotion=choice_data.get("emotion", "neutral"),
                target_agent_id=target_agent_id if include_target_agent_id else None
            )
            choices.append(choice)
        
        return choices
    
    async def generate_game_design_document(self, story_summary: str) -> dict:
        """生成游戏设计文档"""
        from ..prompt_templates import GAME_DESIGN_PROMPT
        
        prompt = GAME_DESIGN_PROMPT.format(story_summary=story_summary)
        return await self.generate_json(prompt, model_name=self.story_model_name)
    
    async def generate_worldview_text(self, story_theme: str) -> str:
        """生成世界观文本"""
        from ..prompt_templates import WORLDVIEW_PROMPT
        
        prompt = WORLDVIEW_PROMPT.format(story_theme=story_theme)
        return await self.generate_text(prompt, model_name=self.story_model_name)
    
    async def generate_agent_backstory(self, character_info: str, story_setting: str) -> str:
        """生成角色背景故事"""
        from ..prompt_templates import AGENT_BACKSTORY_PROMPT
        
        prompt = AGENT_BACKSTORY_PROMPT.format(
            character_info=character_info,
            story_setting=story_setting
        )
        return await self.generate_text(prompt, model_name=self.role_model_name)
    
    async def summarize_long_novel_in_chunks(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """分块摘要长篇小说 - 并发处理版本"""
        if len(novel_text) <= chunk_size:
            return await self.generate_text(f"请为以下小说内容生成详细摘要：\n\n{novel_text}")
        
        # 分块处理
        chunks = [novel_text[i:i+chunk_size] for i in range(0, len(novel_text), chunk_size)]
        
        # 并发处理各个块
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def summarize_chunk(chunk):
            async with semaphore:
                return await self.generate_text(f"请为以下文本片段生成摘要：\n\n{chunk}")
        
        chunk_summaries = await asyncio.gather(*[summarize_chunk(chunk) for chunk in chunks])
        
        # 合并摘要
        combined_summary = "\n\n".join(chunk_summaries)
        
        # 最终整合
        final_prompt = f"请将以下分段摘要整合为一个完整的故事摘要：\n\n{combined_summary}"
        return await self.generate_text(final_prompt)

    async def distill_story_summary(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """提炼故事摘要 - 支持并发处理"""
        summary = await self.summarize_long_novel_in_chunks(novel_text, chunk_size, max_concurrent)
        
        # 进一步提炼
        distill_prompt = f"请将以下摘要进一步提炼为核心故事要点：\n\n{summary}"
        return await self.generate_text(distill_prompt)

    async def generate_initial_relationship_prompt(self, character_name: str, key_variables: List[str]) -> str:
        """生成初始关系描述"""
        variables_str = ", ".join(key_variables)
        prompt = f"为角色 {character_name} 生成初始关系设定，考虑以下关键要素：{variables_str}"
        return await self.generate_text(prompt)

    async def generate_interactive_scene(self, gdd: dict, chapter_gdd: dict,
                                       novel_chunk: str, agent_map: dict,
                                       character_profiles: list = None,
                                       current_game_state: dict = None,
                                       previous_chapter_summary: str = None) -> dict:
        """生成互动场景序列"""
        from ..prompt_templates import INTERACTIVE_SCENE_PROMPT
        
        prompt = INTERACTIVE_SCENE_PROMPT.format(
            gdd=json.dumps(gdd, ensure_ascii=False, indent=2),
            chapter_gdd=json.dumps(chapter_gdd, ensure_ascii=False, indent=2),
            novel_chunk=novel_chunk,
            agent_map=json.dumps(agent_map, ensure_ascii=False, indent=2),
            character_profiles=json.dumps(character_profiles or [], ensure_ascii=False, indent=2),
            current_game_state=json.dumps(current_game_state or {}, ensure_ascii=False, indent=2),
            previous_chapter_summary=previous_chapter_summary or "无"
        )
        
        return await self.generate_json(prompt, model_name=self.story_model_name)

    async def generate_chapter_summary_for_npc(self, chapter_summary: str,
                                             key_interactions: List[str]) -> str:
        """为NPC生成章节剧本摘要"""
        interactions_str = "\n".join(key_interactions)
        prompt = f"基于章节摘要和关键互动，为NPC生成剧本摘要：\n\n章节摘要：{chapter_summary}\n\n关键互动：\n{interactions_str}"
        return await self.generate_text(prompt)
    
    # ========================================
    # 图片生成相关方法
    # ========================================
    
    @retry_on_api_error()
    async def generate_enhanced_image_from_request(self, request: ImageGenerationRequest) -> Optional[Tuple[bytes, str]]:
        """生成增强图片"""
        client = await self.get_client()
        
        # 构建提示词
        if request.structured_data:
            prompt = self._convert_structured_prompt_to_string(request.structured_data)
        else:
            prompt = request.prompt
        
        print(f"INFO: 开始生成图片，提示词: {prompt[:100]}...")
        
        try:
            response = await asyncio.to_thread(
                client.models.generate_content,
                model=self.image_gen_model_name,
                contents=prompt,
                config=types.GenerateContentConfig(
                    safety_settings=TEXT_ONLY_SAFETY_SETTINGS
                )
            )
            
            if response.candidates and response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'inline_data') and part.inline_data:
                        image_data = part.inline_data.data
                        mime_type = part.inline_data.mime_type
                        print(f"SUCCESS: 图片生成成功，类型: {mime_type}")
                        return (image_data, mime_type)
            
            print("ERROR: 未能从响应中提取图片数据")
            return None
            
        except Exception as e:
            print(f"ERROR: 图片生成失败: {e}")
            return None
    
    async def generate_structured_image_from_request(self, request: ImageGenerationRequest) -> Optional[Tuple[bytes, str]]:
        """生成结构化图片"""
        return await self.generate_enhanced_image_from_request(request)
    
    def _convert_structured_prompt_to_string(self, structured_data: dict) -> str:
        """转换结构化提示词"""
        if structured_data.get("type") == "character":
            return self._build_character_prompt(structured_data)
        elif structured_data.get("type") == "cover":
            return self._build_cover_prompt(structured_data)
        else:
            # 通用转换
            parts = []
            for key, value in structured_data.items():
                if key != "type" and value:
                    parts.append(f"{key}: {value}")
            return ", ".join(parts)
    
    def _build_character_prompt(self, data: dict) -> str:
        """构建角色提示词"""
        parts = []
        
        if data.get("name"):
            parts.append(f"角色名称: {data['name']}")
        
        if data.get("appearance"):
            parts.append(f"外观: {data['appearance']}")
        
        if data.get("clothing"):
            parts.append(f"服装: {data['clothing']}")
        
        if data.get("pose"):
            parts.append(f"姿势: {data['pose']}")
        
        if data.get("background"):
            parts.append(f"背景: {data['background']}")
        
        # 添加默认的高质量描述符
        parts.append("高质量, 详细, 专业插画风格")
        
        return ", ".join(parts)
    
    def _build_cover_prompt(self, data: dict) -> str:
        """构建封面提示词"""
        parts = []
        
        if data.get("title"):
            parts.append(f"标题: {data['title']}")
        
        if data.get("theme"):
            parts.append(f"主题: {data['theme']}")
        
        if data.get("style"):
            parts.append(f"风格: {data['style']}")
        
        if data.get("elements"):
            parts.append(f"元素: {data['elements']}")
        
        # 添加封面特定描述符
        parts.append("书籍封面, 专业设计, 吸引人的视觉效果")
        
        return ", ".join(parts)
    
    async def generate_image_from_description_with_prompt(self, description: str) -> Optional[Tuple[bytes, str]]:
        """根据描述生成图片"""
        request = ImageGenerationRequest(
            prompt=description,
            width=1024,
            height=1024
        )
        return await self.generate_enhanced_image_from_request(request)
    
    # ========================================
    # 角色相关方法
    # ========================================
    
    async def generate_agent_from_analysis(self, character_gdd: Dict[str, Any],
                                         story_theme_summary: str) -> Tuple[GeneratedCoreAgentData, Optional[bytes], Optional[str]]:
        """生成角色档案"""
        from ..prompt_templates import AGENT_GENERATION_PROMPT
        
        prompt = AGENT_GENERATION_PROMPT.format(
            character_gdd=json.dumps(character_gdd, ensure_ascii=False, indent=2),
            story_theme_summary=story_theme_summary
        )
        
        # 生成角色数据
        agent_data = await self.generate_structured_response(
            prompt=prompt,
            response_model=GeneratedCoreAgentData,
            model_name=self.role_model_name
        )
        
        # 生成角色图片
        image_data = None
        image_mime_type = None
        
        if agent_data.appearance:
            image_request = ImageGenerationRequest(
                structured_data={
                    "type": "character",
                    "name": agent_data.name,
                    "appearance": agent_data.appearance,
                    "clothing": "适合角色设定的服装",
                    "pose": "自然站立姿势",
                    "background": "简洁背景"
                }
            )
            
            image_result = await self.generate_enhanced_image_from_request(image_request)
            if image_result:
                image_data, image_mime_type = image_result
        
        return agent_data, image_data, image_mime_type
    
    async def create_agent_profile(self, name: str, description: str, 
                                 personality: str, scenario: str,
                                 additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建角色档案"""
        profile = {
            "name": name,
            "description": description,
            "personality": personality,
            "scenario": scenario,
            "created_at": asyncio.get_event_loop().time()
        }
        
        if additional_data:
            profile.update(additional_data)
        
        return profile
    
    async def generate_character_variations(self, base_character: Dict[str, Any], 
                                          variation_count: int = 3) -> List[Dict[str, Any]]:
        """生成角色变体"""
        variations = []
        base_name = base_character.get("name", "角色")
        
        for i in range(variation_count):
            prompt = f"基于以下角色创建一个变体版本（变体{i+1}）：\n{json.dumps(base_character, ensure_ascii=False, indent=2)}"
            
            variation_data = await self.generate_json(prompt, model_name=self.role_model_name)
            variations.append(variation_data)
        
        return variations
    
    async def analyze_character_compatibility(self, character1: Dict[str, Any], 
                                            character2: Dict[str, Any]) -> Dict[str, Any]:
        """分析角色兼容性"""
        prompt = f"""分析以下两个角色的兼容性：

角色1：{json.dumps(character1, ensure_ascii=False, indent=2)}

角色2：{json.dumps(character2, ensure_ascii=False, indent=2)}

请分析他们的性格兼容性、互动潜力和可能的冲突点。"""
        
        return await self.generate_json(prompt, model_name=self.role_model_name)
    
    async def enhance_character_backstory(self, character: Dict[str, Any], 
                                        story_context: str = "") -> str:
        """增强角色背景故事"""
        prompt = f"""为以下角色创建详细的背景故事：

角色信息：{json.dumps(character, ensure_ascii=False, indent=2)}

故事背景：{story_context}

请创建一个丰富、有深度的背景故事。"""
        
        return await self.generate_text(prompt, model_name=self.role_model_name)
    
    def get_character_summary(self, character: Dict[str, Any]) -> str:
        """获取角色摘要"""
        name = character.get("name", "未知角色")
        personality = character.get("personality", "")
        description = character.get("description", "")
        
        summary_parts = [name]
        if personality:
            summary_parts.append(f"性格：{personality[:50]}...")
        if description:
            summary_parts.append(f"描述：{description[:50]}...")
        
        return " | ".join(summary_parts)
    
    # ========================================
    # 嵌入相关方法
    # ========================================
    
    @retry_on_api_error()
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本嵌入向量 - 修复版"""
        try:
            client = await self.get_client()
            
            response = await asyncio.to_thread(
                client.models.embed_content,
                model=self.embedding_model_name,
                contents=text
            )
            
            if response.embedding and response.embedding.values:
                return response.embedding.values
            else:
                print(f"ERROR: 获取嵌入向量失败 - 响应中不包含有效的嵌入数据。响应: {response}")
                raise Exception("未能获取有效的嵌入向量")
                
        except Exception as e:
            print(f"ERROR: 获取嵌入向量时发生异常: {e}")
            traceback.print_exc()
            raise e
    
    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取嵌入向量"""
        embeddings = []
        
        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(5)
        
        async def get_single_embedding(text):
            async with semaphore:
                return await self.get_embedding(text)
        
        embeddings = await asyncio.gather(*[get_single_embedding(text) for text in texts])
        return embeddings
    
    def calculate_cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算余弦相似度"""
        import math
        
        # 计算点积
        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
        
        # 计算向量长度
        magnitude1 = math.sqrt(sum(a * a for a in embedding1))
        magnitude2 = math.sqrt(sum(a * a for a in embedding2))
        
        # 避免除零
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    async def semantic_search(self, query: str, documents: List[str], 
                            top_k: int = 5) -> List[Tuple[int, str, float]]:
        """语义搜索"""
        # 获取查询的嵌入向量
        query_embedding = await self.get_embedding(query)
        
        # 获取所有文档的嵌入向量
        doc_embeddings = await self.get_embeddings_batch(documents)
        
        # 计算相似度
        similarities = []
        for i, doc_embedding in enumerate(doc_embeddings):
            similarity = self.calculate_cosine_similarity(query_embedding, doc_embedding)
            similarities.append((i, documents[i], similarity))
        
        # 按相似度排序并返回top_k
        similarities.sort(key=lambda x: x[2], reverse=True)
        return similarities[:top_k]
    
    async def cluster_texts(self, texts: List[str], num_clusters: int = 3) -> Dict[int, List[int]]:
        """文本聚类"""
        # 获取所有文本的嵌入向量
        embeddings = await self.get_embeddings_batch(texts)
        
        # 简单的K-means聚类实现
        import random
        
        # 随机初始化聚类中心
        centroids = random.sample(embeddings, num_clusters)
        
        for _ in range(10):  # 最多迭代10次
            clusters = {i: [] for i in range(num_clusters)}
            
            # 分配每个点到最近的聚类中心
            for i, embedding in enumerate(embeddings):
                distances = [
                    1 - self.calculate_cosine_similarity(embedding, centroid)
                    for centroid in centroids
                ]
                closest_cluster = distances.index(min(distances))
                clusters[closest_cluster].append(i)
            
            # 更新聚类中心
            new_centroids = []
            for cluster_id in range(num_clusters):
                if clusters[cluster_id]:
                    # 计算聚类中心
                    cluster_embeddings = [embeddings[i] for i in clusters[cluster_id]]
                    centroid = [
                        sum(emb[j] for emb in cluster_embeddings) / len(cluster_embeddings)
                        for j in range(len(cluster_embeddings[0]))
                    ]
                    new_centroids.append(centroid)
                else:
                    new_centroids.append(centroids[cluster_id])
            
            centroids = new_centroids
        
        return clusters
    
    async def find_duplicate_texts(self, texts: List[str], 
                                 similarity_threshold: float = 0.9) -> List[Tuple[int, int, float]]:
        """找到重复文本"""
        embeddings = await self.get_embeddings_batch(texts)
        duplicates = []
        
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                similarity = self.calculate_cosine_similarity(embeddings[i], embeddings[j])
                if similarity >= similarity_threshold:
                    duplicates.append((i, j, similarity))
        
        return duplicates
    
    def get_embedding_stats(self, embeddings: List[List[float]]) -> Dict[str, Any]:
        """获取嵌入统计信息"""
        if not embeddings:
            return {"count": 0}
        
        import statistics
        
        # 计算维度
        dimension = len(embeddings[0])
        
        # 计算每个维度的统计信息
        dimension_stats = []
        for dim in range(dimension):
            values = [emb[dim] for emb in embeddings]
            dimension_stats.append({
                "mean": statistics.mean(values),
                "median": statistics.median(values),
                "std": statistics.stdev(values) if len(values) > 1 else 0
            })
        
        return {
            "count": len(embeddings),
            "dimension": dimension,
            "dimension_stats": dimension_stats
        }
    
    # ========================================
    # 工具方法
    # ========================================
    
    def log_success(self, operation: str, details: str = ""):
        """记录成功日志"""
        print(f"SUCCESS: {operation}" + (f" - {details}" if details else ""))
    
    def log_error(self, operation: str, error: Exception):
        """记录错误日志"""
        print(f"ERROR: {operation} - {error}")
        traceback.print_exc()

# 创建全局实例，保持与原有代码的兼容性
llm_service = LLMService()
</file>

<file path="backend/src/services/prompt_assembler.py">
#!/usr/bin/env python3
"""
PromptAssembler - RAG记忆系统的核心组件
负责动态组装上下文，集成RAG检索结果、对话摘要和近期历史
"""

import asyncio
from typing import List, Dict, Any, Optional, Literal
from .llm_service import llm_service
from .supabase_service import supabase_service
from .summarization_service import summarization_service
from src.prompt_templates import ADVANCED_STORY_CHAT_PROMPT


class PromptAssembler:
    """
    动态提示词组装器
    将角色设定、RAG记忆、对话摘要和近期历史智能组合
    """
    
    def __init__(self):
        self.max_context_tokens = 8000  # 预留给上下文的最大token数
        self.recent_messages_limit = 10  # 近期消息数量限制
        self.rag_memories_limit = 5     # RAG检索记忆数量限制

    async def build_fast_prompt(
        self,
        chat_id: str,
        user_message: str,
        agent_id: str,
        mode: Literal['chat', 'story'] = 'chat'
    ) -> str:
        """
        构建快速提示词，不包含RAG搜索，用于立即响应

        Args:
            chat_id: 会话ID
            user_message: 用户当前输入
            agent_id: AI角色ID
            mode: 对话模式，'chat'为角色聊天，'story'为故事模式

        Returns:
            快速构建的提示词字符串
        """
        try:
            # 1. 获取模式感知的角色配置
            agent_info = await self._get_agent_with_mode_config(agent_id, mode)
            if not agent_info:
                raise Exception(f"未找到角色 {agent_id}")

            # 2. 并行获取基础数据（不包含需要embedding的RAG搜索）
            tasks = {
                "history": supabase_service.get_messages_by_chat_id(
                    chat_id=chat_id,
                    limit=self.recent_messages_limit
                ),
                "world_info": self._get_world_info_entries(agent_id, user_message)
            }

            # 3. 根据模式决定是否获取摘要或故事任务
            if mode == 'chat':
                tasks["summary"] = summarization_service.get_chat_summary(chat_id)
            elif mode == 'story':
                tasks["story_chapter"] = self._get_story_chapter_info(chat_id)

            # 执行所有查询
            results = await asyncio.gather(*[tasks[key] for key in tasks.keys()])
            # 将结果重新映射回字典
            results_map = dict(zip(tasks.keys(), results))

            # 添加空的RAG结果
            results_map["rag"] = []

            # 4. 组装最终提示词
            prompt = await self._assemble_final_prompt_v3(
                agent_info=agent_info,
                results_map=results_map,
                user_message=user_message,
                mode=mode,
                chat_id=chat_id
            )

            print(f"SUCCESS: 为会话 {chat_id} 构建了{mode}模式的快速提示词（无RAG）")
            return prompt

        except Exception as e:
            print(f"ERROR: 构建快速提示词失败: {e}")
            # 降级方案：返回基础提示词
            return await self._build_fallback_prompt(agent_id, user_message)
        
    async def build_prompt(
        self,
        chat_id: str,
        user_message: str,
        agent_id: str,
        mode: Literal['chat', 'story'] = 'chat'
    ) -> str:
        """
        V3版: 根据模式 (mode) 动态构建上下文

        Args:
            chat_id: 会话ID
            user_message: 用户当前输入
            agent_id: AI角色ID
            mode: 对话模式，'chat'为角色聊天，'story'为故事模式

        Returns:
            完整的提示词字符串
        """
        try:
            # 1. 获取模式感知的角色配置
            agent_info = await self._get_agent_with_mode_config(agent_id, mode)
            if not agent_info:
                raise Exception(f"未找到角色 {agent_id}")

            # 2. 生成用户消息的向量表示
            user_embedding = await llm_service.get_embedding(user_message)

            # 3. 并行获取通用数据
            tasks = {
                "rag": supabase_service.search_chat_memories(
                    chat_id=chat_id,
                    query_embedding=user_embedding,
                    match_threshold=0.75,
                    match_count=self.rag_memories_limit
                ),
                "history": supabase_service.get_messages_by_chat_id(
                    chat_id=chat_id,
                    limit=self.recent_messages_limit
                ),
                "world_info": self._get_world_info_entries(agent_id, user_message)
            }

            # 4. 根据模式决定是否获取摘要或故事任务
            if mode == 'chat':
                tasks["summary"] = summarization_service.get_chat_summary(chat_id)
            elif mode == 'story':
                # 在故事模式下，我们获取章节任务和游戏状态
                tasks["story_chapter"] = self._get_story_chapter_info(chat_id)
                tasks["game_state"] = self._get_game_state(chat_id)

            # 执行所有查询
            results = await asyncio.gather(*[tasks[key] for key in tasks.keys()])
            # 将结果重新映射回字典
            results_map = dict(zip(tasks.keys(), results))

            # 5. 组装最终提示词
            prompt = await self._assemble_final_prompt_v3(
                agent_info=agent_info,
                results_map=results_map,
                user_message=user_message,
                mode=mode,
                chat_id=chat_id
            )
            
            print(f"SUCCESS: 为会话 {chat_id} 构建了{mode}模式的提示词")
            return prompt

        except Exception as e:
            print(f"ERROR: 构建提示词失败: {e}")
            # 降级方案：返回基础提示词
            return await self._build_fallback_prompt(agent_id, user_message)
    

    
    async def _assemble_final_prompt(
        self,
        agent_info: Dict[str, Any],
        relevant_memories: List[Dict[str, Any]],
        chat_summary: Optional[str],
        recent_messages: List[Dict[str, Any]],
        user_message: str
    ) -> str:
        """组装最终的提示词"""
        
        prompt_parts = []
        
        # 1. 角色核心设定
        prompt_parts.append("# 角色设定")
        prompt_parts.append(f"你是 {agent_info['name']}。")
        
        if agent_info.get('personality'):
            prompt_parts.append(f"性格特征：{agent_info['personality']}")
        
        if agent_info.get('scenario'):
            prompt_parts.append(f"场景设定：{agent_info['scenario']}")
        
        if agent_info.get('system_prompt'):
            prompt_parts.append(f"系统指令：{agent_info['system_prompt']}")
        elif agent_info.get('roleplay_prompt'):
            prompt_parts.append(f"角色扮演指令：{agent_info['roleplay_prompt']}")
        
        # 2. 对话摘要（宏观背景）
        if chat_summary:
            prompt_parts.append("\n# 对话背景")
            prompt_parts.append(f"之前的对话摘要：{chat_summary}")
        
        # 3. 相关记忆片段（RAG检索结果）
        if relevant_memories:
            prompt_parts.append("\n# 相关记忆")
            prompt_parts.append("以下是与当前话题相关的对话片段：")
            for i, memory in enumerate(relevant_memories, 1):
                similarity = memory.get('similarity', 0)
                content = memory.get('content', '')
                prompt_parts.append(f"{i}. (相似度: {similarity:.2f}) {content}")
        
        # 4. 近期对话历史
        if recent_messages:
            prompt_parts.append("\n# 近期对话")
            for msg in recent_messages[-5:]:  # 只显示最近5条
                role = "用户" if msg['role'] == 'user' else agent_info['name']
                prompt_parts.append(f"{role}: {msg['content']}")
        
        # 5. 当前用户输入
        prompt_parts.append(f"\n用户: {user_message}")
        
        # 6. 回复指令
        prompt_parts.append(f"\n请以 {agent_info['name']} 的身份回复。回复要自然、生动，符合角色设定。")
        
        return "\n".join(prompt_parts)
    
    async def _build_fallback_prompt(self, agent_id: str, user_message: str) -> str:
        """降级方案：构建基础提示词"""
        try:
            agent_info = await supabase_service.get_agent_by_id(agent_id)
            if not agent_info:
                return f"用户: {user_message}\n\n请回复用户的消息。"
            
            prompt = f"你是 {agent_info['name']}。\n"
            if agent_info.get('roleplay_prompt'):
                prompt += f"{agent_info['roleplay_prompt']}\n"
            prompt += f"\n用户: {user_message}\n\n请回复："
            
            return prompt
        except Exception as e:
            print(f"ERROR: 构建降级提示词失败: {e}")
            return f"用户: {user_message}\n\n请回复用户的消息。"

    async def _get_agent_with_mode_config(self, agent_id: str, mode: str) -> Optional[Dict[str, Any]]:
        """获取模式感知的角色配置"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.rpc('get_agent_with_mode_config', {
                    'p_agent_id': agent_id,
                    'p_mode': mode
                }).execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"WARN: 获取模式感知角色配置失败，回退到基础配置: {e}")
            # 回退到基础的get_agent_by_id
            return await supabase_service.get_agent_by_id(agent_id)

    async def _get_world_info_entries(self, agent_id: str, user_message: str) -> List[Dict[str, Any]]:
        """获取世界书条目"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.rpc('get_active_world_info_entries', {
                    'p_agent_id': agent_id,
                    'p_search_text': user_message
                }).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"WARN: 获取世界书条目失败: {e}")
            return []

    async def _get_story_chapter_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取故事章节信息"""
        try:
            # 获取聊天详情以获取story_id
            chat_details = await supabase_service.get_chat_by_id(chat_id)
            if not chat_details or not chat_details.get('story_id'):
                return None

            # 获取故事章节
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("story_chapters")
                .select("*")
                .eq("story_id", chat_details['story_id'])
                .order("created_at", desc=False)
                .limit(1)
                .execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"WARN: 获取故事章节信息失败: {e}")
            return None

    async def _get_game_state(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取游戏状态"""
        try:
            chat_details = await supabase_service.get_chat_by_id(chat_id)
            if not chat_details:
                return None
            return chat_details.get('game_state', {})
        except Exception as e:
            print(f"WARN: 获取游戏状态失败: {e}")
            return {}

    def _format_relationship_status(self, game_state: Optional[Dict], agent_name: str, fallback_instructions: str) -> str:
        """将游戏状态转换为自然语言关系描述"""
        if not game_state:
            return fallback_instructions or "你对主角的看法是中立的。"

        descriptions = []
        for key, value in game_state.items():
            if key.startswith(f"{agent_name}."):
                variable = key.split('.')[1]
                # 在这里添加规则来将数值转换为自然语言
                if variable == "好感度":
                    if value < 30:
                        descriptions.append(f"你对主角的好感度很低({value}/100)，你感到不信任和警惕。")
                    elif value > 70:
                        descriptions.append(f"你对主角的好感度很高({value}/100)，你感到亲近和信任。")
                    else:
                        descriptions.append(f"你对主角的好感度一般({value}/100)。")
                elif variable == "误解程度":
                    if value > 70:
                        descriptions.append(f"你对主角存在深度误解({value}/100)，这影响着你的判断。")
                    elif value < 30:
                        descriptions.append(f"你对主角的误解已经基本消除({value}/100)。")
                    else:
                        descriptions.append(f"你对主角仍有一些误解({value}/100)。")
                elif variable == "信任度":
                    if value < 30:
                        descriptions.append(f"你对主角缺乏信任({value}/100)，保持着防备心理。")
                    elif value > 70:
                        descriptions.append(f"你对主角非常信任({value}/100)，愿意敞开心扉。")
                    else:
                        descriptions.append(f"你对主角的信任度一般({value}/100)。")
                # 可以为其他变量添加更多规则

        return " ".join(descriptions) if descriptions else fallback_instructions

    async def _assemble_final_prompt_v3(
        self,
        agent_info: Dict[str, Any],
        results_map: Dict[str, Any],
        user_message: str,
        mode: str,
        chat_id: str
    ) -> str:
        """V3版本的提示词组装器"""

        # 在故事模式下获取主角信息
        protagonist_info = None
        if mode == 'story':
            protagonist_info = await self._get_protagonist_info(chat_id)

        prompt_parts = {
            "roleplay_instructions": await self._format_roleplay_instructions(agent_info, mode, protagonist_info),
            "core_persona": self._format_persona(agent_info),
            "world_info": self._format_world_info(results_map.get("world_info", [])),
            "long_term_memory": "",  # 根据模式填充
            "short_term_memory": self._format_rag_memory(results_map.get("rag", []), agent_info),
            "recent_history": await self._format_history_with_protagonist(results_map.get("history", []), agent_info['name'], mode, protagonist_info)
        }

        # 根据模式使用不同的提示词模板
        if mode == 'story':
            # 故事模式使用新的高级模板
            story_chapter = results_map.get("story_chapter", {})
            game_state = results_map.get("game_state", {})

            # 从 agent_info 中获取预生成的初始关系描述作为后备
            fallback_instructions = agent_info.get('effective_instructions', '')

            # 格式化关系状态
            relationship_status = self._format_relationship_status(game_state, agent_info['name'], fallback_instructions)

            # 格式化对话历史
            chat_history = await self._format_history_with_protagonist(
                results_map.get("history", []), agent_info['name'], mode, protagonist_info
            )

            # 使用高级故事模板
            final_prompt = ADVANCED_STORY_CHAT_PROMPT.format(
                agent_name=agent_info['name'],
                agent_persona=self._format_persona(agent_info),
                world_info=self._format_world_info(results_map.get("world_info", [])),
                chapter_title=story_chapter.get('title', '未知章节'),
                mission_objective=story_chapter.get('mission_objective_text', '无'),
                relationship_status=relationship_status,
                chapter_event_summary=story_chapter.get('chapter_event_summary', '无特定事件。'),
                chat_history=chat_history,
                user_message=user_message
            )
            return final_prompt.strip()
        else:
            # 聊天模式使用原有逻辑
            summary_data = results_map.get("summary")
            summary_text = summary_data if isinstance(summary_data, str) else (summary_data.get('relationship_summary') or summary_data.get('summary_text') if summary_data else None)
            prompt_parts["long_term_memory"] = f"# 长期记忆摘要 (我们的对话概要)\n{summary_text or '这是我们对话的开始。'}"

            # 构建最终Prompt
            user_display_name = protagonist_info['name'] if protagonist_info else "User"
            final_prompt = f"""{prompt_parts["roleplay_instructions"]}

{prompt_parts["core_persona"]}

{prompt_parts["world_info"]}

{prompt_parts["long_term_memory"]}

{prompt_parts["short_term_memory"]}

# 近期对话历史
{prompt_parts["recent_history"]}
{user_display_name}: {user_message}
{agent_info['name']}
"""
            return final_prompt.strip()

    def _format_persona(self, agent: Dict[str, Any]) -> str:
        """格式化角色设定 - 模式感知版本"""
        parts = ["# 角色核心设定"]
        if agent.get('description'):
            parts.append(f"描述: {agent['description']}")
        if agent.get('personality'):
            parts.append(f"人格: {agent['personality']}")

        # 使用模式感知的场景设定
        effective_scenario = agent.get('effective_scenario') or agent.get('scenario')
        if effective_scenario:
            parts.append(f"场景: {effective_scenario}")

        # 根据模式决定是否包含背景故事
        if agent.get('should_use_backstory') and agent.get('backstory_text'):
            parts.append(f"背景故事: {agent['backstory_text']}")

        return "\n".join(parts)

    def _format_world_info(self, world_info_entries: List[Dict[str, Any]]) -> str:
        """格式化世界书信息"""
        if not world_info_entries:
            return ""
        entries = "\n".join([f"- {entry['content']}" for entry in world_info_entries])
        return f"# 世界背景与知识 (动态激活)\n{entries}"

    def _format_rag_memory(self, memories: List[Dict[str, Any]], agent_info: Dict[str, Any] = None) -> str:
        """格式化RAG记忆 - 模式感知版本"""
        memory_parts = []

        if memories:
            entries = "\n".join([f"- {mem['content']}" for mem in memories])
            memory_parts.append(f"# 精准记忆片段 (相关对话细节)\n{entries}")

        # 根据模式决定是否包含对话示例（弱化权重，仅供模仿语气）
        if agent_info and agent_info.get('should_use_mes_example') and agent_info.get('mes_example'):
            memory_parts.append(f"# 对话风格参考 (仅供模仿语气，不要照搬内容)\n{agent_info['mes_example']}")

        return "\n\n".join(memory_parts) if memory_parts else ""

    async def _get_protagonist_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取故事中的主角信息"""
        try:
            # 获取聊天详情以获取story_id
            chat_details = await supabase_service.get_chat_by_id(chat_id)
            if not chat_details or not chat_details.get('story_id'):
                return None

            # 获取故事详情以获取protagonist_agent_id
            story_detail = await supabase_service.get_story_by_id(chat_details['story_id'])
            if not story_detail or not story_detail.get('protagonist_agent_id'):
                return None

            # 获取主角Agent信息
            protagonist_agent = await supabase_service.get_agent_by_id(story_detail['protagonist_agent_id'])
            return protagonist_agent
        except Exception as e:
            print(f"WARN: 获取主角信息失败: {e}")
            return None

    async def _format_roleplay_instructions(self, agent_info: Dict[str, Any], mode: str, protagonist_info: Optional[Dict[str, Any]]) -> str:
        """格式化角色扮演指令"""
        if mode == 'story' and protagonist_info:
            return f"""你正在扮演 {agent_info['name']}。严格遵守你的角色核心设定。

# 你正在和女主角 {protagonist_info['name']} 对话。
# 以下对话历史中，"{protagonist_info['name']}" 代表玩家扮演的女主角。"""
        else:
            return f"你正在扮演 {agent_info['name']}。严格遵守你的角色核心设定。"

    async def _format_history_with_protagonist(self, messages: List[Dict[str, Any]], agent_name: str, mode: str, protagonist_info: Optional[Dict[str, Any]]) -> str:
        """格式化对话历史，在故事模式下将用户消息显示为主角"""
        if not messages:
            return ""

        formatted_messages = []
        for msg in messages[-5:]:  # 只显示最近5条
            if msg['role'] == 'user':
                # 在故事模式下，用户消息显示为主角的话
                if mode == 'story' and protagonist_info:
                    formatted_messages.append(f"{protagonist_info['name']}: {msg['content']}")
                else:
                    formatted_messages.append(f"用户: {msg['content']}")
            else:
                formatted_messages.append(f"{agent_name}: {msg['content']}")

        return "\n".join(formatted_messages)

    def _format_history(self, messages: List[Dict[str, Any]], agent_name: str) -> str:
        """格式化对话历史"""
        if not messages:
            return ""
        return "\n".join([f"{'User' if msg['role'] == 'user' else agent_name}: {msg['content']}" for msg in reversed(messages)])


# 全局实例
prompt_assembler = PromptAssembler()
</file>

<file path="backend/src/services/summarization_service.py">
#!/usr/bin/env python3
"""
Summarization Service - 滚动摘要与长期记忆系统
负责生成和维护对话的滚动摘要，为AI提供宏观的对话背景
"""

import asyncio
from typing import List, Dict, Any, Optional
from .llm_service import llm_service
from .supabase_service import supabase_service


class SummarizationService:
    """
    滚动摘要服务
    维护对话的长期记忆，通过摘要的方式压缩历史信息
    """
    
    def __init__(self):
        self.summary_trigger_threshold = 15  # 每15条新消息触发一次摘要更新
        self.max_summary_length = 1000      # 摘要最大长度（字符）
        
    async def should_update_summary(self, chat_id: str) -> bool:
        """
        判断是否需要更新摘要
        基于新消息数量来决定
        """
        try:
            # 获取当前摘要信息
            summary_info = await self._get_summary_info(chat_id)
            
            if not summary_info:
                # 如果没有摘要，检查是否有足够的消息来创建第一个摘要
                total_messages = await self._count_total_messages(chat_id)
                return total_messages >= self.summary_trigger_threshold
            
            # 计算自上次摘要以来的新消息数量
            last_summarized_id = summary_info.get('last_summarized_message_id', 0)
            new_messages_count = await self._count_messages_since(chat_id, last_summarized_id)
            
            return new_messages_count >= self.summary_trigger_threshold
            
        except Exception as e:
            print(f"ERROR: 检查摘要更新条件失败: {e}")
            return False
    
    async def update_chat_summary(self, chat_id: str) -> bool:
        """
        更新指定会话的摘要
        """
        try:
            print(f"INFO: 开始为会话 {chat_id} 更新摘要...")
            
            # 获取现有摘要信息
            summary_info = await self._get_summary_info(chat_id)
            old_summary = summary_info.get('summary_text', '') if summary_info else ''
            last_summarized_id = summary_info.get('last_summarized_message_id', 0) if summary_info else 0
            
            # 获取需要摘要的新消息
            new_messages = await self._get_messages_since(chat_id, last_summarized_id)
            
            if not new_messages:
                print(f"WARN: 会话 {chat_id} 没有新消息需要摘要")
                return False
            
            # 生成新的摘要
            new_summary = await self._generate_summary(old_summary, new_messages)
            
            # 保存摘要到数据库
            latest_message_id = new_messages[-1]['id']
            success = await self._save_summary(chat_id, new_summary, latest_message_id)
            
            if success:
                print(f"SUCCESS: 会话 {chat_id} 的摘要已更新，包含 {len(new_messages)} 条新消息")
                return True
            else:
                print(f"ERROR: 保存会话 {chat_id} 的摘要失败")
                return False
                
        except Exception as e:
            print(f"ERROR: 更新会话 {chat_id} 的摘要失败: {e}")
            return False
    
    async def get_chat_summary(self, chat_id: str) -> Optional[str]:
        """
        获取指定会话的最新摘要
        """
        try:
            summary_info = await self._get_summary_info(chat_id)
            return summary_info.get('summary_text') if summary_info else None
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 的摘要失败: {e}")
            return None
    
    async def _get_summary_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取摘要信息"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("chat_summaries")
                .select("*")
                .eq("chat_id", chat_id)
                .execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"ERROR: 获取摘要信息失败: {e}")
            return None
    
    async def _count_total_messages(self, chat_id: str) -> int:
        """统计会话的总消息数"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("messages")
                .select("id", count="exact")
                .eq("chat_id", chat_id)
                .execute()
            )
            return response.count or 0
        except Exception as e:
            print(f"ERROR: 统计消息数量失败: {e}")
            return 0
    
    async def _count_messages_since(self, chat_id: str, since_message_id: int) -> int:
        """统计自指定消息ID以来的新消息数量"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("messages")
                .select("id", count="exact")
                .eq("chat_id", chat_id)
                .gt("id", since_message_id)
                .execute()
            )
            return response.count or 0
        except Exception as e:
            print(f"ERROR: 统计新消息数量失败: {e}")
            return 0
    
    async def _get_messages_since(self, chat_id: str, since_message_id: int) -> List[Dict[str, Any]]:
        """获取自指定消息ID以来的所有新消息"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("messages")
                .select("*")
                .eq("chat_id", chat_id)
                .gt("id", since_message_id)
                .order("created_at", desc=False)
                .execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取新消息失败: {e}")
            raise e
    
    async def _generate_summary(self, old_summary: str, new_messages: List[Dict[str, Any]]) -> str:
        """
        生成新的摘要
        结合旧摘要和新消息，生成更新后的摘要
        """
        try:
            # 构建摘要提示词
            messages_text = "\n".join([
                f"{'用户' if msg['role'] == 'user' else 'AI'}: {msg['content']}"
                for msg in new_messages
            ])
            
            if old_summary:
                prompt = f"""请基于以下【旧摘要】和【新对话】，生成一个更新后的、更全面的对话摘要。

【旧摘要】：
{old_summary}

【新对话】：
{messages_text}

请生成一个简洁但全面的摘要，包含：
1. 对话的主要话题和发展脉络
2. 重要的事实信息和细节
3. 角色之间的关系变化
4. 任何重要的决定或约定

摘要应该保持在{self.max_summary_length}字符以内，重点突出最重要的信息。"""
            else:
                prompt = f"""请为以下对话生成一个简洁但全面的摘要：

【对话内容】：
{messages_text}

请生成一个摘要，包含：
1. 对话的主要话题
2. 重要的事实信息和细节
3. 角色之间的互动情况
4. 任何重要的决定或约定

摘要应该保持在{self.max_summary_length}字符以内。"""
            
            # 调用LLM生成摘要
            summary = await llm_service.generate_text_response(prompt)
            
            # 确保摘要长度不超过限制
            if len(summary) > self.max_summary_length:
                summary = summary[:self.max_summary_length] + "..."
            
            return summary.strip()
            
        except Exception as e:
            print(f"ERROR: 生成摘要失败: {e}")
            # 不再返回降级摘要，而是将异常向上抛出
            raise e
    
    async def _save_summary(self, chat_id: str, summary_text: str, last_message_id: int) -> bool:
        """保存摘要到数据库"""
        try:
            # 使用upsert操作，如果存在则更新，不存在则插入
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("chat_summaries")
                .upsert({
                    "chat_id": chat_id,
                    "summary_text": summary_text,
                    "last_summarized_message_id": last_message_id,
                    "updated_at": "NOW()"
                })
                .execute()
            )
            return bool(response.data)
        except Exception as e:
            print(f"ERROR: 保存摘要失败: {e}")
            return False


# 全局实例
summarization_service = SummarizationService()
</file>

<file path="backend/src/services/supabase_service.py">
#!/usr/bin/env python3
"""
Supabase服务层 - V5.0 重构版
处理所有数据库操作，基于统一的消息流模型。
"""

import os
import asyncio
import traceback
from typing import List, Dict, Any, Optional
import httpx
from supabase import create_client, Client, ClientOptions
from postgrest.exceptions import APIError
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# --- 通用重试装饰器 ---
def retry_on_db_error(retries=3, delay=1, backoff=2):
    """
    一个装饰器，用于在数据库操作失败时重试。
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if "EOF occurred in violation of protocol" in str(e) or "connection closed" in str(e).lower():
                        print(f"WARN: 在 {func.__name__} 中检测到数据库连接错误 (尝试 {attempt + 1}/{retries})。正在重试...")
                        await asyncio.sleep(delay * (backoff ** attempt))
                    else:
                        print(f"ERROR: 在 {func.__name__} 中发生非重试类型的数据库错误: {e}")
                        raise e
            print(f"ERROR: 在 {func.__name__} 中，所有重试均失败。最后一次错误: {last_exception}")
            raise last_exception
        return wrapper
    return decorator

class SupabaseService:
    """封装所有Supabase数据库操作"""
    
    def __init__(self):
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("ERROR: 缺少Supabase配置，请检查.env文件中的SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY")
        
        # 最终修复方案: 根据 supabase-py 源码，直接注入一个自定义配置的 httpx 客户端
        # 这是解决 "EOF occurred" 和 "TypeError" 的根本方法。
        # 增加超时时间以解决网络超时问题
        httpx_client = httpx.Client(
            http2=False,
            timeout=httpx.Timeout(
                connect=30.0,  # 连接超时：30秒（默认5秒）
                read=60.0,     # 读取超时：60秒（默认5秒）
                write=30.0,    # 写入超时：30秒（默认5秒）
                pool=120.0     # 连接池超时：120秒（默认5秒）
            ),
            limits=httpx.Limits(
                max_connections=100,    # 最大连接数
                max_keepalive_connections=20  # 最大保持连接数
            )
        )
        opts = ClientOptions(
            httpx_client=httpx_client,
            postgrest_client_timeout=60.0,  # PostgREST客户端超时：60秒
            storage_client_timeout=60.0,    # 存储客户端超时：60秒
            function_client_timeout=60.0    # 函数客户端超时：60秒
        )

        self.supabase: Client = create_client(supabase_url, supabase_key, options=opts)
        print("INFO: Supabase客户端初始化成功 (已通过自定义httpx客户端禁用HTTP/2，并优化超时设置)")

    # ========================================
    # 用户档案相关操作
    # ========================================
    
    async def get_user_profile_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID获取用户档案"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("user_profiles").select("*").eq("user_id", user_id).maybe_single().execute()
            )
            if response:
                return response.data
            return None
        except Exception as e:
            print(f"ERROR: 获取用户档案失败: {e}")
            traceback.print_exc()
            return None

    # ========================================
    # 智能体 (模板) 相关操作
    # ========================================
    
    async def get_agents(self, user_id: Optional[str] = None, is_public: Optional[bool] = None, limit: int = 50, include_system: bool = False) -> List[Dict[str, Any]]:
        """获取智能体列表，并包含创作者信息"""
        try:
            # --- ▼▼▼ 修改开始 ▼▼▼ ---
            # 直接调用已经存在的、能正确获取创作者信息的RPC函数
            # 注意：这个RPC函数自带了is_public=true的筛选和排序，所以下面的代码需要调整
            if is_public:
                response = await asyncio.to_thread(
                    lambda: self.supabase.rpc('get_public_agents_with_creator', {'p_limit': limit}).execute()
                )
                return response.data or []

            # 如果不是只查询public的，则回退到原来的查询逻辑，但去掉错误的关联查询
            else:
                query = self.supabase.table("agents").select("*") # 去掉了 ", user_profiles(display_name)"
                if not include_system:
                    query = query.eq("is_system_agent", False)
                if user_id:
                    query = query.eq("user_id", user_id)
                query = query.order("created_at", desc=True).limit(limit)
                response = await asyncio.to_thread(lambda: query.execute())
                return response.data or []
            # --- ▲▲▲ 修改结束 ▲▲▲ ---
        except Exception as e:
            print(f"ERROR: 获取智能体列表失败: {e}")
            raise e

    async def get_agent_by_id(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取智能体详情 - 修复版"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agents").select("*").eq("id", agent_id).maybe_single().execute()
            )
            
            # 增加对response对象本身的检查
            if response and hasattr(response, 'data'):
                return response.data
            return None
            
        except APIError as e:
            print(f"ERROR: 获取智能体详情时发生API错误: {e.message}")
            return None
        except Exception as e:
            print(f"ERROR: 获取智能体详情时发生未知错误: {e}")
            traceback.print_exc()
            return None

    async def create_agent(self, user_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """创建新智能体（支持UCIT格式）"""
        try:
            agent_data = {"user_id": user_id, **kwargs}
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agents").insert(agent_data, returning="representation").execute()
            )
            if response.data:
                print(f"SUCCESS: 智能体 '{kwargs.get('name')}' 成功存入数据库。")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 创建智能体时发生数据库异常: {e}")
            return None

    async def create_agent_from_character_card(self, user_id: str, character_data: Dict[str, Any], image_url: Optional[str], spec: str = "chara_card_v2", spec_version: str = "2.0") -> Optional[Dict[str, Any]]:
        """从角色卡数据创建智能体"""
        try:
            # 1. 定义已映射到专用列的字段，用于后续去重
            mapped_keys = {
                "name", "description", "personality", "scenario", "first_mes",
                "mes_example", "creator_notes", "system_prompt",
                "post_history_instructions", "tags", "gender", "voice_name",
                # 即使它们可能不存在于原始卡中，也列出来以防万一
                "image_url", "avatar_url", "is_public"
            }

            # 2. 准备要插入数据库的数据
            agent_data = {
                "user_id": user_id,
                "name": character_data.get("name", "未命名角色"),
                "description": character_data.get("description", ""),
                "personality": character_data.get("personality", ""),
                "scenario": character_data.get("scenario", ""),
                "first_mes": character_data.get("first_mes", ""),
                "mes_example": character_data.get("mes_example", ""),
                "creator_notes": character_data.get("creator_notes", ""),
                "system_prompt": character_data.get("system_prompt", ""),
                "post_history_instructions": character_data.get("post_history_instructions", ""),
                "tags": character_data.get("tags", []),
                "image_url": image_url,
                "avatar_url": image_url,
                "spec": spec,
                "spec_version": spec_version,
                "is_public": True,
                "gender": character_data.get("gender", "other"),
                "voice_name": character_data.get("voice_name", "Kore")
            }

            # 3. 创建一个副本用于存储到 'data' 字段，并移除已映射的键
            supplementary_data = character_data.copy()
            for key in mapped_keys:
                supplementary_data.pop(key, None)
            
            agent_data["data"] = supplementary_data

            # 4. 执行插入
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agents").insert(agent_data, returning="representation").execute()
            )
            if response.data:
                print(f"SUCCESS: 角色卡 '{character_data.get('name')}' 成功导入数据库。")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 导入角色卡时发生数据库异常: {e}")
            return None

    def _generate_default_roleplay_prompt(self, character_data: Dict[str, Any]) -> str:
        """为没有system_prompt的角色卡生成默认的roleplay_prompt"""
        name = character_data.get("name", "角色")
        personality = character_data.get("personality", "")
        scenario = character_data.get("scenario", "")

        prompt = f"你是{name}。"
        if personality:
            prompt += f"\n\n性格特征：{personality}"
        if scenario:
            prompt += f"\n\n场景设定：{scenario}"

        prompt += f"\n\n请始终保持{name}的角色设定，用第一人称进行对话。回复要生动自然，符合角色的性格特点。"

        return prompt

    # ========================================
    # 故事 (模板) 相关操作
    # ========================================
    
    async def get_stories(self, user_id: Optional[str] = None, is_public: Optional[bool] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取故事列表"""
        try:
            # 核心修改：使用关联查询获取章节数量
            query = self.supabase.table("stories").select("*, story_chapters(count)")
            if user_id:
                query = query.eq("user_id", user_id)
            if is_public is not None:
                query = query.eq("is_public", is_public)
            query = query.order("created_at", desc=True).limit(limit)
            response = await asyncio.to_thread(lambda: query.execute())
            
            # 核心修改：处理返回结果，将章节数提取到 'chapter_count' 字段
            stories = response.data or []
            for story in stories:
                if 'story_chapters' in story and story['story_chapters']:
                    story['chapter_count'] = story['story_chapters'][0]['count']
                else:
                    story['chapter_count'] = 0
                story.pop('story_chapters', None)  # 清理掉原始的关联查询字段
            
            return stories
        except Exception as e:
            print(f"ERROR: 获取故事列表失败: {e}")
            raise e

    async def get_story_by_id(self, story_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取故事详情 (兼容single异常)"""
        try:
            # 首选 single()，精确获取一条记录，性能最佳
            response = await asyncio.to_thread(
                lambda: self.supabase.table("stories").select("*").eq("id", story_id).single().execute()
            )
            if response and response.data:
                return response.data
        except Exception as e:
            # 当 single() 因返回 0 条或多条记录导致 406/404 时降级为 limit(1)
            print(f"WARN: single() 查询故事 {story_id} 失败 ({e})，正回退至 limit(1) 查询以兼容 Supabase 行为变更。")
            try:
                fallback_resp = await asyncio.to_thread(
                    lambda: self.supabase.table("stories").select("*").eq("id", story_id).limit(1).execute()
                )
                return fallback_resp.data[0] if fallback_resp and fallback_resp.data else None
            except Exception as ee:
                print(f"ERROR: get_story_by_id 回退查询仍失败: {ee}")
                return None
        # 如果 single() 成功但未返回数据
        return None

    @retry_on_db_error()
    async def create_story(self, user_id: str, title: str, theme_prompt: str, **kwargs) -> Optional[Dict[str, Any]]:
        """创建新故事"""
        story_data = {"user_id": user_id, "title": title, "theme_prompt": theme_prompt, **kwargs}
        response = await asyncio.to_thread(
            lambda: self.supabase.table("stories").insert(story_data, returning="representation").execute()
        )
        return response.data[0] if response.data else None

    async def get_story_chapters(self, story_id: str) -> List[Dict[str, Any]]:
        """获取故事的所有章节，按章节号排序"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("story_chapters")
                .select("*").eq("story_id", story_id).order("chapter_number", desc=False).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取故事章节列表失败: {e}")
            raise e

    @retry_on_db_error()
    async def create_story_chapter(self, **kwargs) -> Optional[Dict[str, Any]]:
        """创建并保存一个新的故事章节。"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("story_chapters").insert(kwargs, returning="representation").execute()
            )
            if response.data:
                print(f"SUCCESS: 故事章节 '{kwargs.get('title')}' 成功存入数据库。")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 创建故事章节时发生数据库异常: {e}")
            return None

    # ========================================
    # 统一对话模型核心操作 (V5)
    # ========================================

    @retry_on_db_error()
    async def create_chat_session(self, user_id: str, agent_ids: List[str], story_id: Optional[str] = None) -> Optional[str]:
        """创建一个新的对话会话，并关联参与者，返回 chat_id"""
        try:
            # 1. 在 chats 表中插入新记录
            chat_insert_data = {"user_id": user_id}
            if story_id:
                chat_insert_data["story_id"] = story_id
            
            chat_response = await asyncio.to_thread(
                lambda: self.supabase.table("chats").insert(chat_insert_data, returning="representation").execute()
            )
            
            if not chat_response.data:
                print("ERROR: 创建chat会话失败，未返回数据。")
                return None
            
            new_chat_id = chat_response.data[0]['id']
            
            # 2. 在 chat_participants 表中为每个 agent_id 插入记录
            if agent_ids:
                participants_data = [{"chat_id": new_chat_id, "agent_id": agent_id} for agent_id in agent_ids]
                participants_response = await asyncio.to_thread(
                    lambda: self.supabase.table("chat_participants").insert(participants_data).execute()
                )
                if participants_response.data is None and participants_response.error:
                     print(f"ERROR: 关联会话参与者失败: {participants_response.error}")
                     # Consider rolling back the chat creation or handling the error
                     return None

            print(f"SUCCESS: 成功创建会话 {new_chat_id}，关联了 {len(agent_ids)} 个参与者。")
            return new_chat_id

        except Exception as e:
            print(f"ERROR: 创建会话时发生严重错误: {e}")
            traceback.print_exc()
            return None

    @retry_on_db_error()
    async def add_message_to_chat(self, chat_id: str, role: str, content: str, agent_id: Optional[str] = None, metadata: Optional[Dict] = None, audio_url: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """向指定会话添加一条新消息"""
        try:
            message_data = {
                "chat_id": chat_id,
                "role": role,
                "content": content,
                "agent_id": agent_id,
                "metadata": metadata,
                "audio_url": audio_url
            }
            response = await asyncio.to_thread(
                lambda: self.supabase.table("messages").insert(message_data, returning="representation").execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"ERROR: 添加消息到会话 {chat_id} 失败: {e}")
            return None

    @retry_on_db_error()
    async def update_message_embedding(self, message_id: int, embedding: List[float]) -> bool:
        """更新消息的向量表示"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("messages")
                .update({"embedding": embedding})
                .eq("id", message_id)
                .execute()
            )
            if response.data:
                print(f"SUCCESS: 消息 {message_id} 的embedding更新成功")
                return True
            return False
        except Exception as e:
            print(f"ERROR: 更新消息 {message_id} 的embedding失败: {e}")
            return False

    @retry_on_db_error()
    async def search_chat_memories(self, chat_id: str, query_embedding: List[float], match_threshold: float = 0.78, match_count: int = 5) -> List[Dict[str, Any]]:
        """使用向量相似度搜索聊天记忆"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc(
                    "match_chat_memories",
                    {
                        "query_embedding": query_embedding,
                        "p_chat_id": chat_id,
                        "match_threshold": match_threshold,
                        "match_count": match_count
                    }
                ).execute()
            )
            memories = response.data or []
            print(f"SUCCESS: 在会话 {chat_id} 中找到 {len(memories)} 条相关记忆")
            return memories
        except Exception as e:
            print(f"ERROR: 搜索会话 {chat_id} 的记忆失败: {e}")
            raise e

    async def get_messages_by_chat_id(self, chat_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """分页获取指定会话的历史消息"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("messages")
                .select("*")
                .eq("chat_id", chat_id)
                .order("created_at", desc=False)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 的消息失败: {e}")
            raise e

    async def get_chat_participants(self, chat_id: str) -> List[Dict[str, Any]]:
        """获取指定会话的所有AI参与者信息"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chat_participants")
                .select("agents(*)")
                .eq("chat_id", chat_id)
                .execute()
            )
            return [p['agents'] for p in response.data if p.get('agents')] if response.data else []
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 的参与者失败: {e}")
            raise e

    async def get_user_chat_list(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取用户的聊天列表 (调用数据库RPC函数)"""
        try:
            # 调用数据库中已经定义好的、能够正确获取最新消息的函数
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc(
                    'get_user_chat_list',
                    {
                        'p_user_id': user_id,
                        'p_limit': limit
                    }
                ).execute()
            )
            
            # RPC函数返回的数据结构已经符合前端预期，可以直接返回
            return response.data or []

        except Exception as e:
            print(f"ERROR: 调用 get_user_chat_list RPC 失败: {e}")
            raise e

    async def get_chat_by_id(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取单个会话的信息"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats").select("*").eq("id", chat_id).single().execute()
            )
            return response.data
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 失败: {e}")
            return None

    async def get_chat_by_user_and_story(self, user_id: str, story_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID和故事ID查找会话（若存在则返回）"""
        try:
            # 修复：使用 limit(1) 而不是 maybe_single() 来避免 Missing response 错误
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats")
                .select("*")
                .eq("user_id", user_id)
                .eq("story_id", story_id)
                .order("updated_at", desc=True)  # 按更新时间降序，获取最新的会话
                .limit(1)
                .execute()
            )
            # 如果找到记录，返回第一条；否则返回 None
            return response.data[0] if response and response.data and len(response.data) > 0 else None
        except Exception as e:
            print(f"ERROR: 获取用户 {user_id} 的故事 {story_id} 会话失败: {e}")
            return None

    async def get_chat_by_user_and_agent(self, user_id: str, agent_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID和角色ID查找会话（若存在则返回）"""
        try:
            # 修复：使用 limit(1) 而不是 maybe_single() 来避免 Missing response 错误
            # 修复：使用 is_("story_id", "null") 而不是 eq("story_id", None) 来查询NULL值
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats")
                .select("*, chat_participants!inner(agent_id)")
                .eq("user_id", user_id)
                .is_("story_id", "null")  # 修复：正确查询NULL值
                .eq("chat_participants.agent_id", agent_id)
                .order("updated_at", desc=True)  # 按更新时间降序，获取最新的会话
                .limit(1)
                .execute()
            )
            # 如果找到记录，返回第一条；否则返回 None
            return response.data[0] if response and response.data and len(response.data) > 0 else None
        except Exception as e:
            print(f"ERROR: 获取用户 {user_id} 与角色 {agent_id} 的会话失败: {e}")
            return None

    @retry_on_db_error()
    async def update_chat_progress(self, chat_id: str, progress_data: Dict) -> bool:
        """更新会话的任务进度"""
        try:
            await asyncio.to_thread(
                lambda: self.supabase.table("chats").update({"task_progress": progress_data}).eq("id", chat_id).execute()
            )
            return True
        except Exception as e:
            print(f"ERROR: 更新会话 {chat_id} 进度失败: {e}")
            return False

    @retry_on_db_error()
    async def update_chat_game_state(self, chat_id: str, game_state: Dict) -> bool:
        """织梦者引擎：更新会话的游戏状态"""
        try:
            await asyncio.to_thread(
                lambda: self.supabase.table("chats").update({"game_state": game_state}).eq("id", chat_id).execute()
            )
            return True
        except Exception as e:
            print(f"ERROR: 更新会话 {chat_id} 游戏状态失败: {e}")
            return False

    async def touch_chat(self, chat_id: str) -> bool:
        """更新指定会话的 updated_at 时间戳"""
        try:
            # 调用 update 方法但不传递任何数据，这样只会触发 update_updated_at_column 触发器
            await asyncio.to_thread(
                lambda: self.supabase.table("chats").update({}).eq("id", chat_id).execute()
            )
            return True
        except Exception as e:
            print(f"ERROR: 'Touching' chat {chat_id} failed: {e}")
            return False

    # ========================================
    # 记忆相关操作
    # ========================================
    async def create_agent_memory(self, agent_id: str, user_id: str, memory_text: str) -> Optional[Dict[str, Any]]:
        """创建智能体记忆"""
        try:
            memory_data = {"agent_id": agent_id, "user_id": user_id, "memory_text": memory_text}
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agent_memories").insert(memory_data, returning="representation").execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"ERROR: 创建记忆失败: {e}")
            return None

    async def get_agent_memories(self, agent_id: str, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取智能体记忆列表"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agent_memories")
                .select("*").eq("agent_id", agent_id).eq("user_id", user_id)
                .order("created_at", desc=True).limit(limit).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取记忆列表失败: {e}")
            return []

    # ========================================
    # 排行榜、搜索等辅助功能
    # ========================================
    async def get_story_rankings(self, period: str) -> List[Dict[str, Any]]:
        """获取故事排行榜"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc('get_story_rankings', {'p_period': period}).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取故事排行榜失败: {e}")
            raise e

    async def get_agent_rankings(self, period: str) -> List[Dict[str, Any]]:
        """获取角色排行榜"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc('get_agent_rankings', {'p_period': period}).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取角色排行榜失败: {e}")
            raise e

    # ========================================
    # 首页推荐 & 发现 - 辅助方法
    # ========================================

    async def get_public_agents_with_creator(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取公开的智能体列表，并附带创作者信息 (使用数据库RPC)"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc('get_public_agents_with_creator', {'p_limit': limit}).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取公开智能体列表失败: {e}")
            raise e
            
    async def search_agents_and_users(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """同时搜索智能体和用户"""
        try:
            search_term = f"%{query}%"
            agents_task = asyncio.to_thread(
                lambda: self.supabase.table("agents").select("*").ilike("name", search_term).limit(limit).execute()
            )
            users_task = asyncio.to_thread(
                lambda: self.supabase.table("user_profiles").select("id, user_id, display_name, avatar_url").ilike("display_name", search_term).limit(limit).execute()
            )
            agents_response, users_response = await asyncio.gather(agents_task, users_task)
            return {"agents": agents_response.data or [], "users": users_response.data or []}
        except Exception as e:
            print(f"ERROR: 搜索失败: {e}")
            raise e

    # ========================================
    # ========================================
    # Agent模式配置管理
    # ========================================
    @retry_on_db_error()
    async def create_agent_mode_config(
        self,
        agent_id: str,
        mode: str,
        mode_specific_first_mes: Optional[str] = None,
        mode_specific_scenario: Optional[str] = None,
        mode_specific_instructions: Optional[str] = None,
        enable_mes_example: bool = True,
        enable_backstory: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        创建Agent模式特定配置

        Args:
            agent_id: Agent ID
            mode: 模式 ('chat' 或 'story')
            mode_specific_first_mes: 模式特定开场白
            mode_specific_scenario: 模式特定场景
            mode_specific_instructions: 模式特定指令
            enable_mes_example: 是否启用对话示例
            enable_backstory: 是否启用背景故事

        Returns:
            创建的配置记录，失败时返回None
        """
        try:
            config_data = {
                'agent_id': agent_id,
                'mode': mode,
                'enable_mes_example': enable_mes_example,
                'enable_backstory': enable_backstory
            }

            # 只添加非空的可选字段
            if mode_specific_first_mes:
                config_data['mode_specific_first_mes'] = mode_specific_first_mes
            if mode_specific_scenario:
                config_data['mode_specific_scenario'] = mode_specific_scenario
            if mode_specific_instructions:
                config_data['mode_specific_instructions'] = mode_specific_instructions

            response = await asyncio.to_thread(
                lambda: self.supabase.table('agent_mode_configs').insert(config_data).execute()
            )

            if response.data:
                print(f"SUCCESS: Agent模式配置创建成功 - Agent: {agent_id}, Mode: {mode}")
                return response.data[0]
            else:
                print(f"ERROR: Agent模式配置创建失败 - 无数据返回")
                return None

        except Exception as e:
            print(f"ERROR: Agent模式配置创建失败: {e}")
            return None

    # ========================================
    # 羁绊系统相关操作
    # ========================================
    @retry_on_db_error()
    async def increment_bond_value(self, user_id: str, agent_id: str, amount: int = 1) -> Optional[Dict[str, Any]]:
        """通过RPC调用数据库函数，增加用户与角色的羁绊值。"""
        try:
            response = await asyncio.to_thread(lambda: self.supabase.rpc('increment_bond_value', {
                'p_user_id': user_id,
                'p_agent_id': agent_id,
                'p_increase_amount': amount
            }).execute())
            if response.data:
                print(f"INFO: 羁绊值更新成功 for user {user_id}, agent {agent_id}.")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 调用 increment_bond_value RPC 失败: {e}")
            return None

    @retry_on_db_error()
    async def get_user_agent_bond(self, user_id: str, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取用户与指定角色的羁绊详情。"""
        try:
            response = await asyncio.to_thread(lambda: self.supabase.table("user_agent_bonds")
                .select("*")
                .eq("user_id", user_id)
                .eq("agent_id", agent_id)
                .maybe_single()
                .execute())
            return response.data
        except Exception as e:
            print(f"ERROR: 获取羁绊详情失败 for user {user_id}, agent {agent_id}: {e}")
            return None

    @retry_on_db_error()
    async def get_user_all_bonds(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的所有羁绊关系。"""
        try:
            response = await asyncio.to_thread(lambda: self.supabase.table("user_agent_bonds")
                .select("*, agents(id, name, avatar_url)")
                .eq("user_id", user_id)
                .order("bond_level", desc=True)
                .order("bond_value", desc=True)
                .execute())
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取用户所有羁绊关系失败 for user {user_id}: {e}")
            return []

    # ========================================
    # 健康检查
    # ========================================
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = await asyncio.to_thread(lambda: self.supabase.rpc('health_check', {}).execute())
            if response.data is not None:
                return {"status": "healthy", "database": "connected"}
            else:
                raise Exception(f"Health check RPC call returned unexpected data: {response.data}")
        except Exception as e:
            print(f"ERROR: 数据库健康检查失败: {e}")
            return {"status": "unhealthy", "database": "disconnected", "error": str(e)}

# 创建全局服务实例
supabase_service = SupabaseService()
</file>

<file path="backend/src/supabase_main.py">
#!/usr/bin/env python3
"""
星恋 AI 后端服务 - V5.0 统一消息流重构版
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import asyncio
import uuid
import json
import base64
import traceback
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional

import os
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Query, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from PIL import Image
import io

from src.services import (
    llm_service,
    supabase_service,
    simple_imagekit_service,
    prompt_assembler,
    summarization_service,
    chat_service
)
from src.prompt_templates import STREAMING_ROLEPLAY_CHAT_PROMPT
from src.pydantic_models import CharacterCardImportRequest, ImportedAgentResponse, CharacterCardV2, TavernAICharacterCard

# 全局服务实例已通过导入获得
# llm_service 已从 src.services.llm_service 导入

# 全局并发锁已迁移到 chat_service.py

# regenerate_user_choices 函数已迁移到 chat_service.py


# --- Lifespan Manager ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("INFO: 启动 星恋AI FastAPI服务器 (V5.0 统一消息流)")
    print("=" * 50)
    health = await supabase_service.health_check()
    if health["status"] != "healthy":
        print(f"FATAL: Supabase数据库连接失败: {health.get('error')}")
    else:
        print("INFO: Supabase数据库连接成功。")
    print("INFO: 所有服务启动完成")
    yield
    print("INFO: FastAPI服务器已关闭")

# --- App 初始化 ---
app = FastAPI(title="Xingye Backend - Unified Message Stream", version="5.0.0", lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===================================================================
# API 端点 (V5 重构)
# ===================================================================

@app.get("/")
async def root():
    return {"message": "星恋 AI 后端服务运行中 - V5.0 统一消息流"}

@app.get("/health")
async def health_check():
    health = await supabase_service.health_check()
    return {
        "status": "healthy" if health["status"] == "healthy" else "unhealthy",
        "details": health
    }

# --- 新增：游客登录接口 (V4 - 缓存优先版) ---
class GuestLoginRequest(BaseModel):
    guest_id: str

@app.post("/api/auth/guest-login", response_model=Dict[str, Any])
async def guest_login(request: GuestLoginRequest):
    """游客登录接口 - V2 优化版"""
    guest_email = f"guest_{request.guest_id}@xinglian.app"
    # 使用基于 guest_id 的固定密码，确保幂等性
    guest_password = f"guest_password_{request.guest_id}" 

    try:
        # 步骤 1: 尝试直接登录
        print(f"INFO: [Guest] 尝试为游客 '{guest_email}' 直接登录...")
        session_response = await asyncio.to_thread(
            lambda: supabase_service.supabase.auth.sign_in_with_password({
                "email": guest_email,
                "password": guest_password
            })
        )
        print(f"INFO: [Guest] 游客 '{guest_email}' 登录成功。")
        return session_response.model_dump()

    except Exception as login_error:
        # 如果登录失败，很可能是用户不存在
        print(f"INFO: [Guest] 直接登录失败: {str(login_error)[:100]}... 尝试创建新游客账户。")
        try:
            # 步骤 2: 创建新用户
            await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.admin.create_user({
                    "email": guest_email,
                    "password": guest_password,
                    "email_confirm": True,
                    "user_metadata": { "display_name": f"游客_{request.guest_id[-6:]}" }
                })
            )
            print(f"INFO: [Guest] 游客账户 '{guest_email}' 创建成功。")

            # 步骤 3: 再次尝试登录
            # 此时用户必定存在，登录应该会成功
            session_response_after_create = await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.sign_in_with_password({
                    "email": guest_email,
                    "password": guest_password
                })
            )
            print(f"INFO: [Guest] 新游客账户登录成功。")
            return session_response_after_create.model_dump()

        except Exception as e:
            # 如果在创建或再次登录时发生错误
            print(f"ERROR: [Guest] 游客登录/创建流程最终失败: {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"游客登录/创建失败: {str(e)}")

# --- 对话创建 (入口) API ---

@app.post("/api/chats/start-with-agent/{agent_id}", response_model=Dict[str, str])
async def start_chat_with_agent(agent_id: str, user_id: str = Query(..., description="发起聊天的用户ID")):
    """开始与指定Agent的聊天会话 - 重构版本，业务逻辑委托给chat_service"""
    try:
        chat_id = await chat_service.start_chat_with_agent(user_id, agent_id)
        return {"chat_id": chat_id}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@app.post("/api/chats/start-story/{story_id}", response_model=Dict[str, str])
async def start_story_chat(story_id: str, user_id: str = Query(..., description="发起故事的用户ID")):
    try:
        existing_chat = await supabase_service.get_chat_by_user_and_story(user_id, story_id)
        if existing_chat:
            print(f"INFO: Found existing chat {existing_chat['id']} for user {user_id} and story {story_id}.")
            return {"chat_id": existing_chat["id"]}

        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")
        
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters")
        first_chapter = chapters[0]

        opening_sequence = first_chapter.get("opening_sequence") or []
        
        agent_ids = list(set(
            element.get("agent_id") or element.get("character_id")
            for element in opening_sequence
            if element.get("agent_id") or element.get("character_id")
        ))

        print(f"--- AGENT_IDS DEBUG ---")
        print(f"Opening sequence length: {len(opening_sequence)}")
        print(f"Extracted agent_ids: {agent_ids}")
        for i, element in enumerate(opening_sequence[:3]):
            agent_id = element.get('agent_id') or element.get('character_id')
            print(f"  Element {i}: type={element.get('element_type')}, agent_id={agent_id}")
        print(f"----------------------")

        if not agent_ids:
            print(f"WARN: Story {story_id} chapter 1 has no characters in opening sequence.")
            agents = await supabase_service.get_agents(is_public=True, limit=2)
            agent_ids = [a['id'] for a in agents]

        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=agent_ids, story_id=story_id)
        if not chat_id:
            raise HTTPException(status_code=500, detail="Failed to create chat session for story")

        return {"chat_id": chat_id}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@app.post("/api/chats/{chat_id}/touch")
async def touch_chat_session(chat_id: str):
    updated = await supabase_service.touch_chat(chat_id)
    if updated:
        return {"status": "ok"}
    raise HTTPException(status_code=404, detail="Chat not found or failed to update")

# --- 数据获取 API ---

@app.get("/api/chats/{chat_id}/messages", response_model=List[Dict])
async def get_chat_messages(chat_id: str, limit: int = 50, offset: int = 0):
    messages = await supabase_service.get_messages_by_chat_id(chat_id, limit, offset)
    return messages

@app.get("/api/chats/{chat_id}/details", response_model=Dict)
async def get_chat_details(chat_id: str):
    chat_details = await supabase_service.get_chat_by_id(chat_id)
    if not chat_details:
        raise HTTPException(status_code=404, detail="Chat session not found")
    return chat_details

@app.get("/api/chats/{chat_id}/participants", response_model=List[Dict])
async def get_chat_participants(chat_id: str):
    participants = await supabase_service.get_chat_participants(chat_id)
    return participants

@app.get("/api/user-chats", response_model=List[Dict])
async def get_user_chat_list(user_id: str, limit: int = 20):
    chat_list = await supabase_service.get_user_chat_list(user_id, limit)
    return chat_list

# --- 新增: 排行榜 & 发现 API ---

@app.get("/api/rankings", response_model=List[Dict])
async def get_rankings(type: str = Query(..., pattern="^(story|agent)$"), period: str = Query("daily")):
    try:
        if type == "story":
            rankings = await supabase_service.get_story_rankings(period)
        else:
            rankings = await supabase_service.get_agent_rankings(period)
        return rankings
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch rankings: {e}")

@app.get("/api/agents/public-with-creator", response_model=List[Dict])
async def get_public_agents_with_creator(limit: int = Query(10, ge=1, le=50)):
    try:
        agents = await supabase_service.get_public_agents_with_creator(limit)
        return agents
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agents: {e}")

@app.get("/api/agents/{agent_id}", response_model=Dict)
async def get_agent_detail(agent_id: str):
    try:
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        try:
            from .pydantic_models import AgentDetailResponse
            validated_agent = AgentDetailResponse.model_validate(agent)
            return validated_agent.model_dump()
        except Exception as validation_error:
            print(f"WARN: Agent详情数据验证失败，返回原始数据: {validation_error}")
            return agent
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agent detail: {e}")

# --- 角色卡导入 API ---

@app.post("/api/agents/import", response_model=ImportedAgentResponse)
async def import_character_card(file: UploadFile = File(...), user_id: str = Form(...)):
    try:
        if file.content_type == 'image/png':
            file_content = await file.read()
            image = Image.open(io.BytesIO(file_content))
            chara_data_b64 = image.info.get('chara')
            if not chara_data_b64:
                raise HTTPException(status_code=400, detail="PNG文件中未找到角色卡数据")
            try:
                json_data = json.loads(base64.b64decode(chara_data_b64))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"角色卡数据解析失败: {str(e)}")
        elif file.content_type == 'application/json':
            file_content = await file.read()
            try:
                json_data = json.loads(file_content.decode('utf-8'))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"JSON文件解析失败: {str(e)}")
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请上传PNG或JSON文件")

        if 'spec' in json_data and 'data' in json_data:
            spec = json_data.get('spec', 'chara_card_v2')
            spec_version = json_data.get('spec_version', '2.0')
            character_data = json_data['data']
        else:
            spec = 'chara_card_v1'
            spec_version = '1.0'
            character_data = json_data

        if not character_data.get('name'):
            raise HTTPException(status_code=400, detail="角色卡缺少必要的name字段")

        new_agent = await supabase_service.create_agent_from_character_card(
            user_id=user_id,
            character_data=character_data,
            spec=spec,
            spec_version=spec_version
        )

        if not new_agent:
            raise HTTPException(status_code=500, detail="角色卡导入失败")

        return ImportedAgentResponse(
            id=str(new_agent['id']),
            name=new_agent['name'],
            message=f"角色 '{new_agent['name']}' 导入成功！"
        )
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"导入角色卡时发生错误: {str(e)}")

@app.get("/api/stories", response_model=List[Dict])
async def get_stories(is_public: bool = Query(True), limit: int = Query(10, ge=1, le=50), user_id: Optional[str] = None):
    try:
        stories = await supabase_service.get_stories(user_id=user_id, is_public=is_public, limit=limit)
        return stories
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch stories: {e}")

@app.get("/api/stories/{story_id}", response_model=Dict)
async def get_story_detail(story_id: str):
    try:
        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")

        chapters = await supabase_service.get_story_chapters(story_id)

        try:
            agents_response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("story_agents")
                .select("agents(*)")
                .eq("story_id", story_id)
                .execute()
            )
            agents = [item['agents'] for item in agents_response.data] if agents_response.data else []
            for agent in agents:
                if 'first_mes' in agent and 'opening_line' not in agent:
                    agent['opening_line'] = agent['first_mes']
            print(f"DEBUG: Found {len(agents)} agents for story {story_id}")
        except Exception as agents_e:
            print(f"WARN: Failed to fetch story agents, falling back to public agents: {agents_e}")
            agents = await supabase_service.get_agents(is_public=True, limit=20)

        return {
            **story,
            "chapters": chapters,
            "agents": agents,
        }
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch story detail: {e}")

# --- 公开配置 API ---

class PublicConfig(BaseModel):
    supabase_url: str
    supabase_anon_key: str

@app.get("/api/config/public", response_model=PublicConfig)
async def get_public_config():
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")

    if not supabase_url or not supabase_anon_key:
        raise HTTPException(status_code=500, detail="Server configuration is incomplete.")

    return {
        "supabase_url": supabase_url,
        "supabase_anon_key": supabase_anon_key,
    }

# --- 独立评分API (PRD核心要求) ---

class EvaluateRequest(BaseModel):
    user_message: str

@app.post("/api/chats/{chat_id}/evaluate", response_model=Dict[str, Any])
async def evaluate_user_message_for_story(chat_id: str, request: EvaluateRequest):
    try:
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat or not chat.get('story_id'):
            raise HTTPException(status_code=404, detail="Story chat session not found.")

        story_id = chat['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters.")
        
        task_progress = chat.get('task_progress', {})

        current_chapter_id = task_progress.get('current_chapter_id')
        if not current_chapter_id and chapters:
            current_chapter_id = chapters[0]['id']

        current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

        if current_progress >= 100:
            return {
                "progress_increment": 0,
                "reasoning": "本章任务已完成。",
                "current_progress": 100,
                "chapter_complete": True
            }

        current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), chapters[0] if chapters else None)
        if not current_chapter:
            raise HTTPException(status_code=404, detail="Current chapter not found.")

        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=10)

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=request.user_message
        )

        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {
            'progress': new_progress,
            'status': new_status
        }

        if new_progress >= 100:
            current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
            if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                next_chapter = chapters[current_chapter_index + 1]
                if next_chapter['id'] not in updated_task_progress['chapters']:
                    updated_task_progress['chapters'][next_chapter['id']] = {
                        'progress': 0,
                        'status': 'unlocked'
                    }

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        return {
            "progress_increment": score_response.progress_increment,
            "current_progress": new_progress,
            "chapter_complete": new_progress >= 100
        }

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to evaluate message: {e}")

# --- 章节推进 API ---

@app.post("/api/chats/{chat_id}/next-chapter", response_model=Dict[str, str])
async def advance_to_next_chapter(chat_id: str, user_id: str = Query(...)):
    try:
        response = await asyncio.to_thread(
            lambda: supabase_service.supabase.rpc('advance_to_next_chapter', {
                'p_current_chat_id': chat_id,
                'p_user_id': user_id
            }).execute()
        )

        if response.error:
            raise Exception(response.error.message)

        new_chat_id = response.data

        if not new_chat_id:
            raise HTTPException(status_code=404, detail="已经是最后一章或找不到下一章。")

        return {"new_chat_id": new_chat_id}

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"进入下一章失败: {str(e)}")

# --- 羁绊系统 API ---

@app.get("/api/bonds/{agent_id}", response_model=Dict)
async def get_bond_details(agent_id: str, user_id: str = Query(...)):
    """获取用户与指定角色的羁绊详情"""
    try:
        bond_details = await supabase_service.get_user_agent_bond(user_id, agent_id)
        if not bond_details:
            # 如果还没有记录，返回一个初始状态
            return {"bond_value": 0, "bond_level": 1}
        return bond_details
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get bond details: {e}")

@app.get("/api/user/{user_id}/bonds", response_model=List[Dict])
async def get_user_all_bonds(user_id: str):
    """获取用户的所有羁绊关系"""
    try:
        bonds = await supabase_service.get_user_all_bonds(user_id)
        return bonds
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get user bonds: {e}")

# --- 摘要与记忆管理 API ---

@app.post("/api/chats/{chat_id}/summary/update")
async def update_chat_summary(chat_id: str):
    try:
        success = await summarization_service.update_chat_summary(chat_id)
        if success:
            return {"status": "success", "message": "摘要已更新"}
        else:
            raise HTTPException(status_code=500, detail="摘要更新失败")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update summary: {e}")

@app.get("/api/chats/{chat_id}/summary")
async def get_chat_summary(chat_id: str):
    try:
        summary = await summarization_service.get_chat_summary(chat_id)
        return {
            "chat_id": chat_id,
            "summary": summary,
            "has_summary": summary is not None
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {e}")

@app.get("/api/chats/{chat_id}/memory-status")
async def get_memory_status(chat_id: str):
    try:
        total_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .execute()
        )
        vectorized_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .not_.is_("embedding", "null")
            .execute()
        )
        summary = await summarization_service.get_chat_summary(chat_id)

        return {
            "chat_id": chat_id,
            "total_messages": total_messages.count or 0,
            "vectorized_messages": vectorized_messages.count or 0,
            "has_summary": summary is not None,
            "memory_coverage": (vectorized_messages.count or 0) / max(total_messages.count or 1, 1) * 100
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get memory status: {e}")

# ===================================================================
# WebSocket (V5 重构)
# ===================================================================

# ConnectionManager 和 manager 实例已移动到 chat_service.py

# ===================================================================
# 织梦者引擎 - 选择效果处理
# ===================================================================

async def process_choice_effect(chat_id: str, choice_effect: dict):
    try:
        print(f"INFO: 处理选择效果 for chat {chat_id}: {choice_effect}")

        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat:
            print(f"ERROR: Chat {chat_id} not found")
            return

        current_game_state = chat.get('game_state', {})
        
        if current_game_state is None:
            current_game_state = {}

        updated_game_state = dict(current_game_state)
        state_changes = []

        for variable_name, effect_value in choice_effect.items():
            if isinstance(effect_value, str):
                delta = int(effect_value)
            else:
                delta = int(effect_value)

            current_value = updated_game_state.get(variable_name, 50)
            new_value = max(0, min(100, current_value + delta))
            updated_game_state[variable_name] = new_value

            state_changes.append({
                "variable": variable_name,
                "old_value": current_value,
                "new_value": new_value,
                "delta": delta
            })
            print(f"  - {variable_name}: {current_value} -> {new_value} ({delta:+d})")

        await supabase_service.update_chat_game_state(chat_id, updated_game_state)

        change_descriptions = []
        for change in state_changes:
            variable_parts = change["variable"].split(".")
            if len(variable_parts) >= 2:
                character_name = variable_parts[0]
                variable_type = variable_parts[1]
                if change["delta"] > 0:
                    change_descriptions.append(f"{character_name}的{variable_type}提升了！")
                elif change["delta"] < 0:
                    change_descriptions.append(f"{character_name}的{variable_type}下降了...")

        if change_descriptions:
            await chat_service.manager.broadcast(chat_id, json.dumps({
                "type": "game_state_update",
                "changes": state_changes,
                "descriptions": change_descriptions,
                "new_game_state": updated_game_state
            }))
            print(f"SUCCESS: 游戏状态更新完成 for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: 处理选择效果失败 for chat {chat_id}: {e}")
        traceback.print_exc()

@app.websocket("/ws/chat/{chat_id}")
async def websocket_chat_endpoint(websocket: WebSocket, chat_id: str, user_id: str = Query(...)):
    # chat_service 现在拥有 manager，通过它来连接
    await chat_service.manager.connect(websocket, chat_id)
    try:
        # 1. 发送初始状态包 (此逻辑可封装进 chat_service)
        await chat_service.send_initial_state(websocket, chat_id)

        # 2. 循环接收消息
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # 3. 将消息处理完全委托给 chat_service
            await chat_service.handle_websocket_message(chat_id, user_id, message_data)

    except WebSocketDisconnect:
        chat_service.manager.disconnect(websocket, chat_id)
    except Exception as e:
        print(f"ERROR: WebSocket for chat {chat_id} error: {e}")
        traceback.print_exc()
        chat_service.manager.disconnect(websocket, chat_id)


# process_ai_turn 函数已迁移到 chat_service.py

# process_regular_chat_turn 和 process_rag_enhancement_async 函数已迁移到 chat_service.py

# generate_and_broadcast_choices 函数已迁移到 chat_service.py

# stream_and_save_response 函数已迁移到 chat_service.py

# generate_and_save_embedding 和 check_and_update_summary 函数已迁移到 chat_service.py

async def process_choice_scoring_only(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None):
    try:
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session or not chat_session.get('story_id'):
            print(f"ERROR: Chat {chat_id} is not a story chat")
            return

        story_id = chat_session['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            print(f"ERROR: No chapters found for story {story_id}")
            return

        current_chapter = chapters[0]
        task_progress = chat_session.get('task_progress', {})
        current_progress = task_progress.get('current_progress', 0)

        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=20)

        await asyncio.sleep(1.0)
        print(f"INFO: Starting choice scoring for chat {chat_id}")

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        if score_response:
            increment = score_response.progress_increment
            new_total = min(current_progress + increment, 100)
            is_chapter_complete = new_total >= 100

            updated_task_progress = {**task_progress, 'current_progress': new_total}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            score_message = {
                "type": "score_update",
                "progress_increment": increment,
                "current_progress": new_total,
                "chapter_complete": is_chapter_complete
            }
            await chat_service.manager.broadcast(chat_id, json.dumps(score_message))
            print(f"INFO: Choice scoring completed for chat {chat_id}: +{increment} -> {new_total}")

    except Exception as e:
        print(f"ERROR: Choice scoring failed for chat {chat_id}: {e}")
        traceback.print_exc()

async def process_story_turn_with_scoring(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None, target_agent_index: Optional[int] = None):
    lock = chat_service.get_lock(chat_id)
    async with lock:
        try:
            chat_task = supabase_service.get_chat_by_id(chat_id)
            history_task = supabase_service.get_messages_by_chat_id(chat_id, limit=15)
            participants_task = supabase_service.get_chat_participants(chat_id)
            chat, history, participants = await asyncio.gather(chat_task, history_task, participants_task)

            if not chat or not chat.get('story_id'):
                raise ValueError("Not a valid story session.")

            # 选择回复角色的逻辑保持不变
            if target_agent_index is not None and target_agent_index < len(participants):
                replying_agent = participants[target_agent_index]
            elif target_agent_id:
                replying_agent = next((p for p in participants if p['id'] == target_agent_id), participants[0])
            else:
                replying_agent = participants[0] if participants else None

            if not replying_agent:
                raise ValueError("No agent available for reply")

            # --- ▼▼▼ 核心修改区域开始 ▼▼▼ ---
            # 1. 不再手动构建简单的prompt
            #    移除所有 STREAMING_STORY_CHAT_PROMPT(...) 的相关代码

            # 2. 直接调用 prompt_assembler 来构建完整的、带有故事上下文的 prompt
            print(f"INFO: [Story Mode] Building advanced prompt for agent {replying_agent.get('name')}...")
            streaming_prompt = await prompt_assembler.build_prompt(
                chat_id=chat_id,
                user_message=user_message,
                agent_id=replying_agent['id'],
                mode='story' # <--- 关键：指明这是故事模式
            )

            # 3. 将 AI 回复和评分任务并行执行
            ai_reply_task = asyncio.create_task(stream_and_save_response(chat_id, replying_agent, streaming_prompt, is_story_mode=True))

            # (评分任务的逻辑保持不变，但需要确保它能正确获取章节信息)
            story_id = chat['story_id']
            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                raise ValueError("Story has no chapters.")

            task_progress = chat.get('task_progress', {})
            # 修正：从 task_progress 中获取当前章节和进度
            current_chapter_id = task_progress.get('current_chapter_id')
            current_chapter = None
            if current_chapter_id:
                current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), None)

            # 如果没有当前章节ID或找不到，则默认为第一个
            if current_chapter is None:
                current_chapter = chapters[0]
                current_chapter_id = current_chapter['id']

            current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

            scoring_task = asyncio.create_task(_delayed_scoring_task(
                chat_id=chat_id, user_message=user_message, current_chapter=current_chapter,
                current_progress=current_progress, task_progress=task_progress, history=history
            ))

            await ai_reply_task
            print(f"INFO: AI reply completed for chat {chat_id}, scoring task running in background")
            # --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---

        except Exception as e:
            print(f"ERROR: process_story_turn_with_scoring failed for chat {chat_id}: {e}")
            traceback.print_exc()

async def _delayed_scoring_task(chat_id: str, user_message: str, current_chapter: dict, current_progress: int, task_progress: dict, history: list):
    try:
        await asyncio.sleep(1.0)
        print(f"INFO: Starting delayed scoring task for chat {chat_id}")

        if current_progress >= 100:
            print(f"INFO: Chapter already completed for chat {chat_id}, skipping scoring")
            return

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {'progress': new_progress, 'status': new_status}

        if new_progress >= 100:
            chapters = await supabase_service.get_story_chapters(current_chapter.get('story_id'))
            if chapters:
                current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
                if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                    next_chapter = chapters[current_chapter_index + 1]
                    if next_chapter['id'] not in updated_task_progress['chapters']:
                        updated_task_progress['chapters'][next_chapter['id']] = {'progress': 0, 'status': 'unlocked'}

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        await chat_service.manager.broadcast(chat_id, json.dumps({
            "type": "score_update", "progress_increment": score_response.progress_increment,
            "current_progress": new_progress, "chapter_complete": new_progress >= 100
        }))
        print(f"INFO: Scoring completed for chat {chat_id}: +{score_response.progress_increment} -> {new_progress}")

    except Exception as e:
        print(f"ERROR: Delayed scoring task failed for chat {chat_id}: {e}")
        traceback.print_exc()

async def process_story_turn(chat: dict, history: list, user_message: str, agent: dict, participants: list):
    chat_id = chat['id']

    # 使用 prompt_assembler 来构建完整的、带有故事上下文的 prompt
    print(f"INFO: [Story Mode] Building advanced prompt for agent {agent.get('name')}...")
    streaming_prompt = await prompt_assembler.build_prompt(
        chat_id=chat_id,
        user_message=user_message,
        agent_id=agent['id'],
        mode='story' # 指明这是故事模式
    )

    await stream_and_save_response(chat_id, agent, streaming_prompt, is_story_mode=True)

async def process_opening_sequence(chat_id: str):
    lock = chat_service.get_lock(chat_id)
    async with lock:
        try:
            chat = await supabase_service.get_chat_by_id(chat_id)
            if not chat or not chat.get('story_id'):
                print(f"ERROR: Chat {chat_id} is not a story chat or not found")
                return

            story_id = chat['story_id']
            story_detail = await supabase_service.get_story_by_id(story_id)
            protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                print(f"ERROR: No chapters found for story {story_id}")
                return

            first_chapter = chapters[0]
            opening_sequence = first_chapter.get("opening_sequence") or []

            if not opening_sequence:
                print(f"INFO: No opening sequence found for story {story_id}")
                return

            task_progress = chat.get('task_progress', {})
            opening_progress = task_progress.get('opening_sequence_index', 0)

            if opening_progress >= len(opening_sequence):
                print(f"INFO: Opening sequence already completed for chat {chat_id}")
                return

            current_element = opening_sequence[opening_progress]
            element_type = current_element.get("element_type", "text")
            content = current_element.get("content_or_prompt", "")
            character_id = current_element.get("agent_id") or current_element.get("character_id")

            if content:
                role = 'narration'
                if character_id:
                    if character_id == protagonist_agent_id:
                        role = 'user'
                    else:
                        role = 'assistant'
                await supabase_service.add_message_to_chat(
                    chat_id=chat_id, role=role, content=content, agent_id=character_id,
                    metadata={"is_opening_sequence": True}
                )
                print(f"INFO: Saved opening sequence element to chat {chat_id} as '{role}' message (character_id: {character_id}, protagonist: {protagonist_agent_id}).")

            message_data = {
                "type": "opening_sequence_element",
                "data": {
                    "element_type": element_type, "content": content, "character_id": character_id,
                    "sequence_index": opening_progress, "total_elements": len(opening_sequence),
                    "is_last": opening_progress == len(opening_sequence) - 1
                }
            }
            if element_type == "choice" and "choices" in current_element:
                message_data["data"]["choices"] = current_element["choices"]

            await chat_service.manager.broadcast(chat_id, json.dumps(message_data))

            new_progress = opening_progress + 1
            updated_task_progress = {**task_progress, 'opening_sequence_index': new_progress}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            print(f"INFO: Sent opening sequence element {opening_progress + 1}/{len(opening_sequence)} for chat {chat_id}")

            if new_progress >= len(opening_sequence):
                completion_message = {"type": "opening_sequence_complete", "data": {"chat_id": chat_id}}
                await chat_service.manager.broadcast(chat_id, json.dumps(completion_message))
                print(f"INFO: Opening sequence completed for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: Failed to process opening sequence for chat {chat_id}: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    import uvicorn
    print("INFO: 启动星恋 AI 后端服务 - V5.0 统一消息流")
    print("INFO: 访问 http://127.0.0.1:8000/docs 查看API文档")
    uvicorn.run("supabase_main:app", host="0.0.0.0", port=8000, reload=True)
</file>

<file path="backend/src/utils/__init__.py">
# Utils package
</file>

<file path="backend/src/utils/user_management.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理工具模块
提供可复用的用户创建和管理功能
"""

import asyncio
from typing import Optional
from supabase import Client


async def get_or_create_user(supabase_admin: Client, email: str, password: str, **metadata) -> Optional[str]:
    """
    一个可复用的函数，用于幂等地创建用户并更新其档案。
    
    Args:
        supabase_admin: Supabase管理员客户端
        email: 用户邮箱
        password: 用户密码
        **metadata: 额外的用户元数据
        
    Returns:
        用户ID，如果失败返回None
    """
    try:
        # 检查用户是否存在
        response = await asyncio.to_thread(lambda: supabase_admin.auth.admin.list_users())
        users_list = getattr(response, 'users', response)
        existing_user = next((u for u in users_list if u.email == email), None)

        if existing_user:
            print(f"[USER_MGMT] ✓ 用户 '{email}' 已存在。")
            return str(existing_user.id)
            
    except Exception as e:
        print(f"[USER_MGMT] WARN: 检查用户存在性时发生错误: {e}")

    # 创建新用户
    try:
        print(f"[USER_MGMT] INFO: 正在创建新用户 '{email}'...")
        
        # 创建用户账户
        create_response = await asyncio.to_thread(
            lambda: supabase_admin.auth.admin.create_user({
                "email": email,
                "password": password,
                "email_confirm": True,
                "user_metadata": metadata
            })
        )
        
        if hasattr(create_response, 'user') and create_response.user:
            user_id = str(create_response.user.id)
            print(f"[USER_MGMT] ✓ 用户 '{email}' 创建成功，ID: {user_id}")
            return user_id
        else:
            print(f"[USER_MGMT] ERROR: 用户创建失败，响应格式异常")
            return None
            
    except Exception as e:
        print(f"[USER_MGMT] ERROR: 创建用户时发生错误: {e}")
        return None


async def create_user_if_not_exists(supabase_admin: Client, email: str, password: str, **metadata) -> Optional[str]:
    """
    向后兼容的函数名，调用get_or_create_user
    """
    return await get_or_create_user(supabase_admin, email, password, **metadata)
</file>

<file path="devtools_options.yaml">
description: This file stores settings for Dart & Flutter DevTools.
documentation: https://docs.flutter.dev/tools/devtools/extensions#configure-extension-enablement-states
extensions:
</file>

<file path="novel_compiler.bat">
@echo off
chcp 65001 >nul 2>&1
cls
echo.
echo ========================================
echo DreamWeaver Engine - Novel Game Compiler
echo ========================================
echo.
echo Starting novel compiler...
echo Processing all .txt files in /novel/ directory
echo.

cd /d "%~dp0"

REM Check if .env file exists, create if not
if not exist "backend\.env" (
    echo INFO: .env file not found, creating...
    cd backend
    python create_env.py
    cd ..
)

REM Run novel compiler (no parameters needed, will auto-find files and create user)
python "backend\scripts\novel_compiler.py"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ========================================
    echo ERROR: Compilation failed!
    echo Please check the error messages above.
    echo ========================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ========================================
echo Compilation completed successfully!
echo Press any key to exit...
echo ========================================
pause >nul
</file>

<file path="novel/test.txt">
再次见到他，是在七年之后，一家拥挤的超市，到处挤满了周末采购的人潮。

赵默笙独自推着购物车，艰难地在人群中走走停停。刚刚从国外回来的她，还不太适应这样的拥挤，然而这样热闹而亲切的场面，却使她不自觉地带着微笑，几乎是用感激的心情聆听这嘈杂的乡音。她不知道别人刚刚回国是不是也和她一样，心里的激动和喜悦几乎无法抑制。

七年！久违了啊！

但是，怎么刚回国就遇见了他呢？不，确切地说，应该是他们。

默笙默默地看着站在蔬菜架前的那一双俪影，再一次领略了命运的奇妙。七年之前，也正是他们，使她最终做出了出国的决定。

现在他们一起来买东西呢，那么最终还是在一起了吧！还好她走得快啊，不然恐怕只会伤得更深。

何以琛，何以玫，她真傻，怎么会以为有相似的名字就是兄妹呢？

“我们根本不是兄妹，以前我们两家是很要好的邻居，大家都姓何，所以大人就取了相似的名字。后来以琛的爸爸妈妈出了意外，我们家就收养了以琛。”

“你觉得你比得过我和以琛二十年青梅竹马的感情吗？”

“我今天是想告诉你，我爱以琛，我不想偷偷摸摸地爱他，我要和你光明正大地竞争。”

十九岁的那年，默笙生日的前一天，她一向文静内向的好朋友何以玫，突然勇气十足地对她这样宣言。一向温柔不与人争的以玫会这样说，一定是爱到了极点。

可是她拿什么跟她竞争呢？就在以玫宣战的当天，她就败了，然后逃去了美国七年。

何以琛——突然想到那日他冰冷的眉眼，绝情的言语，默笙的心有一丝抽痛，浅浅的，几乎难以察觉，却是存在的。

他们向她的方向走来，默笙抓住推车的手指关节开始泛白，几乎立刻想要掉头。但超市实在是太挤了，推着购物车的她根本无法转身。而在下一刻她也想开了，为什么要逃避？她应该平静地对他们说：“嗨，好久不见。”然后潇洒地走开，留给他们一个美丽的背影。

更何况，他们也许根本认不出她来了。她变了好多，以前那头飘逸的长发已经变成了齐耳利落的短发，以前白皙的皮肤已经让加州的阳光晒黑。穿着宽大的t-shirt，牛仔，球鞋的她，和以前的差距太大。

他们慢慢地，一步一步地走近，然后……擦肩而过。

不是不心痛的。

若有似无的语声传来。

“要不要买点牛奶？”以玫轻柔的声音。

“……”

回答却听不真切了。好怀念，以琛低沉如大提琴的声音，这些年在异国他乡，仍然时时处处在她耳边吟诵。

失落，但也松了一口气，默笙抬起一直低垂的头，迈开步子。

“砰”的一声，购物车撞上了地上堆成一坐小山似的减价肥皂。罪魁祸首赵默笙傻傻地看着几百块肥皂坍塌下来，场面颇为壮观。

呃，她可不可以当作不是她干的。

“天那！这已经是今天第三次了。”不知道哪里冒出来的超市理货员发出痛苦的**。

所以，这也不应该怪她吧，哪有人把货物堆在路中间的。默笙悄悄地吐吐舌头，努力地摆出一副愧疚的表情。

这里的动静引起了周围人的注意，包括何以玫。她只是不经意地看向那个特别嘈杂的地方，然后呆住——是她，居然是她，以玫几乎不敢相信自己的眼睛。她，回来了？

“以玫？”何以琛不解她的反应，出声询问，眼光顺着她看去。

高大挺拔的身躯瞬间僵硬。

赵默笙！

那一脸无辜垂着头的小女子可不正是赵默笙！脸上是百分百的歉然，眼睛里却闪着无庸置疑的顽皮笑意。远远的，其实看不大真切她的表情，但以琛就是知道。他一直知道的，她是这样，习惯搅乱一池春水后不负责任地离开，任性自私又可恶。

整整七年……她还晓得回来吗？

何以琛垂眸。“以玫，我们走吧！”

何以玫惊讶地看着一脸平静的以琛。“你不想去打个招呼吗？也许……”

“她早已不是我生活中的人了。”波澜不兴的语调，仿佛真的没有什么。

以玫细细地打量他的神情，却找不出蛛丝马迹，最后只得低叹一声。“走吧！”

最后一眼看向赵默笙，却发现她也正好偏过头来看到她，视线在空中相撞，默笙好像愣了一下，然后脸上浮现了浅浅的笑容，朝她点头致意。

以玫慌忙回头叫：“以琛……”

“嗯？”

“她……”以玫愕然打住，再回首川流的人群中已经没有了她的的身影。

“怎么了？”

“没，没什么。”以玫低头。只是，她明明就看见他们了，为什么这么轻易地就走了？而以琛，也明明看见了她……

没想到有朝一日回到这里。

主编面试的时候问她：“赵小姐，你为什么选择在a城工作？”

默笙突然不知道怎么回答。为什么呢？因为曾在这里念过一年多的大学？因为曾在这里认识他？因为曾在这里经受过很多很多？

她开始也不知道，回国前第一个想到的就是这里，直到那天见到他才明白，她是想见他，虽然他已经不属于她，但是，她就是想看看他。

只看看而已。

“可能是因为不能回家吧。”默笙说，主编奇怪地打量了她良久，留下了她，成了某女性杂志的摄影记者。

然而主编过分地看重她在国外杂志工作的经历使她不安。

“那只是一个小杂志社。”默笙这样对主编说。

“哎！阿笙。”四十多岁的女主编亲热地叫着她的名字。“你是在夸奖我的博识吗？我居然连美国一个不起眼的小杂志社都一清二楚。”

默笙笑了起来，不安也一扫而空。

主编正色地说：“阿笙，我知道一个中国人在美国当一个摄影师多么的难，你必须比大多数白人优秀。他们总以为我们中国人是没有艺术细胞的。”

就这样安定下来，她仍然去那家超市购物，却再也没有遇见过他们。直到有一次，超市的保安叫住了她。

“小姐，请你到保安室来一趟。”

默笙一愣，直觉没有好事，报纸上有太多的关于超市保安强行搜身甚至打人的报道。

默笙谨慎地盯着他，保安无奈地说：“小姐，我对你没有恶意，只是想问你一个月前有没有丢了东西。”

一个月前她刚回国，难道她丢了什么自己也不知道？好奇的随他走进保安室，保安递给她一个黑色的皮夹。

默笙不用看里面就知道不是自己的，笑着摇摇头说：“你弄错了，这不是我的。”

保安出乎意料地固执。“你打开来看看。”

她接过打开，然后看到了自己的照片。

保安得意地说：“小姐，这是你的照片吧，虽然和现在差别很大，可我还是一眼就认出来了。”

差别是很大的，因为那是刚上大学时拍的入学照。她还是长长的头发扎成马尾，傻乎乎地笑着。

怎么会出现在一个陌生的皮夹里？

默笙把皮夹还给保安。“这的确不是我的。”

保安傻傻的。“照片上的人不是你吗？”

“是我，可是皮夹不是我的。”

“可一定是认识你的人的，小姐，说不定这个皮夹的主人暗恋你……”

哎，谁说中国人没有联想力的？

“可是……”

“你拿去吧拿去吧，一直没人来认领，放在这里我们也很难处理，交上去也是充公，还不如给你，你和皮夹的主人肯定有点关联。啊！说不定我还促成了一段美好的姻缘呢……”保安沉浸在电视连续剧似的想像里。

一个月前，大约也是她碰到何以琛何以玫的时候，会是他掉的吗？怀着这样可笑的猜测，默笙把皮夹拿回了家。

晚上洗完澡在床上仔细地研究它，简单的式样，名贵的牌子，现金不多，完全不能确定失主的身份。

而那张照片，默笙小心地取出来，上面还有钢印的痕迹，应该是从什么证件上撕下来的。无意地翻过来，她突然怔住，背后有字！那潇洒凌厉得仿佛要破纸而出的字迹她一辈子都不会忘记。

那是以琛的笔迹，用黑色钢笔写着——

mysunshine！

复杂城市里的生活一样可以过得很单纯，工作，吃和睡，如此而已。一段忙乱的适应期后，接下来就是麻木的重复。

“阿笙啊，我到处找你。”

默笙刚踏入杂志社，就听到老远的有人在喊。

“老白，有什么事情？”

老白其实很年轻，是杂志社的另一个摄影师，姓李，因为老说白字所以大家戏称他老白。他哄明星很有一套，所以杂志封面人物的拍摄都由他负责。

“我老婆要生了，明天帮萧大模特拍照的事能不能麻烦你？”

萧筱？默笙有点为难。“我是没什么问题，但听说萧筱的脾气很怪，不是熟人根本不配合。”

老白也想到了这一点，想了想说：“这样吧，你先去试试，如果实在不行再叫我。”

第二天，当默笙见到冷艳动人的萧筱时，她完全呆住了。她对国内的明星不熟，以前从来没有见过萧筱的照片，不知道她竟然……竟然跟她大学时代的好友长得那么像。

可她的好友是那样一个纯朴而笨拙的农村姑娘，眼前的人却跷着修长的玉腿，抽烟的动作熟练而妩媚……

默笙不敢认，也许只是相像的人罢了。

可萧大模特眯着眼瞅了她一眼，踏着优雅的步伐走来，停在她面前。

“怎么，不认识我了？”

“……少梅？”

“呵！”她讽刺地轻笑一声。“可不就是我。”

“阿笙，你跟萧筱认识？真是太好了。”一起来的同事兴奋地说。

“大一的时候她是我的上铺。”

“大学里的上下铺可是最要好的。”萧筱的经纪人也凑上来说。

“不是要拍照吗？快拍吧！”萧筱不耐烦了。

她真的变了好多！默笙一边拍照一边想，镜头下的人不再是那个笨拙得可爱的少梅，那么她是谁呢？

也许谁都不是。一个好的摄影师能够摄取镜头下人的灵魂，而默笙捕捉不到萧筱的灵魂，也许是她功力不足，更也许是镜头下的人根本没有。

萧筱很空洞！一种让人无力绝望的空洞，也许正是这种空洞才使她红得发紫。

拍完一组，萧筱挥挥手。“今天就到这吧。”

“可是萧筱，下面还有……”她的经纪人急切地说。

“就到这儿。”萧筱毫无余地地说，转头对着默笙，“我们去喝杯咖啡。”

“久别重逢应该喝酒，可惜最近我的胃出了问题，只好喝咖啡了。”

“呃，喝咖啡很好，或者你应该喝点牛奶。”默笙不知道说什么话才好，有太多太多的事想问，却不知道从何问起。

“身体比较重要，节食也要有尺度。”默笙找些不着边际的话说。

“我从来不节食。”萧筱似笑非笑。“我酗酒。”

“少梅！”默笙惊愕于她一副自我厌恶的神色，激动地握住她的手，她怎么变成这样的呢？

萧筱反射地甩开她的手，默笙一愣，气氛尴尬而沉默。

“你变了很多。”半晌默笙涩涩地说。



“是的，还记得大一的时候我暗恋过一个人吗？”萧筱冷漠地叙述自己的故事。“有一天我告诉他我喜欢他，他接受了，但他不爱我，然后少梅死了，我现在是萧筱。”

三言两语，蚀骨穿心。默笙一阵心痛，什么都问不出口了。

过了一会，萧筱冷讽地说：“你倒没怎么变，还是一副虚情假意的样子。怎么舍得从金光闪闪的美国回来的？”

这话多少伤了默笙，但想一想毕竟是她理亏在先。当年一声不吭就走了，七年杳无音讯，是她对不起她们的友情。“那时候，我是走得太匆忙了……”

“你不用跟我说这些。”萧筱打断她。“这些话你应该向何以琛说。”

何以琛？怎么会扯到他？默笙想起那日他和以玫俪影双双，“我想他并不在意……”

“不在意？你以为每个人都和你一样无情无义没心没肺？”萧筱的声音激动起来，“你刚失踪的那几天，他找你找得快要发疯，后来干脆整天在宿舍楼下等，可是他等来了什么？”萧筱目光冷冷地指责她。“来了几个人把你的东西都拿走了，然后告诉他告诉我们，你已经去了美国，可能永远不会回来。”

“默笙，你真狠。”萧筱顿了顿说，“我永远忘不掉他当时的样子，仿佛一下子被掏空了，绝望到了极点，叫人都不忍心看，他是那样高傲的人，居然会露出那样的表情……”

默笙听得浑浑噩噩，这些事情真的发生过吗？

“也许他是内疚……”

“赵默笙，抛弃他去美国的是你，该内疚的也是你。”

“少梅，你不明白……”

“我有眼睛会看。”

默笙停住不说了，所有的人都以为是她抛弃了他吗？明明不是啊！

明明是他说那样的话……他说他不想再见到她，他说他宁愿从来都不认识她，他叫她滚得越远越好……

明明是他！

告别萧筱，默笙走在初夏的街道上，脑中仍回响着萧筱的话。

“他后来一直一个人……何以玫？她不是他妹妹吗？”

他们竟然没有在一起，那她当年离开又是为了什么？

他又是为什么要说那样的话？

摊开手掌，掌心里稳稳躺着的纸片上写着“袁向何律师事务所”的地址。

萧筱说：“也许你需要。”

她不是特意来的，她只是路过。可她毕竟已经站在“袁向何律师事务所”里了。

接待她的小姐抱歉地微笑：“何律师不在所里，请问你有预约吗？”

默笙说不清自己是失落多些还是轻松多些。“没有。”

“那你有什么事情吗？我可以帮你转告，或者……”小姐看向时钟，“你在这里等一下，何律师也快回来了。”

“哦，不用了，我下次再来。”默笙走出两步又回头。“这是何律师的钱包，请你帮我转交给他，谢谢。”

这就是结果吧。

向来缘浅，奈何情深。

“阿笙，你在国外工作和国内工作感觉有什么不同？”快下班了，杂志社的人也无心工作，闲聊时突然问起。

“呃。”默笙四处张望一下，见头头不在，“薪水高很多。”

希罕！吃不到葡萄的同事们立刻鼻孔出气表示不屑。

“你在那边有没有受到歧视？”

“多少吧。”

“其实这也没什么好在意的，香港人还不是看不起大陆人！”大宝从香港回来后感受颇深。

“当自己真的遇到就不会这么想得开了。有次我老板就当着所有同事的面说中国没有真正的艺术家。我一听气极了，从来没有那么真切地感觉到自己是中国人，当场就指着洋老头的鼻子说，你懂什么中国艺术，我们中国人玩艺术的时候你们美国人还不知道在哪里混呢。”

“真猛！有气节！”同事们纷纷拍手，赞口不绝，然后一齐问她：“后来你是被什么借口炒掉的？”

“……”默笙哭笑不得，“老美虽然自大，度量倒还是和身材成正比的。后来有一天老板居然拿着不知道哪弄来的文房四宝来找我要我写几个中国字，说他要挂在客厅。”

“哇，真的假的？”

“阿笙，你的字能看吗？”

“哈，我露了一手郑板桥的绝活，先把墨汁统统倒在宣纸上，再装模作样勾勾弄弄了半天，把那些美国人唬得一楞楞的叹为观止。不过说实话那几个字要不是我自己写的我绝对看不出是什么。”

“你写了什么？”

“尔乃蛮夷！”

噗嗤！有个同事喷茶。

一片哈哈声中，远远的有人叫：“阿笙，有人找你。”

默笙转头，被誉为花仙子——花痴仙子的小红八婆兮兮地跑来。“在会客室里，好英俊好冷漠好有味道的男人哦。而且一看就是那种事业有成的都市精英青年才俊哎，阿笙，你刚刚回国就泡上了这种好货色，真人不露相哦。”

花仙子的话能信猪都能在天上飞了，一般而言她的话要除以二，有时候还可以乘上负数。

不过默笙十分好奇，她才回国不认识什么人，谁会来找她？

绝没想到是他！

会客室里背对她立在落地窗前的英挺男子，竟然是何以琛。听到开门声，他回头，清冷的眸光射向她，淡淡的表情没有一点起伏。

花仙子总算没有夸张，他的确英俊不凡，气宇轩昂，剪裁合体的西装衬托出高大挺拔的身材，和以前一样的自信沉着，但又多了几分凌人的气势。

她完完全全地说不出话来。

而他神色镇定从容不迫地点头致意。“赵小姐。”

赵小姐？

默笙真的想笑，然而难度太高。“何……先生。”

远远地比了比椅子，默笙说：“请坐。”

她拿出茶叶，低头掩饰自己的神色，她无法像他那样无动于衷，只能藏起自己的激动。“你要喝点什么？”

“谢谢，不用。”他的目光冷峻。“我说几句话就走。”

“哦，你来找我……你怎么知道我在这？”

他停顿五秒才开口。“萧筱。我是她的律师。”

“有什么事吗？”

他口气透着寒意。“赵小姐三天前到鄙事务所时曾说会再度光临，却迟迟不见你来，我只好亲自过来拜访。”

默笙愕然，抬头迎上他灼灼的眸子。“你怎么知道……”她并没有留下名字，他怎么知道还皮夹的人是她？

“赵小姐，我恰好有正常人的推理能力。”他嘲讽地说。

也许当律师的都有这种“正常人的推理能力”，默笙盯着墙壁。“我是去还皮夹，你既然已经拿到就不用再跑一趟了。”

何以琛眸光一闪。“除了还皮夹，你没有别的事？”

她还可以有什么事吗？默笙怔怔。“没有了。”

“很好。”他眼中仿佛掠过一丝失望，移步到她面前。“可是我有事。”

他拿出那个黑色的皮夹放在她眼前。“这里面原来有一张照片，赵小姐知道下落吗？”

当然知道，默笙低头。“有吗？我没有注意。”

“哦？皮夹里除了钱什么都没有，赵小姐如何知道皮夹是我的？”

默笙哑口无言。差点忘了他是律师，善于找出对方言辞上的一切漏洞，想骗他先得掂掂自己的斤两。

他欠身。“赵小姐可否把照片还给我？”

默笙突然觉得莫名其妙。他是什么意思？一边摆出一副“你是陌生人”的模样，一边却又讨要她的照片。

“照片上的人是我，为什么要给你？”

“赵小姐，我劝你不要和一个律师讨论物品的所有权问题。”以琛冷冷地说。

默笙气馁，这样的以琛她不熟悉而且无法应付。“照片不在这里。”

“明天给我。”

“明天我有……”

“赵小姐！”何以琛打断她，“我想我们都不想和对方有太多的纠缠，何不早死早超生。”

早死早超生?默笙默然半晌，“你要那张照片干什么？”

“谁知道呢。”以琛目光沉沉，“也许我想把它放在我身边，时时提醒我那段愚蠢的过去。”

愚蠢……是啊，多愚蠢！她居然会有所期待。

何以琛径自做出决定。“我明天会来取，你若没空，可以请别人转交。再见，赵小姐。”

他举步离开，手刚刚握上门把，听到身后的默笙低声说：“等等……明天，我会送过去。”

“好。”以琛面无表情地回头。“谢谢你的合作，明天见。”

默笙怔怔的目送他高挺的背影离去。不是没想过有朝一日他们重逢会是什么样子，但怎么也没想到，他们居然连说一句“好久不见”的情分都没有了。

愚蠢的过去吗？

默笙站在卧室里的镜子前，审视镜子里面与她对视的女人。

如果一头短发变长扎成马尾，如果晒黑的皮肤变白皙一些，如果还能毫无顾忌地笑得灿灿烂烂……最重要的是，如果眼睛里减掉这七年多出来的沉郁，添满张扬的天真——那么，她就变成了初上大学刚认识何以琛的赵默笙。

“何以琛何以琛……”

“何以琛何以琛……”

以琛是怎么被她缠上的她也不太清楚，以琛更是莫名其妙，反正那时候她就追着他跑。直到有一次他受不了了，板着脸问：“赵默笙，你为什么老是跟着我？”

换成现在的她大概会羞愧得无地自容吧！然而那时侯的她是那么的不知羞，睁大眼睛问：“以琛，是你笨还是我笨，哎，你那么聪明，一定是我笨了，我怎么这么失败，追了半天人家都不知道我在干什么！”

犹记得以琛目瞪口呆，半天说不出话来。后来他提到这件事，曾好笑又好气地说，他本来是想用质问的口气让她感到羞愧的，谁料到这世上居然有脸皮这么厚的小女子，反将了他一军。

所以当时法律系的高才生迟迟反应过来后，居然只能结结巴巴地说：“我不准备在大学里找女朋友。”

她那时侯单纯的连借口都听不出，一鼓作气地问：“那我现在先排队，等你大学毕业了，可不可以有优先录取权？”

面对毫不讲章法的对手，口若悬河的最佳辩手顿失滔滔，抛下一句有课就落荒而逃。

她当然没有就此气馁，可在她想到更好的办法之前，居然听到学校有人在传：法律系的那个何以琛听说有女朋友了，叫什么赵默笙，名字挺拗口的。

她一听几乎是飞快地跑到自习教室找到以琛，急忙澄清：“谣言不是我传出去的，你要相信我。”

以琛从书中抬头，目光清明地说：“我知道。”

她傻傻地问：“你怎么知道？”

以琛神色自若地回答：“因为那是我传的。”

这回终于换她瞠目结舌，耳边是他在冷静地分析。“我考虑过了，如果三年后你注定是我女朋友，我何不提早行使我的权利。”

呵！那时候啊！

镜子里的人嘴角微微弯起，然而笑意还没到达眼底，已经收敛。

茫茫然走到阳台上，看那月朗星稀，明天应该是个好天。
</file>

<file path="pubspec.lock">
# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  animated_text_kit:
    dependency: "direct main"
    description:
      name: animated_text_kit
      sha256: adba517adb7e6adeb1eb5e1c8a147dd7bc664dfdf2f5e92226b572a91393a93d
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  app_links:
    dependency: transitive
    description:
      name: app_links
      sha256: "85ed8fc1d25a76475914fff28cc994653bd900bc2c26e4b57a49e097febb54ba"
      url: "https://pub.dev"
    source: hosted
    version: "6.4.0"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: af060ed76183f9e2b87510a9480e56a5352b6c249778d07bd2c95fc35632a555
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "2fde1607386ab523f7a36bb3e7edb43bd58e6edaf2ffb29d8a6d578b297fdbbd"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.7"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: "2b7fff16a552486d078bfc09a8cde19f426dc6d6329262b684182597bec5b1ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.25"
  bloc:
    dependency: "direct main"
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: "959525d3162f249993882720d52b7e0c833978df229be20702b33d48d91de70f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: e1ea89119e34903dca74b883d0dd78eb762814f97fb6c76f35e9ff74d261a18f
      url: "https://pub.dev"
    source: hosted
    version: "7.0.3"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: "825aec673606875c33cd8d3c4083f1a3c3999015a84178b317b7ef396b7384f3"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.7"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: "526faf84284b86a4cb36d20a5e45147747b7563d921373d4ee0559c54fcdbcea"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "9e8c3858111da373efc5aa341de011d9bd23e2c5c5e0c62bccf32438e192d7b1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: f948e346c12f8d5480d2825e03de228d0eb8c3a737e4cdaa122267b89c022b5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.28"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  functions_client:
    dependency: transitive
    description:
      name: functions_client
      sha256: "91bd57c5ee843957bfee68fdcd7a2e8b3c1081d448e945d33ff695fb9c2a686c"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  go_router:
    dependency: "direct main"
    description:
      name: go_router
      sha256: f02fd7d2a4dc512fec615529824fdd217fecb3a3d3de68360293a551f21634b3
      url: "https://pub.dev"
    source: hosted
    version: "14.8.1"
  gotrue:
    dependency: transitive
    description:
      name: gotrue
      sha256: "941694654ab659990547798569771d8d092f2ade84a72e75bb9bbca249f3d3b1"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "2c11f3f94c687ee9bad77c171151672986360b2b001d109814ee7140b2cf261b"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "178d74305e7866013777bab2c3d8726205dc5a4dd935297175b19a23a2e66571"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "4e973fcf4caae1a4be2fa0a13157aa38a8f9cb049db6529aa00b4d71abc4d928"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.4"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  just_audio:
    dependency: "direct main"
    description:
      name: just_audio
      sha256: "50ed9f0ba88012eabdef7519ba6040bdbcf6c6667ebd77736fb25c196c98c0f3"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.44"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "4cd94536af0219fa306205a58e78d67e02b0555283c1c094ee41e402a14a5c4a"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "9a98035b8b24b40749507687520ec5ab404e291d2b0937823ff45d92cb18d448"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.13"
  jwt_decode:
    dependency: transitive
    description:
      name: jwt_decode
      sha256: d2e9f68c052b2225130977429d30f187aa1981d789c76ad104a32243cfdebfbb
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: d0d310befe2c8ab9e7f393288ccbb11b60c019c6b5afc21973eeee4dda2b35e9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.17"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "07c8f0b1913bcde1ff0d26e57ace2f3012ccbf2b204e070290dad3bb22797646"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  posix:
    dependency: transitive
    description:
      name: posix
      sha256: "6323a5b0fa688b6a010df4905a56b00181479e6d10534cecfecede2aa55add61"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.3"
  postgrest:
    dependency: transitive
    description:
      name: postgrest
      sha256: "10b81a23b1c829ccadf68c626b4d66666453a1474d24c563f313f5ca7851d575"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: "4abbd070a04e9ddc287673bf5a030c7ca8b685ff70218720abab8b092f53dd84"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  realtime_client:
    dependency: transitive
    description:
      name: realtime_client
      sha256: b6a825a4c80f2281ebfbbcf436a8979ae9993d4a30dbcf011b7d2b82ddde9edd
      url: "https://pub.dev"
    source: hosted
    version: "2.5.1"
  retry:
    dependency: transitive
    description:
      name: retry
      sha256: "822e118d5b3aafed083109c72d5f484c6dc66707885e07c0fbcb8b986bba7efc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5c3004a4a8dbb94bd4bf5412a4def4acdaa12e12f269737a5751369e12d1a962"
      url: "https://pub.dev"
    source: hosted
    version: "0.28.0"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: ef3489a969683c4f3d0239010cc8b7a2a46543a8d139e111c06c558875083544
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "0f9e4418835d1b2c3ae78fdb918251959106cefdbc4dd43526e182f80e82f6d4"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "6e8bf70b7fef813df4e9a36f658ac46d107db4b4cfe1048b477d4e453a8159f5"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "20cbd561f743a342c76c151d6ddb93a9ce6005751e7aa458baad3858bfbfb6ac"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.10"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  storage_client:
    dependency: transitive
    description:
      name: storage_client
      sha256: "09bac4d75eea58e8113ca928e6655a09cc8059e6d1b472ee801f01fde815bcfc"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  supabase:
    dependency: transitive
    description:
      name: supabase
      sha256: "56c3493114caac8ef0dc3cac5fa24a9edefeb8c22d45794814c0fe3d2feb1a98"
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  supabase_flutter:
    dependency: "direct main"
    description:
      name: supabase_flutter
      sha256: "66b8d0a7a31f45955b11ad7b65347abc61b31e10f8bdfa4428501b81f5b30fa5"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: f6a7e5c4835bb4e3026a04793a4199ca2d14c739ec378fdfe23fc8075d0439f8
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "8582d7f6fe14d2652b4c45c9b6c14c0b678c2af2d083a11b604caeba51930d79"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.16"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7f2022359d4c099eea7df3fdf739f7d3d3b9faf3166fb1dd390775176e0b76cb"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.3"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "4bd2b7b4dc4d4d0b94e5babfffbca8eac1a126c7f3d6ecbc1a11013faa3abba2"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: "direct main"
    description:
      name: web_socket_channel
      sha256: "58c6666b342a38816b2e7e50ed0f1e261959630becd4c879c4f26bfa14aa5a42"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "66814138c3562338d05613a6e368ed8cfb237ad6d64a9e9334be3f309acfca03"
      url: "https://pub.dev"
    source: hosted
    version: "5.14.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  yet_another_json_isolate:
    dependency: transitive
    description:
      name: yet_another_json_isolate
      sha256: fe45897501fa156ccefbfb9359c9462ce5dec092f05e8a56109db30be864f01e
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
sdks:
  dart: ">=3.8.0 <4.0.0"
  flutter: ">=3.29.0"
</file>

<file path="pubspec.yaml">
name: xinglian
description: A Flutter clone of the Xinglian app.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  intl: ^0.19.0 # <--- 添加这行 (版本号可能需要根据你的项目调整)
  # 路由管理 - 方便进行复杂的页面跳转和参数传递
  go_router: ^14.1.0

  # 状态管理 - 后续动态化页面必备 (此处选择 Bloc，你也可以用Riverpod)
  flutter_bloc: ^8.1.5
  bloc: ^8.1.4

  # BLoC模式最佳实践 - 用于状态比较
  equatable: ^2.0.5

  # 瀑布流布局
  flutter_staggered_grid_view: ^0.7.0

  # 本地存储 - 用于持久化用户设置
  shared_preferences: ^2.2.3

  # 网络请求库
  dio: ^5.4.3+1

  # Provider状态管理 - 用于ApiClient单例
  provider: ^6.1.2

  # WebSocket通信库
  web_socket_channel: ^2.4.5

  # Supabase 客户端
  supabase_flutter: ^2.5.8

  # 获取设备信息 (Web兼容)
  device_info_plus: ^10.1.0

  # 音频播放库
  just_audio: ^0.9.36

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # --- v 新增依赖 v ---
  # 用于截图功能
  screenshot: ^3.0.0 

  # 用于调用原生分享功能
  share_plus: ^9.0.0

  # 用于获取保存截图的临时目录
  path_provider: ^2.1.3
  
  # 用于实现打字机等文本动画
  animated_text_kit: ^4.2.2

  # 文件选择器 - 用于角色卡导入
  file_picker: ^8.0.0+1
  # --- ^ 新增依赖 ^ ---

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

  # --- v 新增图标生成工具 v ---
  flutter_launcher_icons: ^0.13.1
  # --- ^ 新增图标生成工具 ^ ---

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # 在这里添加 fonts 部分
  fonts:
    - family: BaiWuChangKeKeTi
      fonts:
        - asset: assets/fonts/BaiWuChangKeKeTi-Regular.ttf
          # 如果你有不同字重的字体文件，也可以在这里指定
          # weight: 400
    - family: YanShiYouRanXiaoKai
      fonts:
        - asset: assets/fonts/YanShiYouRanXiaoKai-Regular.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# --- v 新增图标配置 v ---
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # optional
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/images/logo.png"
# --- ^ 新增图标配置 ^ ---
</file>

<file path="role_import.bat">
@echo off
echo "Running role_import.py..."
python "role/role_import.py"
pause
</file>

<file path="role/import_config.json">
{
  "default_user_id": "00000000-0000-0000-0000-000000000000",
  "imagekit_settings": {
    "folder": "/imported_characters/",
    "tags": ["character_card", "imported", "tavern_ai"],
    "use_unique_filename": true
  },
  "import_settings": {
    "skip_existing": false,
    "auto_generate_missing_fields": true,
    "default_voice": "Kore",
    "default_gender": "other",
    "make_public": true
  },
  "validation_rules": {
    "require_name": true,
    "require_description": false,
    "min_name_length": 1,
    "max_name_length": 100,
    "allowed_image_formats": [".png"],
    "max_file_size_mb": 10
  },
  "output_settings": {
    "generate_report": true,
    "save_extracted_json": false,
    "verbose_logging": true,
    "backup_original_files": false
  }
}
</file>

<file path="role/role_import.py">
#!/usr/bin/env python3
"""
批量角色卡导入工具 - V3.0 (重构版)
从/role/目录下的所有PNG文件中解析TavernAI角色卡，
使用 <EMAIL> 用户身份，将角色作为公开角色导入。
"""

import os
import sys
import base64
import json
import asyncio
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List

from PIL import Image, PngImagePlugin
from dotenv import load_dotenv
from supabase import create_client, Client

# --- 核心设置 ---
# 添加backend目录到Python路径，以便导入后端服务
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))
load_dotenv(backend_dir / '.env')

# 导入后端服务
# V5.0 统一服务入口
from src.services.llm_service import llm_service
from src.services import supabase_service, simple_imagekit_service

# --- 全局配置 ---
ROLE_DIR = Path(__file__).parent  # /role/ 目录
META_USER_EMAIL = "<EMAIL>"
META_USER_PASSWORD = "password123" # 默认密码

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)


async def get_or_create_metarole_user() -> Optional[str]:
    """
    获取或创建 <EMAIL> 用户，并返回用户ID。
    如果用户已存在，直接返回其ID。
    """
    email = META_USER_EMAIL
    password = META_USER_PASSWORD
    
    try:
        # 检查用户是否存在
        response = await asyncio.to_thread(lambda: supabase_admin_client.auth.admin.list_users())
        users_list = getattr(response, 'users', response)
        existing_user = next((u for u in users_list if u.email == email), None)
        if existing_user:
            print(f"✅ 用户 '{email}' 已存在。")
            return str(existing_user.id)
    except Exception as e:
        print(f"⚠️ 检查用户时发生警告: {e}。")

    # 创建用户
    try:
        print(f"🔄 正在创建新用户 '{email}'...")
        user_response = await asyncio.to_thread(
            lambda: supabase_admin_client.auth.admin.create_user({
                "email": email, "password": password, "email_confirm": True, "user_metadata": {"display_name": "Meta Role Importer"}
            })
        )
        user_id = str(user_response.user.id)
        await asyncio.sleep(1.5) # 等待profile触发器完成
        
        # 更新profile
        profile_update_data = {'display_name': 'Meta Role Importer'}
        await asyncio.to_thread(
            lambda: supabase_admin_client.table('user_profiles').update(profile_update_data).eq('user_id', user_id).execute()
        )
        print(f"✅ 成功创建用户 '{email}'。")
        return user_id
    except Exception as e:
        if 'User already registered' in str(e):
            print(f"ℹ️ 用户 '{email}' 已注册，正在重新获取...")
            response = await asyncio.to_thread(lambda: supabase_admin_client.auth.admin.list_users())
            users_list = getattr(response, 'users', response)
            existing_user = next((u for u in users_list if u.email == email), None)
            if existing_user:
                return str(existing_user.id)
        
        print(f"❌ 创建或获取用户 '{email}' 失败: {e}")
        return None


class CharacterCardImporter:
    """角色卡批量导入器"""

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.success_count = 0
        self.error_count = 0
        self.errors: List[Dict[str, Any]] = []

    def extract_character_data_from_png(self, image_path: Path) -> Optional[Dict[str, Any]]:
        """从PNG文件中提取角色卡数据"""
        try:
            with Image.open(image_path) as img:
                if not isinstance(img, PngImagePlugin.PngImageFile):
                    print(f"⚠️  文件不是有效的PNG: {image_path.name}")
                    return None
                
                text_meta = img.text or {}
                if 'chara' in text_meta:
                    b64_data = text_meta['chara']
                    decoded = base64.b64decode(b64_data).decode('utf-8')
                    return json.loads(decoded)
                else:
                    print(f"⚠️  PNG文件中未找到'chara'数据: {image_path.name}")
                    return None
        except Exception as e:
            print(f"❌ 解析角色卡数据失败 {image_path.name}: {e}")
            return None

    # --- ▼▼▼ 核心修改区域：注入了新的清洗提示词 ▼▼▼ ---
    async def translate_and_refine_character_data(self, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM翻译、清洗和整理角色数据。"""
        print("  - 正在使用AI进行翻译、清洗和内容整理...")
        
        original_data_json = json.dumps(original_data, ensure_ascii=False, indent=2)
        
        prompt = f"""你是一名顶级的游戏角色本地化专家和内容审核员。你的任务是接收一个JSON格式的TavernAI角色卡数据，并根据以下【核心规则】将其转换为符合规范的、干净的中文版本。

**【核心规则】**
1.  **HTML标签清洗 (最重要！)**:
    *   你必须彻底清除所有文本字段中的全部HTML标签 (例如 `<p>`, `<strong>`, `<span>`, `<b>`, `<br>`)。
    *   最终输出的JSON中，所有字段的值都必须是纯文本，不包含任何HTML代码。
    *   例如：如果输入是 `<p>你好, <strong>世界</strong></p>`，输出必须是 `你好, 世界`。

2.  **内容翻译**:
    *   将所有文本字段（特别是 name, description, personality, scenario, first_mes, mes_example, system_prompt）翻译成符合角色性格的、自然的简体中文。
    *   角色姓名（'name'字段）需要翻译成符合其性格特征的中文名称、昵称或音译。

3.  **内容格式与多样性重塑**:
    *   **system_prompt**: 重写此字段，使其包含以下多样性回复指令： "1. 你的回复必须具备高度的多样性。 2. 你的回复可以自由组合或侧重于：内心独白、环境观察、动作、直接对话。所有非对话部分都用英文半角括号 () 包裹。 3. 不要总是使用“文字(动作)文字”的固定格式，有时纯动作或独白就足够了。"
    *   **mes_example**: 基于原始示例的意图，重写此字段以展示多种回复风格。所有非对话部分都必须用英文半角括号 () 包裹。例如：
        - {{{{user}}}}: 你在想什么？\\n{{{{char}}}}: (他为什么会知道这件事？难道是……) '你从哪听说的？'
        - {{{{user}}}}: 我们下一步怎么办？\\n{{{{char}}}}: (他沉默地站起身，走到窗边，冰冷的夜风吹动了他的衣角。)
    *   **first_mes**: 保持 `文字(动作)文字` 格式，确保括号内有丰富的非语言描述。

4.  **内容合规性修改**:
    *   仔细审查所有文本内容，删除或修改任何涉及"色情/暴力/血腥/政治"的敏感内容，并用合规但能体现性格的内容创意性地替换。

**任务要求**:
请处理以下JSON数据，并返回一个结构完全相同、但内容经过清洗、翻译和重塑的JSON对象。不要添加任何额外的解释或注释，只返回JSON。

**原始角色数据:**
```json
{original_data_json}
```

**处理后的中文角色数据 (JSON格式):**"""

        try:
            # 使用统一的LLM服务和指定的模型
            refined_data = await llm_service.generate_json(
                prompt,
                model_name=os.getenv("GEMINI_CHAT_MODEL", "gemini-2.5-flash")
            )
            print("  - ✅ AI内容处理成功。")
            return refined_data
        except Exception as e:
            print(f"  - ❌ AI内容处理失败: {e}。将使用原始数据继续导入。")
            return original_data
    # --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---

    async def upload_image_to_imagekit(self, image_path: Path, character_name: str) -> Optional[str]:
        """上传图片到ImageKit并返回URL"""
        try:
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            base64_data = base64.b64encode(image_bytes).decode('utf-8')
            
            safe_name = "".join(c for c in character_name if c.isalnum() or c in (' ', '-', '_')).rstrip().replace(' ', '_')
            file_name = f"{safe_name}_{uuid.uuid4().hex[:6]}.png"

            upload_result = await simple_imagekit_service.upload_image_from_base64(
                base64_data=base64_data,
                file_name=file_name,
                folder="/xinglian/agent/",
                tags=["character_card", "imported"]
            )

            if upload_result["success"]:
                return upload_result.get("url")
            else:
                print(f"❌ 图片上传失败: {upload_result.get('error')}")
                return None
        except Exception as e:
            print(f"❌ 上传图片时发生错误: {e}")
            return None

    async def import_character_to_database(self, character_data: Dict[str, Any], image_url: Optional[str]) -> Optional[Dict[str, Any]]:
        """将角色数据导入到数据库"""
        try:
            # 确保角色是公开的
            character_data['is_public'] = True
            character_data['avatar_url'] = image_url # 确保头像URL也被设置

            # 兼容旧版和新版字段：将 'first_mes' 或 'opening_line' 统一到 'first_mes'
            if 'first_mes' not in character_data and 'opening_line' in character_data:
                character_data['first_mes'] = character_data.pop('opening_line')

            # 使用supabase_service导入角色卡
            new_agent = await supabase_service.create_agent_from_character_card(
                user_id=self.user_id,
                character_data=character_data,
                image_url=image_url,
                spec=character_data.get('spec', 'chara_card_v1'),
                spec_version=character_data.get('spec_version', '1.0')
            )
            return new_agent
        except Exception as e:
            print(f"❌ 导入数据库失败: {e}")
            return None

    async def process_single_character(self, image_path: Path):
        """处理单个角色卡文件"""
        print(f"\n🔄 处理文件: {image_path.name}")
        
        # 1. 提取数据
        character_data = self.extract_character_data_from_png(image_path)
        if not character_data:
            self.error_count += 1
            self.errors.append({'file': image_path.name, 'error': '无法提取角色卡数据'})
            return

        # v2格式兼容
        if 'spec' in character_data and 'data' in character_data:
            character_data = character_data['data']

        # 调用AI进行翻译和清洗
        refined_data = await self.translate_and_refine_character_data(character_data)

        character_name = refined_data.get('name', 'Unknown')
        if character_name == 'Unknown' or not character_name.strip():
            print(f"❌ 文件 {image_path.name} 缺少角色名称，跳过。")
            self.error_count += 1
            self.errors.append({'file': image_path.name, 'error': '角色名称为空'})
            return
            
        print(f"  - 角色名称 (处理后): {character_name}")

        # 2. 上传图片到ImageKit
        print(f"  - 上传图片...")
        image_url = await self.upload_image_to_imagekit(image_path, character_name)
        if not image_url:
            print(f"  - ⚠️  图片上传失败，将不带图片继续导入。")

        # 3. 导入数据库（使用整理后的数据）
        print(f"  - 导入数据库...")
        new_agent = await self.import_character_to_database(refined_data, image_url)
        if not new_agent:
            self.error_count += 1
            self.errors.append({'file': image_path.name, 'character': character_name, 'error': '数据库导入失败'})
            return

        self.success_count += 1
        print(f"  - ✅ 成功导入角色: {character_name} (ID: {new_agent['id']})")


    async def batch_import(self):
        """批量导入所有角色卡"""
        print("🚀 开始批量导入角色卡...")
        print(f"📁 扫描目录: {ROLE_DIR}")

        png_files = list(ROLE_DIR.glob("*.png"))
        if not png_files:
            print("❌ 未找到任何PNG文件。")
            return

        print(f"📊 找到 {len(png_files)} 个PNG文件。")

        for image_path in png_files:
            await self.process_single_character(image_path)

        # 报告结果
        total = len(png_files)
        print("\n" + "="*60)
        print("📈 导入完成!")
        print(f"  - 总文件数: {total}")
        print(f"  - 成功导入: {self.success_count}")
        print(f"  - 失败数量: {self.error_count}")

        if self.errors:
            print("\n❌ 错误详情:")
            for error in self.errors:
                print(f"  - 文件: {error.get('file', 'N/A')}, 角色: {error.get('character', 'N/A')}, 错误: {error.get('error')}")
        print("="*60)


async def main():
    """主函数"""
    print("=" * 60)
    print("🌟 星恋AI - 批量角色卡导入工具 V3.0")
    print("=" * 60)

    # 1. 检查服务连接
    print("🔍 检查服务连接...")
    try:
        if (await supabase_service.health_check())["status"] != "healthy":
            print("❌ Supabase数据库连接失败。")
            return
        print("✅ Supabase数据库连接正常。")
        
        if not (await simple_imagekit_service.health_check())["success"]:
            print("❌ ImageKit服务连接失败。")
            return
        print("✅ ImageKit服务连接正常。")
    except Exception as e:
        print(f"❌ 服务连接检查失败: {e}")
        return

    # 2. 获取或创建导入专用用户
    print("\n🔍 准备导入用户...")
    user_id = await get_or_create_metarole_user()
    if not user_id:
        print("❌ 无法获取或创建导入所需的用户。脚本终止。")
        return
    print(f"✅ 使用用户ID: {user_id}")

    # 3. 开始导入
    importer = CharacterCardImporter(user_id=user_id)
    await importer.batch_import()

    print("\n🎉 脚本执行完毕。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ 用户中断导入。")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
</file>

<file path="setup.bat">
@echo off
:: Set UTF-8 code page for Chinese character support
chcp 65001 > nul 2>&1

:: Set window title
title XingLian AI - Development Environment Setup

cls
echo.
echo [INFO] ==========================================================
echo [INFO] ==    Starting XingLian AI Backend Environment Setup   ==
echo [INFO] ==========================================================
echo.

:: Change to backend directory relative to this script
cd /d "%~dp0backend"
echo [INFO] Working directory: %cd%
echo.

:: Check Python installation first
echo [STEP 0/4] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found! Please install Python and add it to PATH.
    goto :error
)
for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Found: %PYTHON_VERSION%
echo.

:: 1. Check and create Python virtual environment
echo [STEP 1/4] Checking Python virtual environment (venv)...

:: Check if venv exists and is valid
if exist venv (
    echo [INFO] Virtual environment folder exists, checking validity...
    call venv\Scripts\activate >nul 2>&1
    if %errorlevel% neq 0 (
        echo [WARNING] Virtual environment appears corrupted, recreating...
        rmdir /s /q venv
        goto :create_venv
    ) else (
        echo [INFO] Virtual environment is valid, skipping creation.
        goto :check_env
    )
) else (
    :create_venv
    echo [INFO] Virtual environment not found, creating new one...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to create virtual environment! Check Python installation.
        goto :error
    )
    echo [SUCCESS] Virtual environment created successfully!
)
echo.

:check_env
:: Activate virtual environment for subsequent operations
call venv\Scripts\activate
if %errorlevel% neq 0 (
    echo [ERROR] Failed to activate virtual environment!
    goto :error
)

:: 2. Check and create .env configuration file
echo [STEP 2/4] Checking .env configuration file...
if not exist .env (
    echo [INFO] .env file not found, creating via create_env.py...
    python create_env.py
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to create .env file!
        goto :error
    )
    echo [SUCCESS] .env file created successfully!
) else (
    echo [INFO] .env file already exists, skipping creation.
)
echo.

:: 3. Upgrade pip first
echo [STEP 3/4] Upgrading pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo [WARNING] Failed to upgrade pip, continuing anyway...
)
echo.

:: 4. Install or update all dependencies
echo [STEP 4/4] Installing/updating dependencies (from requirements.txt)...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies! Check network or requirements.txt file.
    goto :error
)
echo [SUCCESS] All dependencies installed/updated successfully!
echo.

echo.
echo [COMPLETE] =====================================================
echo [COMPLETE] ==        Development Environment Ready!           ==
echo [COMPLETE] ==                                                 ==
echo [COMPLETE] ==        You can now run start.bat to start      ==
echo [COMPLETE] ==        the server or novel_compiler.bat        ==
echo [COMPLETE] ==        to compile novels.                      ==
echo [COMPLETE] =====================================================
echo.
goto :end

:error
echo.
echo [FAILED] !!! Environment setup failed, check error messages above. !!!
echo.

:end
pause
</file>

<file path="start.bat">
@echo off
chcp 65001 > nul

title 星恋AI - 开发环境启动器

echo.
echo [INFO] =======================================================
echo [INFO] ==      XingLian AI - Full-Stack Launcher            ==
echo [INFO] =======================================================
echo.

echo [STEP 1/4] 检查项目环境...

if not exist "%~dp0backend\venv\Scripts\python.exe" (
    echo [ERROR] 后端虚拟环境 'backend\venv' 未找到或已损坏!
    echo [ERROR] 请先成功运行一次 setup.bat 来完成初始化设置。
    goto :error
)
echo [SUCCESS] 后端环境检查通过。

set "PYTHON_EXE=%~dp0backend\venv\Scripts\python.exe"

echo.
echo [STEP 2/4] 确保测试用户存在...
cd /d "%~dp0"
"%PYTHON_EXE%" "backend\scripts\ensure_test_user.py"
echo.

echo [INFO] 检查 Node.js 和前端环境...
where node >nul 2>nul
if errorlevel 1 (
    echo [WARNING] Node.js 未找到或未正确配置！
    echo [WARNING] 将跳过前端启动，仅启动后端服务器。
    echo [INFO] 如需启动前端，请：
    echo [INFO] 1. 安装 Node.js: https://nodejs.org/
    echo [INFO] 2. 在 web 目录运行: npm install
    echo [INFO] 3. 重新运行此脚本
    set "SKIP_FRONTEND=1"
) else (
    if not exist "%~dp0web\node_modules" (
        echo [WARNING] 前端依赖未安装！
        echo [WARNING] 将跳过前端启动，仅启动后端服务器。
        echo [INFO] 请先在 web 目录运行: npm install
        set "SKIP_FRONTEND=1"
    ) else (
        echo [SUCCESS] 前端环境检查通过。
        set "SKIP_FRONTEND=0"
    )
)
echo.

echo [STEP 3/4] 在新窗口中启动后端服务器...
echo [INFO] 后端日志将在一个名为 'BACKEND SERVER' 的新窗口中显示。
START "BACKEND SERVER" cmd /k "cd /d "%~dp0backend" & "%PYTHON_EXE%" -m uvicorn src.supabase_main:app --host 0.0.0.0 --port 8000 --reload"
echo [SUCCESS] 后端服务器正在启动...
echo.

if "%SKIP_FRONTEND%"=="1" (
    echo [STEP 4/4] 跳过前端启动...
    echo [INFO] 仅启动后端服务器，前端需要手动启动。
    echo.
    echo [COMPLETE] =====================================================
    echo [COMPLETE] ==       后端服务已启动！                         ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==   - 后端服务运行于: http://127.0.0.1:8000       ==
    echo [COMPLETE] ==   - 前端需要安装 Node.js 后手动启动             ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==       可以关闭此启动器窗口。                     ==
    echo [COMPLETE] =====================================================
    echo.
) else (
    echo [STEP 4/4] 在新窗口中启动前端开发服务器...
    echo [INFO] 前端日志将在一个名为 'FRONTEND SERVER' 的新窗口中显示。
    echo [INFO] Next.js 首次编译可能需要一些时间，请耐心等待...
    START "FRONTEND SERVER" cmd /k "cd /d "%~dp0web" & npm run dev"
    echo [SUCCESS] 前端服务器正在启动...
    echo.
    echo.
    echo [COMPLETE] =====================================================
    echo [COMPLETE] ==       所有服务均已在独立窗口中启动！         ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==   - 后端服务运行于: http://127.0.0.1:8000       ==
    echo [COMPLETE] ==   - 前端服务运行于: http://localhost:3000       ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==       可以关闭此启动器窗口。                     ==
    echo [COMPLETE] =====================================================
    echo.
)

:success
echo.
echo ------------------------------------------------------------------
echo [INFO] 服务器已正常停止。
goto :end

:error
echo.
echo ------------------------------------------------------------------
echo [FATAL] 脚本执行过程中发生严重错误！
echo [FATAL] 请检查上面的错误日志。

:end
echo.
pause
</file>

</files>
