"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Home,
  MessageSquare,
  User,
  Plus,
  Package,
  Heart,
  ThumbsUp,
  Trophy,
  HelpCircle,
  CreditCard,
  Shield,
  Twitter,
  Github,
  DiscIcon as Discord,
  Menu,
  Lock,
  Loader2,
} from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"

export default function Sidebar() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [isLoggingIn, setIsLoggingIn] = useState(false)

  // 使用真实的认证状态
  const { user, isGuest, isLoading, signInAsGuest, signOut } = useAuth()
  const isAuthenticated = !!user

  const isActive = (path: string) => {
    return pathname === path || pathname?.startsWith(path + "/")
  }

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const handleProtectedRoute = (e: React.MouseEvent, path: string) => {
    if (!isAuthenticated && (path.includes("persona") || path.includes("chatbot") || path.includes("favorite"))) {
      e.preventDefault()
      setShowLoginModal(true)
    }
  }

  const handleGuestLogin = async () => {
    setIsLoggingIn(true)
    try {
      await signInAsGuest()
      setShowLoginModal(false)
    } catch (error) {
      console.error('Guest login failed:', error)
    } finally {
      setIsLoggingIn(false)
    }
  }

  const NavLink = ({
    href,
    icon: Icon,
    children,
    requireAuth = false,
  }: {
    href: string
    icon: any
    children: React.ReactNode
    requireAuth?: boolean
  }) => {
    const content = (
      <div
        className={`flex items-center gap-3 px-3 py-2 rounded-md ${isActive(href) ? "bg-accent" : "hover:bg-accent/50"}`}
      >
        <Icon size={20} />
        {!isCollapsed && (
          <span className="flex items-center gap-2">
            {children}
            {requireAuth && !isAuthenticated && <Lock size={14} className="text-gray-400" />}
          </span>
        )}
      </div>
    )

    if (requireAuth && !isAuthenticated) {
      return (
        <div
          className="cursor-pointer"
          onClick={(e) => handleProtectedRoute(e, href)}
          title={isCollapsed ? `${children} (Login Required)` : ""}
        >
          {content}
        </div>
      )
    }

    return (
      <Link href={href} title={isCollapsed ? children?.toString() : ""}>
        {content}
      </Link>
    )
  }

  return (
    <>
      <div
        className={`${isCollapsed ? "w-16" : "w-64"} h-screen bg-background border-r flex flex-col transition-all duration-300`}
      >
        <div className="p-4 border-b flex items-center gap-2">
          <Button variant="ghost" size="icon" className="mr-2" onClick={toggleSidebar}>
            <Menu className="h-6 w-6" />
          </Button>
          {!isCollapsed && (
            <span className="font-bold text-lg">
              SPICYCHAT <span className="text-blue-500">AI</span>
            </span>
          )}
        </div>

        <div className="flex-1 overflow-auto py-2">
          <nav className="space-y-1 px-2">
            <NavLink href="/" icon={Home}>
              Home
            </NavLink>
            <NavLink href="/chats" icon={MessageSquare}>
              Chats
            </NavLink>
            <NavLink href="/personas" icon={User} requireAuth={true}>
              My Personas
            </NavLink>

            {!isCollapsed && (
              <div className="pt-4 pb-2 px-3">
                <h3 className="font-medium text-sm text-muted-foreground">Chatbots</h3>
              </div>
            )}

            <NavLink href="/chatbot/create" icon={Plus} requireAuth={true}>
              Create Chatbot
            </NavLink>
            <NavLink href="/my-chatbots" icon={Package} requireAuth={true}>
              My Chatbots
            </NavLink>
            <NavLink href="/favorite-bots" icon={Heart} requireAuth={true}>
              Favorites
            </NavLink>
            <NavLink href="/recommended-bots" icon={ThumbsUp}>
              Recommendations
            </NavLink>
            <NavLink href="/creators/leaderboard" icon={Trophy}>
              Leaderboard
            </NavLink>
            <NavLink href="/blocked-creators" icon={Shield} requireAuth={true}>
              Blocked Creators
            </NavLink>

            {!isCollapsed && <div className="pt-4 pb-2"></div>}

            <NavLink href="/subscribe" icon={CreditCard}>
              Subscribe
            </NavLink>
            <NavLink href="/help" icon={HelpCircle}>
              Help
            </NavLink>
          </nav>
        </div>

        <div className="p-4 border-t">
          {!isAuthenticated ? (
            <div className="space-y-2">
              <Button 
                className="w-full bg-blue-600 hover:bg-blue-700" 
                onClick={handleGuestLogin}
                disabled={isLoggingIn}
              >
                <span className={`flex items-center justify-center gap-2 ${isCollapsed ? "px-0" : ""}`}>
                  {isLoggingIn ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-log-in"
                    >
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
                      <polyline points="10 17 15 12 10 7" />
                      <line x1="15" x2="3" y1="12" y2="12" />
                    </svg>
                  )}
                  {!isCollapsed && (isLoggingIn ? "Signing In..." : "Guest Login")}
                </span>
              </Button>
              {!isCollapsed && (
                <Button className="w-full bg-transparent" variant="outline" asChild>
                  <Link href="/sign-in">
                    <span className="flex items-center justify-center gap-2">
                      <User size={18} />
                      Full Sign In
                    </span>
                  </Link>
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-xs text-gray-400 text-center">
                {isGuest ? "Guest User" : user?.display_name || user?.email || "User"}
              </div>
              <Button 
                className="w-full bg-transparent" 
                variant="outline"
                onClick={signOut}
              >
                <span className={`flex items-center justify-center gap-2 ${isCollapsed ? "px-0" : ""}`}>
                  <User size={18} />
                  {!isCollapsed && "Sign Out"}
                </span>
              </Button>
            </div>
          )}

          {!isCollapsed && (
            <>
              <div className="mt-4 flex justify-between text-xs text-muted-foreground">
                <div className="flex gap-2">
                  <Link href="/terms" className="hover:underline">
                    Terms
                  </Link>
                  <Link href="/privacy" className="hover:underline">
                    Privacy
                  </Link>
                  <Link href="/refunds" className="hover:underline">
                    Refunds
                  </Link>
                </div>
              </div>
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <div className="flex gap-2">
                  <Link href="/reporting" className="hover:underline">
                    Reporting
                  </Link>
                  <Link href="/guidelines" className="hover:underline">
                    Guidelines
                  </Link>
                </div>
              </div>
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <div className="flex gap-2">
                  <Link href="/support" className="hover:underline">
                    Support
                  </Link>
                  <Link href="/affiliates" className="hover:underline">
                    Affiliates
                  </Link>
                </div>
                <div className="flex gap-2">
                  <Link href="#" aria-label="Twitter">
                    <Twitter size={16} />
                  </Link>
                  <Link href="#" aria-label="GitHub">
                    <Github size={16} />
                  </Link>
                  <Link href="#" aria-label="Discord">
                    <Discord size={16} />
                  </Link>
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between text-xs text-muted-foreground">
                <Link href="https://apps.apple.com/app/spicychat" className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-apple"
                  >
                    <path d="M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z" />
                    <path d="M10 2c1 .5 2 2 2 5" />
                  </svg>
                  App Store
                </Link>
                <Link
                  href="https://play.google.com/store/apps/details?id=ai.spicychat"
                  className="flex items-center gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-android-2"
                  >
                    <path d="M18 4h1a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1" />
                    <path d="M10 2h4" />
                    <path d="M5 11h14" />
                    <path d="M12 18.5v.5" />
                  </svg>
                  Android
                </Link>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Login Modal */}
      <Dialog open={showLoginModal} onOpenChange={setShowLoginModal}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle>Sign In Required</DialogTitle>
            <DialogDescription className="text-gray-400">You need to sign in to access this feature.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700"
              onClick={handleGuestLogin}
              disabled={isLoggingIn}
            >
              {isLoggingIn ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Signing in as Guest...
                </>
              ) : (
                "Continue as Guest"
              )}
            </Button>
            <div className="text-center text-gray-400">OR</div>
            <div>
              <label className="text-sm font-medium text-white">Email</label>
              <input
                type="email"
                className="w-full mt-1 p-3 bg-gray-800 border border-gray-600 rounded-md text-white"
                placeholder="Enter your email"
              />
            </div>
            <Button className="w-full bg-gray-700 hover:bg-gray-600">Continue with Email</Button>
            <div className="space-y-2">
              <Button variant="outline" className="w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                <span className="mr-2">G</span> Continue with Google
              </Button>
              <Button variant="outline" className="w-full bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                <span className="mr-2">🍎</span> Continue with Apple
              </Button>
            </div>
            <div className="text-center">
              <Link href="/sign-up" className="text-blue-400 hover:underline text-sm">
                Don't have an account? Sign up
              </Link>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
