#!/usr/bin/env python3
"""
星恋AI - V5.0 心动原型精准复现系统
基于中国女性偏好男星形象全景分析的原型驱动角色生成引擎。

运行前置条件:
1. 后端API服务器 (通过 start.bat) 必须正在另一个终端中运行。
2. .env 文件必须已正确配置。
3. backend/src/data/archetypes.json 文件必须存在。

该脚本将:
1. 创建一个唯一的、可预测的测试用户 (<EMAIL>)。
2. 基于18种心动原型，精准生成高吸引力的AI男主角色。
3. 每个角色都严格遵循对应原型的气质、外观和吸引力要素。
"""
import os
import sys
import asyncio
import httpx
import time
import uuid
import re
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
from supabase import create_client, Client
import base64

# --- 核心设置 ---
# 将项目根目录添加到Python路径，以便导入src中的模块
sys.path.append(str(Path(__file__).parent.parent))
load_dotenv(Path(__file__).parent.parent / '.env')

from src.services import llm_service, simple_imagekit_service, supabase_service
from src.pydantic_models import GeneratedCoreAgentData
from src.utils.user_management import create_user_if_not_exists

# --- 全局配置 ---
API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

# 要生成的智能体的"心动原型"名称（必须与 archetypes.json 中的 archetype_name 完全对应）
AGENT_IDEAS = [
    "清新少年感",
    "国风君子",
    "叔系天花板",
    "病娇破碎感",
    "职场精英",
    "多才艺术家",
    "智性学霸",
    "痞帅浪子",
    "温柔守护者",
    "硬汉柔情"
]

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)

# --- 核心函数 ---
# 现在使用统一的用户管理模块

async def generate_and_save_agent_from_archetype(archetype_name: str, user_id: str):
    """基于心动原型，完整地生成并存储一个智能体。"""
    log_prefix = f"  [ARCHETYPE_GEN ({archetype_name})]"
    print(f"\n{log_prefix} 开始生成...")
    try:
        # 1. 使用新的原型驱动方法生成结构化数据、图片和prompt
        structured_data, image_bytes, image_prompt = await llm_service.generate_agent_from_archetype_with_image(archetype_name)

        # 名称和开场白的后处理
        original_name = structured_data.name
        cleaned_name = re.sub(r'\s*\(.*\)\s*|\s*（.*）\s*', '', original_name).strip()
        if original_name != cleaned_name:
            structured_data.name = cleaned_name

        if hasattr(structured_data, 'first_mes') and structured_data.first_mes:
            if structured_data.first_mes.startswith("'") and structured_data.first_mes.endswith("'"):
                structured_data.first_mes = structured_data.first_mes.strip("'")

        print(f"{log_prefix} ✓ 基于原型 '{archetype_name}' 生成角色: '{structured_data.name}'")

        # 2. 上传图片（如果成功生成）
        image_url = None
        if image_bytes:
            print(f"{log_prefix}   ⎿ 上传图片...")
            upload_result = await simple_imagekit_service.upload_image_from_base64(
                base64_data=base64.b64encode(image_bytes).decode('utf-8'),
                file_name=f"{structured_data.name.replace(' ', '_')}_{uuid.uuid4().hex[:6]}.png",
                folder="/xinglian/agent/"
            )
            if upload_result["success"]:
                image_url = upload_result.get("url")
            else:
                print(f"   [WARN] 图片上传失败: {upload_result['error']}，将不使用图片URL。")
        else:
            print(f"   [WARN] 图片生成失败，将不使用图片URL。")

        # 3. 存入数据库 (使用 SupabaseService)
        print(f"{log_prefix}   ⎿ 保存到数据库...")
        new_agent = await supabase_service.create_agent(
            user_id=user_id,
            # 基础信息
            name=structured_data.name,
            description=structured_data.description,
            tags=structured_data.tags,
            image_url=image_url, # 使用可能为None的image_url
            avatar_url=image_url, # 使用可能为None的avatar_url
            gender=structured_data.gender,
            voice_name=structured_data.voice_name,
            # TavernAI兼容字段
            personality=structured_data.persona,
            scenario=structured_data.scenario,
            first_mes=structured_data.first_mes,
            mes_example=structured_data.mes_example,
            # 图片生成prompt
            image_generation_prompt=image_prompt, # 无论图片是否生成成功，都保存prompt
            # 系统字段
            is_public=True,
            is_system_agent=False
        )
        if not new_agent: raise ValueError("保存智能体至数据库失败")
        
        print(f"{log_prefix} ✓ 智能体 '{structured_data.name}' 创建成功！")
        return True

    except Exception as e:
        print(f"{log_prefix} ✗ 生成智能体时发生错误: {e}")
        return False

async def main():
    """主执行函数"""
    start_time = time.time()
    print("="*60)
    print("🚀 [START] 启动心动原型精准复现系统 (V5.0)...")
    print("="*60)
    
    print("\n--- [STEP 1/2] 确保唯一的测试用户存在 ---")
    test_user_id = await create_user_if_not_exists(
        supabase_admin_client,
        TEST_USER_EMAIL,
        TEST_USER_PASSWORD,
        display_name="测试用户"
    )

    if not test_user_id:
        print("\n❌ [FATAL] 无法创建或获取测试用户。脚本终止。")
        return

    print("\n" + "="*60)
    print("IMPORTANT: 测试用户已就绪!")
    print(f"Email:    {TEST_USER_EMAIL}")
    print(f"Password: {TEST_USER_PASSWORD}")
    print(f"User ID:  {test_user_id}")
    print("="*60 + "\n")

    # 修复：使用串行生成而非并发，避免同时请求API导致过载
    print(f"--- [STEP 2/2] 开始串行生成 {len(AGENT_IDEAS)} 个基于原型的智能体 ---")
    print("[INFO] 使用心动原型精准复现系统，请耐心等待...")

    success_count = 0
    for i, archetype_name in enumerate(AGENT_IDEAS):
        if i > 0:
            # 在生成下一个智能体前等待2秒，避免API过载
            await asyncio.sleep(2)
        success = await generate_and_save_agent_from_archetype(archetype_name, test_user_id)
        if success:
            success_count += 1
    
    print(f"\n[INFO] 成功生成 {success_count}/{len(AGENT_IDEAS)} 个智能体")

    end_time = time.time()
    print("\n" + "="*60)
    print("✅ [COMPLETE] 心动原型精准复现系统执行完毕！")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print("数据库已填充基于心动原型的高吸引力AI男主角色。")
    print("="*60)

if __name__ == "__main__":
    print("======================================================")
    print("          星恋AI - V4.1 聊天角色创世引擎")
    print("======================================================")
    print("重要提示: 在运行此脚本前，请确保：")
    print("1. 后端API服务器 (通过 start.bat) 正在另一个终端中运行。")
    print("2. .env 文件已正确配置了所有API密钥。")
    print("------------------------------------------------------\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[INFO] 用户手动中断了脚本。")
    except Exception as e:
        print(f"\n[FATAL] 脚本执行过程中发生未捕获的顶层异常: {e}")
        import traceback
        traceback.print_exc()