#!/usr/bin/env python3
"""
自动确保测试用户存在的脚本
用于开发环境自动化设置
"""

import asyncio
import os
import sys
from pathlib import Path

# 确保可以导入项目模块
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from supabase import create_client
from src.utils.user_management import get_or_create_user

# 加载环境变量
load_dotenv(Path(__file__).parent.parent / '.env')

# 测试用户配置
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"  # 固定密码，方便开发

async def main():
    """主执行函数，确保测试用户存在"""
    print("[AUTO-SETUP] 正在确保测试用户存在...")
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not supabase_url or not supabase_key:
        print("[AUTO-SETUP] 错误：Supabase环境变量未配置！")
        return

    supabase_admin = create_client(supabase_url, supabase_key)
    
    user_id = await get_or_create_user(
        supabase_admin,
        TEST_USER_EMAIL,
        TEST_USER_PASSWORD,
        display_name="测试用户"
    )
    
    if user_id:
        print("-" * 50)
        print("[AUTO-SETUP] ✅ 测试用户已就绪!")
        print(f"[AUTO-SETUP]    Email:    {TEST_USER_EMAIL}")
        print(f"[AUTO-SETUP]    Password: {TEST_USER_PASSWORD}")
        print("-" * 50)
    else:
        print("[AUTO-SETUP] ❌ 创建或获取测试用户失败！")

if __name__ == "__main__":
    asyncio.run(main())