"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ArrowLeft, User } from "lucide-react"
import Link from "next/link"

export default function PersonaPage({ params }: { params: { id: string } }) {
  const [name, setName] = useState("tommick839")
  const [highlights, setHighlights] = useState("")

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="p-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href="/personas">
            <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <span className="text-sm text-gray-400">Back</span>
        </div>

        <h1 className="text-2xl font-bold mb-8">Persona</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Avatar */}
          <div className="space-y-4">
            <div className="w-full max-w-sm mx-auto">
              <div className="aspect-[4/3] bg-gray-800 rounded-lg flex items-center justify-center mb-4">
                <User className="h-16 w-16 text-gray-600" />
              </div>
              <div className="flex gap-2 justify-center">
                <Button className="bg-blue-600 hover:bg-blue-700">Generate Avatar</Button>
                <span className="text-gray-400 self-center">or</span>
                <Button variant="outline" className="border-gray-600 text-white hover:bg-gray-800 bg-transparent">
                  Choose File
                </Button>
              </div>
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-white">
                Name
              </Label>
              <div className="relative">
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="bg-gray-800 border-gray-700 text-white pr-10"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1 h-8 w-8 text-gray-400 hover:text-white"
                >
                  ×
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="highlights" className="text-white">
                Highlights
              </Label>
              <p className="text-sm text-gray-400 mb-2">
                used only in your conversations to help the AI with context. Keep it short (1-2 sentences).
              </p>
              <Textarea
                id="highlights"
                value={highlights}
                onChange={(e) => setHighlights(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white min-h-[120px] resize-none"
                placeholder="Enter highlights..."
              />
            </div>
          </div>
        </div>

        {/* Update Button */}
        <div className="flex justify-center mt-12">
          <Button className="bg-gray-600 hover:bg-gray-700 px-8">Update</Button>
        </div>
      </div>
    </div>
  )
}
