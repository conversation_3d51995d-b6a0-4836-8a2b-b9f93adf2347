#!/usr/bin/env python3
"""
Summarization Service - 滚动摘要与长期记忆系统
负责生成和维护对话的滚动摘要，为AI提供宏观的对话背景
"""

import asyncio
from typing import List, Dict, Any, Optional
from .llm_service import llm_service
from .supabase_service import supabase_service


class SummarizationService:
    """
    滚动摘要服务
    维护对话的长期记忆，通过摘要的方式压缩历史信息
    """
    
    def __init__(self):
        self.summary_trigger_threshold = 15  # 每15条新消息触发一次摘要更新
        self.max_summary_length = 1000      # 摘要最大长度（字符）
        
    async def should_update_summary(self, chat_id: str) -> bool:
        """
        判断是否需要更新摘要
        基于新消息数量来决定
        """
        try:
            # 获取当前摘要信息
            summary_info = await self._get_summary_info(chat_id)
            
            if not summary_info:
                # 如果没有摘要，检查是否有足够的消息来创建第一个摘要
                total_messages = await self._count_total_messages(chat_id)
                return total_messages >= self.summary_trigger_threshold
            
            # 计算自上次摘要以来的新消息数量
            last_summarized_id = summary_info.get('last_summarized_message_id', 0)
            new_messages_count = await self._count_messages_since(chat_id, last_summarized_id)
            
            return new_messages_count >= self.summary_trigger_threshold
            
        except Exception as e:
            print(f"ERROR: 检查摘要更新条件失败: {e}")
            return False
    
    async def update_chat_summary(self, chat_id: str) -> bool:
        """
        更新指定会话的摘要
        """
        try:
            print(f"INFO: 开始为会话 {chat_id} 更新摘要...")
            
            # 获取现有摘要信息
            summary_info = await self._get_summary_info(chat_id)
            old_summary = summary_info.get('summary_text', '') if summary_info else ''
            last_summarized_id = summary_info.get('last_summarized_message_id', 0) if summary_info else 0
            
            # 获取需要摘要的新消息
            new_messages = await self._get_messages_since(chat_id, last_summarized_id)
            
            if not new_messages:
                print(f"WARN: 会话 {chat_id} 没有新消息需要摘要")
                return False
            
            # 生成新的摘要
            new_summary = await self._generate_summary(old_summary, new_messages)
            
            # 保存摘要到数据库
            latest_message_id = new_messages[-1]['id']
            success = await self._save_summary(chat_id, new_summary, latest_message_id)
            
            if success:
                print(f"SUCCESS: 会话 {chat_id} 的摘要已更新，包含 {len(new_messages)} 条新消息")
                return True
            else:
                print(f"ERROR: 保存会话 {chat_id} 的摘要失败")
                return False
                
        except Exception as e:
            print(f"ERROR: 更新会话 {chat_id} 的摘要失败: {e}")
            return False
    
    async def get_chat_summary(self, chat_id: str) -> Optional[str]:
        """
        获取指定会话的最新摘要
        """
        try:
            summary_info = await self._get_summary_info(chat_id)
            return summary_info.get('summary_text') if summary_info else None
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 的摘要失败: {e}")
            return None
    
    async def _get_summary_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取摘要信息"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("chat_summaries")
                .select("*")
                .eq("chat_id", chat_id)
                .execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"ERROR: 获取摘要信息失败: {e}")
            return None
    
    async def _count_total_messages(self, chat_id: str) -> int:
        """统计会话的总消息数"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("messages")
                .select("id", count="exact")
                .eq("chat_id", chat_id)
                .execute()
            )
            return response.count or 0
        except Exception as e:
            print(f"ERROR: 统计消息数量失败: {e}")
            return 0
    
    async def _count_messages_since(self, chat_id: str, since_message_id: int) -> int:
        """统计自指定消息ID以来的新消息数量"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("messages")
                .select("id", count="exact")
                .eq("chat_id", chat_id)
                .gt("id", since_message_id)
                .execute()
            )
            return response.count or 0
        except Exception as e:
            print(f"ERROR: 统计新消息数量失败: {e}")
            return 0
    
    async def _get_messages_since(self, chat_id: str, since_message_id: int) -> List[Dict[str, Any]]:
        """获取自指定消息ID以来的所有新消息"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("messages")
                .select("*")
                .eq("chat_id", chat_id)
                .gt("id", since_message_id)
                .order("created_at", desc=False)
                .execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取新消息失败: {e}")
            raise e
    
    async def _generate_summary(self, old_summary: str, new_messages: List[Dict[str, Any]]) -> str:
        """
        生成新的摘要
        结合旧摘要和新消息，生成更新后的摘要
        """
        try:
            # 构建摘要提示词
            messages_text = "\n".join([
                f"{'用户' if msg['role'] == 'user' else 'AI'}: {msg['content']}"
                for msg in new_messages
            ])
            
            if old_summary:
                prompt = f"""请基于以下【旧摘要】和【新对话】，生成一个更新后的、更全面的对话摘要。

【旧摘要】：
{old_summary}

【新对话】：
{messages_text}

请生成一个简洁但全面的摘要，包含：
1. 对话的主要话题和发展脉络
2. 重要的事实信息和细节
3. 角色之间的关系变化
4. 任何重要的决定或约定

摘要应该保持在{self.max_summary_length}字符以内，重点突出最重要的信息。"""
            else:
                prompt = f"""请为以下对话生成一个简洁但全面的摘要：

【对话内容】：
{messages_text}

请生成一个摘要，包含：
1. 对话的主要话题
2. 重要的事实信息和细节
3. 角色之间的互动情况
4. 任何重要的决定或约定

摘要应该保持在{self.max_summary_length}字符以内。"""
            
            # 调用LLM生成摘要
            summary = await llm_service.generate_text_response(prompt)
            
            # 确保摘要长度不超过限制
            if len(summary) > self.max_summary_length:
                summary = summary[:self.max_summary_length] + "..."
            
            return summary.strip()
            
        except Exception as e:
            print(f"ERROR: 生成摘要失败: {e}")
            # 不再返回降级摘要，而是将异常向上抛出
            raise e
    
    async def _save_summary(self, chat_id: str, summary_text: str, last_message_id: int) -> bool:
        """保存摘要到数据库"""
        try:
            # 使用upsert操作，如果存在则更新，不存在则插入
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("chat_summaries")
                .upsert({
                    "chat_id": chat_id,
                    "summary_text": summary_text,
                    "last_summarized_message_id": last_message_id,
                    "updated_at": "NOW()"
                })
                .execute()
            )
            return bool(response.data)
        except Exception as e:
            print(f"ERROR: 保存摘要失败: {e}")
            return False


# 全局实例
summarization_service = SummarizationService()