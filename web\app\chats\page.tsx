"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, MessageSquare, Trash2 } from "lucide-react";
import { apiClient } from '@/lib/api';

interface ChatItem {
  chat_id: string;
  display_name: string;
  display_avatar: string;
  latest_message: string;
  created_at: string;
  updated_at: string;
}

const ChatsPage = () => {
  const [chats, setChats] = useState<ChatItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, signInAsGuest, isLoading: isAuthLoading } = useAuth();

  useEffect(() => {
    const fetchChats = async () => {
      if (isAuthLoading) return;
      
      if (!user) {
        await signInAsGuest(); // 确保游客已登录
        return;
      }

      setIsLoading(true);
      setError(null);
      
      try {
        console.log('获取用户聊天列表，用户ID:', user.id);
        const response = await fetch(`http://localhost:8000/api/user-chats?user_id=${user.id}`);
        
        if (!response.ok) {
          throw new Error(`获取聊天列表失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('聊天列表数据:', data);
        
        // 处理返回的数据格式
        const chatList = Array.isArray(data) ? data : (data.chats || []);
        setChats(chatList);
        
      } catch (error) {
        console.error('获取聊天列表失败:', error);
        setError(error instanceof Error ? error.message : '获取聊天列表失败');
        
        // 使用模拟数据作为后备
        const fallbackChats: ChatItem[] = [
          {
            chat_id: "550e8400-e29b-41d4-a716-446655440001",
            display_name: "Lorenzo",
            display_avatar: "/placeholder.svg?height=60&width=60",
            latest_message: "你好，我是Lorenzo。很高兴认识你！",
            created_at: new Date(Date.now() - 300000).toISOString(), // 5分钟前
            updated_at: new Date(Date.now() - 300000).toISOString(),
          },
          {
            chat_id: "550e8400-e29b-41d4-a716-446655440002", 
            display_name: "Rayhan",
            display_avatar: "/placeholder.svg?height=60&width=60",
            latest_message: "作为一个有两个孩子的鳏夫，我很珍惜我们的对话。",
            created_at: new Date(Date.now() - 3600000).toISOString(), // 1小时前
            updated_at: new Date(Date.now() - 1800000).toISOString(), // 30分钟前
          }
        ];
        setChats(fallbackChats);
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchChats();
  }, [user, isAuthLoading, signInAsGuest]);

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return "刚刚";
    if (diffMins < 60) return `${diffMins}分钟前`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}小时前`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}天前`;
  };

  const handleDeleteChat = async (chatId: string) => {
    if (!confirm('确定要删除这个聊天吗？')) return;
    
    try {
      // 这里应该调用删除API
      // await apiClient.deleteChat(chatId);
      setChats(prev => prev.filter(chat => chat.chat_id !== chatId));
    } catch (error) {
      console.error('删除聊天失败:', error);
    }
  };

  if (isLoading || isAuthLoading) {
    return (
      <div className="bg-black min-h-screen text-white">
        <div className="max-w-4xl mx-auto p-6">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-white mr-3" />
            <span className="text-white">正在加载聊天列表...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="max-w-4xl mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-white">你的聊天记录</h1>
          {user && (
            <div className="text-sm text-gray-400">
              {user.display_name === 'Guest User' ? (
                <>
                  <Link href="/sign-up" className="text-blue-400 hover:underline">
                    注册账号
                  </Link>
                  {" "}以永久保存聊天记录
                </>
              ) : (
                `欢迎回来，${user.display_name}`
              )}
            </div>
          )}
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 mb-6">
            <p className="text-red-200 text-sm">{error}</p>
            <p className="text-red-300 text-xs mt-1">正在使用离线模式显示示例数据</p>
          </div>
        )}

        {/* 聊天列表 */}
        {chats.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400 text-lg mb-2">还没有聊天记录</p>
            <p className="text-gray-500 text-sm mb-6">开始与AI角色对话吧！</p>
            <Button asChild className="bg-blue-600 hover:bg-blue-700">
              <Link href="/">浏览角色</Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {chats.map((chat) => (
              <Card key={chat.chat_id} className="bg-gray-900 border-gray-700 overflow-hidden hover:bg-gray-800 transition-colors">
                <CardContent className="p-0">
                  <div className="flex items-center p-4">
                    {/* 角色头像 */}
                    <div className="w-16 h-16 rounded-full overflow-hidden mr-4 flex-shrink-0">
                      <img 
                        src={chat.display_avatar || '/placeholder.svg?height=64&width=64'} 
                        alt={chat.display_name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* 聊天信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="font-semibold text-white truncate">{chat.display_name}</h3>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="bg-gray-700 text-gray-300 text-xs">
                            AI聊天
                          </Badge>
                          <span className="text-xs text-gray-400">
                            {getTimeAgo(chat.updated_at)}
                          </span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-400 truncate mb-2">
                        {chat.latest_message || '开始新的对话...'}
                      </p>
                      
                      {/* 操作按钮 */}
                      <div className="flex items-center justify-between">
                        <Button 
                          size="sm" 
                          className="bg-blue-600 hover:bg-blue-700"
                          asChild
                        >
                          <Link href={`/chat/${chat.chat_id}`}>
                            继续聊天
                          </Link>
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-400 hover:text-red-400 hover:bg-red-900/20"
                          onClick={() => handleDeleteChat(chat.chat_id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        
        {/* 底部提示 */}
        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm">
            想要开始新的对话？{" "}
            <Link href="/" className="text-blue-400 hover:underline">
              浏览更多AI角色
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ChatsPage;