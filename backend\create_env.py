#!/usr/bin/env python3
# 不要随便删除此文件。它用于创建.env文件。
"""
创建.env文件的脚本
"""
import os

def create_env_file():
    """创建.env配置文件"""
    env_content = """# --- Supabase Database ---
SUPABASE_URL="https://aupddjrnljttqxmilymn.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1cGRkanJubGp0dHF4bWlseW1uIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MjQ4NTQsImV4cCI6MjA2ODMwMDg1NH0.vbR6hebyWQZ7YxeGoocR7ceFFRNWZvoONWXvrPz7TP0"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1cGRkanJubGp0dHF4bWlseW1uIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjcyNDg1NCwiZXhwIjoyMDY4MzAwODU0fQ.21vFBEMPfgZA0Mc-x2BsKg7br4h2-_lQE4M70nX2--s"

# --- ImageKit 图片存储服务 ---
IMAGEKIT_ID="uj9lnjyd82"
IMAGEKIT_URL_ENDPOINT="https://ik.imagekit.io/uj9lnjyd82"
IMAGEKIT_PUBLIC_KEY="public_MB//KTinw6AOpfpjxV5f33lXjjE="
IMAGEKIT_PRIVATE_KEY="private_L9NAklnV4lu8sWD56vUI2JYu+XE="

# --- Google Gemini AI (多个API密钥用于轮询) ---
GEMINI_API_KEYS="AIzaSyAHQq4CWqhzdUkK_ZCtvEAbNU-2pKdgSqs,AIzaSyDfIZ8JHtswOGGrEospEXq0G-_lCo2p_Is,AIzaSyCmFX2KUZpEY-_aonLIc0JvMHnnDKpKk24,AIzaSyBV1VtK__aN7ZM1KNnkNXQ5GkAHOVbWk0A,AIzaSyCIf5ZoWt8Pftc24KM-aasS8JUgsj3eCPQ,AIzaSyAI9b917EyA4cyYK-cU2QDK4xmC6bKVJGU,AIzaSyBZv4OxeYu57d0AFUqGmkQKe3rq1WL5iK0,AIzaSyBI6XTacDeodfJj0W-pbDTmzSMhSpuChBw,AIzaSyBJmxwFr7JY-YxQgD2jYKFTDQfSVwmAF9U,AIzaSyDDV8-NOXH9u9mcHzPQl6FBIdDblHQYDwU"

# --- Gemini 模型配置 ---
GEMINI_CHAT_MODEL="gemini-2.5-flash"
GEMINI_GEN_STORY_MODEL="gemini-2.5-pro"
GEMINI_GEN_ROLE_MODEL="gemini-2.5-pro"
GEMINI_GEN_IMAGE_MODEL="gemini-2.0-flash-preview-image-generation"
GEMINI_CHECK_IMAGE_MODEL="gemini-2.5-flash"
GEMINI_CHECK_TASK_MODEL="gemini-2.5-flash"
GEMINI_TTS_MODEL="gemini-2.5-flash-preview-tts"
GEMINI_EMBEDDING_MODEL="gemini-embedding-001"

# --- DOUBAO API KEYS---
DOUBAO_API_KEYS="cdd4f8ed-c1dd-44c3-b49a-39731062e198"

# --- Doubao 模型配置 ---
DOUBAO_GEN_IMAGE_MODEL="doubao-seedream-3-0-t2i-250415"
DOUBAO_EDIT_IMAGE_MODEL="doubao-seededit-3-0-i2i-250628"

# --- Python 进程配置 ---
PYTHON_PATH="python"
"""

    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env文件创建成功！")
    print(f"📁 位置: {os.path.abspath('.env')}")
    print("🗄️ 已配置Supabase数据库连接:")
    print("   - URL: https://aupddjrnljttqxmilymn.supabase.co")
    print("   - 匿名密钥和服务密钥已配置")
    print("🖼️ 已配置ImageKit图片存储服务:")
    print("   - URL: https://ik.imagekit.io/uj9lnjyd82")
    print("   - 公钥和私钥已配置")
    print("🔑 已配置5个Gemini API密钥用于轮询")
    print("🤖 已配置最新的Gemini模型:")
    print(f"   - 聊天模型: {os.getenv('GEMINI_CHAT_MODEL', 'gemini-2.5-flash')}")
    print(f"   - 故事生成模型: {os.getenv('GEMINI_GEN_STORY_MODEL', 'gemini-2.5-pro')}")
    print(f"   - 角色生成模型: {os.getenv('GEMINI_GEN_ROLE_MODEL', 'gemini-2.5-pro')}")
    print(f"   - 图像生成模型: {os.getenv('GEMINI_GEN_IMAGE_MODEL', 'gemini-2.0-flash-preview-image-generation')}")
    print(f"   - 图像审核模型: {os.getenv('GEMINI_CHECK_IMAGE_MODEL', 'gemini-2.5-flash')}")
    print(f"   - 任务检查模型: {os.getenv('GEMINI_CHECK_TASK_MODEL', 'gemini-2.5-flash')}")
    print(f"   - TTS模型: {os.getenv('GEMINI_TTS_MODEL', 'gemini-2.5-flash-preview-tts')}")
    print(f"   - Embedding模型: {os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-004')}")

if __name__ == "__main__":
    create_env_file()