import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar } from "@/components/ui/avatar"
import { Ban } from "lucide-react"

// 修复：使用UUID格式的模拟数据
const blockedCreators = [
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "SpamBot9000",
    avatar: "🤖",
    color: "from-red-500 to-pink-500",
    blockedDate: "2023-07-15",
  },
  {
    id: "550e8400-e29b-41d4-a716-************",
    name: "ToxicCreator",
    avatar: "☣️",
    color: "from-orange-500 to-amber-500",
    blockedDate: "2023-08-22",
  },
]

export default function BlockedCreatorsPage() {
  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Blocked Creators</h1>
      </div>

      {blockedCreators.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5 text-red-500" /> Blocked Creators
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {blockedCreators.map((creator) => (
                <div key={creator.id} className="flex items-center justify-between p-4 rounded-lg bg-accent/50">
                  <div className="flex items-center gap-4">
                    <Avatar>
                      <div
                        className={`w-10 h-10 rounded-full bg-gradient-to-r ${creator.color} flex items-center justify-center text-white text-xl`}
                      >
                        {creator.avatar}
                      </div>
                    </Avatar>
                    <div>
                      <div className="font-medium">{creator.name}</div>
                      <div className="text-sm text-muted-foreground">
                        Blocked on {new Date(creator.blockedDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <Button variant="outline">Unblock</Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="flex flex-col items-center justify-center p-12 text-center">
          <Ban className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">No Blocked Creators</h2>
          <p className="text-muted-foreground max-w-md">
            You haven&apos;t blocked any creators yet. When you block a creator, they will appear here.
          </p>
        </div>
      )}
    </div>
  )
}
