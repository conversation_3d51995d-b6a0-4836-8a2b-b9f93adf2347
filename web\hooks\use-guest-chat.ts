"use client";

import { useState, useEffect } from 'react';

interface GuestMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  username?: string;
}

interface GuestChat {
  id: string;
  character: string;
  messages: GuestMessage[];
  lastActivity: Date;
}

export function useGuestChat() {
  const [guestChats, setGuestChats] = useState<GuestChat[]>([]);

  useEffect(() => {
    // 从localStorage加载游客聊天记录
    const loadGuestChats = () => {
      try {
        const stored = localStorage.getItem('guest_chats');
        if (stored) {
          const parsed = JSON.parse(stored);
          // 转换日期字符串回Date对象
          const chats = parsed.map((chat: any) => ({
            ...chat,
            lastActivity: new Date(chat.lastActivity),
            messages: chat.messages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }))
          }));
          setGuestChats(chats);
        }
      } catch (error) {
        console.error('加载游客聊天记录失败:', error);
      }
    };

    loadGuestChats();
  }, []);

  const saveGuestChat = (chat: GuestChat) => {
    try {
      const updatedChats = [...guestChats.filter(c => c.id !== chat.id), chat];
      setGuestChats(updatedChats);
      localStorage.setItem('guest_chats', JSON.stringify(updatedChats));
    } catch (error) {
      console.error('保存游客聊天记录失败:', error);
    }
  };

  const deleteGuestChat = (chatId: string) => {
    try {
      const updatedChats = guestChats.filter(c => c.id !== chatId);
      setGuestChats(updatedChats);
      localStorage.setItem('guest_chats', JSON.stringify(updatedChats));
    } catch (error) {
      console.error('删除游客聊天记录失败:', error);
    }
  };

  const addMessageToChat = (chatId: string, message: GuestMessage) => {
    try {
      const updatedChats = guestChats.map(chat => {
        if (chat.id === chatId) {
          return {
            ...chat,
            messages: [...chat.messages, message],
            lastActivity: new Date()
          };
        }
        return chat;
      });
      setGuestChats(updatedChats);
      localStorage.setItem('guest_chats', JSON.stringify(updatedChats));
    } catch (error) {
      console.error('添加消息到聊天记录失败:', error);
    }
  };

  return {
    guestChats,
    saveGuestChat,
    deleteGuestChat,
    addMessageToChat
  };
}