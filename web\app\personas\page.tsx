"use client"

import { AuthGuard } from "@/components/auth-guard"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { User, Search, Plus } from "lucide-react"
import Link from "next/link"

export default function PersonasPage() {
  return (
    <AuthGuard requireAuth={true}>
      <div className="bg-black min-h-screen text-white">
        <div className="max-w-4xl mx-auto p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">My Personas</h1>
            <Button className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New
            </Button>
          </div>

          <div className="relative mb-8 max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search"
              className="pl-9 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400"
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <Link href="/persona/tommick839">
              <Card className="cursor-pointer hover:bg-gray-800 transition-colors bg-gray-900 border-gray-700">
                <CardContent className="p-4 text-center">
                  <div className="w-full h-32 bg-gray-800 rounded-lg flex items-center justify-center mb-3 relative">
                    <User className="h-8 w-8 text-gray-600" />
                    <div className="absolute top-2 left-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div className="absolute top-2 right-2 w-4 h-4 bg-gray-600 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-400">⋯</span>
                    </div>
                  </div>
                  <h3 className="font-medium text-sm text-white">tommick839</h3>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
