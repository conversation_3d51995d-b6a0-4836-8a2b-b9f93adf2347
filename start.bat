@echo off
chcp 65001 > nul

title 星恋AI - 开发环境启动器

echo.
echo [INFO] =======================================================
echo [INFO] ==      XingLian AI - Full-Stack Launcher            ==
echo [INFO] =======================================================
echo.

echo [STEP 1/4] 检查项目环境...

if not exist "%~dp0backend\venv\Scripts\python.exe" (
    echo [ERROR] 后端虚拟环境 'backend\venv' 未找到或已损坏!
    echo [ERROR] 请先成功运行一次 setup.bat 来完成初始化设置。
    goto :error
)
echo [SUCCESS] 后端环境检查通过。

set "PYTHON_EXE=%~dp0backend\venv\Scripts\python.exe"

echo.
echo [STEP 2/4] 确保测试用户存在...
cd /d "%~dp0"
"%PYTHON_EXE%" "backend\scripts\ensure_test_user.py"
echo.

echo [INFO] 检查 Node.js 和前端环境...
where node >nul 2>nul
if errorlevel 1 (
    echo [WARNING] Node.js 未找到或未正确配置！
    echo [WARNING] 将跳过前端启动，仅启动后端服务器。
    echo [INFO] 如需启动前端，请：
    echo [INFO] 1. 安装 Node.js: https://nodejs.org/
    echo [INFO] 2. 在 web 目录运行: npm install
    echo [INFO] 3. 重新运行此脚本
    set "SKIP_FRONTEND=1"
) else (
    if not exist "%~dp0web\node_modules" (
        echo [WARNING] 前端依赖未安装！
        echo [WARNING] 将跳过前端启动，仅启动后端服务器。
        echo [INFO] 请先在 web 目录运行: npm install
        set "SKIP_FRONTEND=1"
    ) else (
        echo [SUCCESS] 前端环境检查通过。
        set "SKIP_FRONTEND=0"
    )
)
echo.

echo [STEP 3/4] 在新窗口中启动后端服务器...
echo [INFO] 后端日志将在一个名为 'BACKEND SERVER' 的新窗口中显示。
START "BACKEND SERVER" cmd /k "cd /d "%~dp0backend" & "%PYTHON_EXE%" -m uvicorn src.supabase_main:app --host 0.0.0.0 --port 8000 --reload"
echo [SUCCESS] 后端服务器正在启动...
echo.

if "%SKIP_FRONTEND%"=="1" (
    echo [STEP 4/4] 跳过前端启动...
    echo [INFO] 仅启动后端服务器，前端需要手动启动。
    echo.
    echo [COMPLETE] =====================================================
    echo [COMPLETE] ==       后端服务已启动！                         ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==   - 后端服务运行于: http://127.0.0.1:8000       ==
    echo [COMPLETE] ==   - 前端需要安装 Node.js 后手动启动             ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==       可以关闭此启动器窗口。                     ==
    echo [COMPLETE] =====================================================
    echo.
) else (
    echo [STEP 4/4] 在新窗口中启动前端开发服务器...
    echo [INFO] 前端日志将在一个名为 'FRONTEND SERVER' 的新窗口中显示。
    echo [INFO] Next.js 首次编译可能需要一些时间，请耐心等待...
    START "FRONTEND SERVER" cmd /k "cd /d "%~dp0web" & npm run dev"
    echo [SUCCESS] 前端服务器正在启动...
    echo.
    echo.
    echo [COMPLETE] =====================================================
    echo [COMPLETE] ==       所有服务均已在独立窗口中启动！         ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==   - 后端服务运行于: http://127.0.0.1:8000       ==
    echo [COMPLETE] ==   - 前端服务运行于: http://localhost:3000       ==
    echo [COMPLETE] ==                                                 ==
    echo [COMPLETE] ==       可以关闭此启动器窗口。                     ==
    echo [COMPLETE] =====================================================
    echo.
)

:success
echo.
echo ------------------------------------------------------------------
echo [INFO] 服务器已正常停止。
goto :end

:error
echo.
echo ------------------------------------------------------------------
echo [FATAL] 脚本执行过程中发生严重错误！
echo [FATAL] 请检查上面的错误日志。

:end
echo.
pause