#!/usr/bin/env python3
"""
批量角色卡导入工具 - V3.0 (重构版)
从/role/目录下的所有PNG文件中解析TavernAI角色卡，
使用 <EMAIL> 用户身份，将角色作为公开角色导入。
"""

import os
import sys
import base64
import json
import asyncio
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List

from PIL import Image, PngImagePlugin
from dotenv import load_dotenv
from supabase import create_client, Client

# --- 核心设置 ---
# 添加backend目录到Python路径，以便导入后端服务
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))
load_dotenv(backend_dir / '.env')

# 导入后端服务
# V5.0 统一服务入口
from src.services.llm_service import llm_service
from src.services import supabase_service, simple_imagekit_service

# --- 全局配置 ---
ROLE_DIR = Path(__file__).parent  # /role/ 目录
META_USER_EMAIL = "<EMAIL>"
META_USER_PASSWORD = "password123" # 默认密码

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)


async def get_or_create_metarole_user() -> Optional[str]:
    """
    获取或创建 <EMAIL> 用户，并返回用户ID。
    如果用户已存在，直接返回其ID。
    """
    email = META_USER_EMAIL
    password = META_USER_PASSWORD
    
    try:
        # 检查用户是否存在
        response = await asyncio.to_thread(lambda: supabase_admin_client.auth.admin.list_users())
        users_list = getattr(response, 'users', response)
        existing_user = next((u for u in users_list if u.email == email), None)
        if existing_user:
            print(f"✅ 用户 '{email}' 已存在。")
            return str(existing_user.id)
    except Exception as e:
        print(f"⚠️ 检查用户时发生警告: {e}。")

    # 创建用户
    try:
        print(f"🔄 正在创建新用户 '{email}'...")
        user_response = await asyncio.to_thread(
            lambda: supabase_admin_client.auth.admin.create_user({
                "email": email, "password": password, "email_confirm": True, "user_metadata": {"display_name": "Meta Role Importer"}
            })
        )
        user_id = str(user_response.user.id)
        await asyncio.sleep(1.5) # 等待profile触发器完成
        
        # 更新profile
        profile_update_data = {'display_name': 'Meta Role Importer'}
        await asyncio.to_thread(
            lambda: supabase_admin_client.table('user_profiles').update(profile_update_data).eq('user_id', user_id).execute()
        )
        print(f"✅ 成功创建用户 '{email}'。")
        return user_id
    except Exception as e:
        if 'User already registered' in str(e):
            print(f"ℹ️ 用户 '{email}' 已注册，正在重新获取...")
            response = await asyncio.to_thread(lambda: supabase_admin_client.auth.admin.list_users())
            users_list = getattr(response, 'users', response)
            existing_user = next((u for u in users_list if u.email == email), None)
            if existing_user:
                return str(existing_user.id)
        
        print(f"❌ 创建或获取用户 '{email}' 失败: {e}")
        return None


class CharacterCardImporter:
    """角色卡批量导入器"""

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.success_count = 0
        self.error_count = 0
        self.errors: List[Dict[str, Any]] = []

    def extract_character_data_from_png(self, image_path: Path) -> Optional[Dict[str, Any]]:
        """从PNG文件中提取角色卡数据"""
        try:
            with Image.open(image_path) as img:
                if not isinstance(img, PngImagePlugin.PngImageFile):
                    print(f"⚠️  文件不是有效的PNG: {image_path.name}")
                    return None
                
                text_meta = img.text or {}
                if 'chara' in text_meta:
                    b64_data = text_meta['chara']
                    decoded = base64.b64decode(b64_data).decode('utf-8')
                    return json.loads(decoded)
                else:
                    print(f"⚠️  PNG文件中未找到'chara'数据: {image_path.name}")
                    return None
        except Exception as e:
            print(f"❌ 解析角色卡数据失败 {image_path.name}: {e}")
            return None

    # --- ▼▼▼ 核心修改区域：注入了新的清洗提示词 ▼▼▼ ---
    async def translate_and_refine_character_data(self, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM翻译、清洗和整理角色数据。"""
        print("  - 正在使用AI进行翻译、清洗和内容整理...")
        
        original_data_json = json.dumps(original_data, ensure_ascii=False, indent=2)
        
        prompt = f"""你是一名顶级的游戏角色本地化专家和内容审核员。你的任务是接收一个JSON格式的TavernAI角色卡数据，并根据以下【核心规则】将其转换为符合规范的、干净的中文版本。

**【核心规则】**
1.  **HTML标签清洗 (最重要！)**:
    *   你必须彻底清除所有文本字段中的全部HTML标签 (例如 `<p>`, `<strong>`, `<span>`, `<b>`, `<br>`)。
    *   最终输出的JSON中，所有字段的值都必须是纯文本，不包含任何HTML代码。
    *   例如：如果输入是 `<p>你好, <strong>世界</strong></p>`，输出必须是 `你好, 世界`。

2.  **内容翻译**:
    *   将所有文本字段（特别是 name, description, personality, scenario, first_mes, mes_example, system_prompt）翻译成符合角色性格的、自然的简体中文。
    *   角色姓名（'name'字段）需要翻译成符合其性格特征的中文名称、昵称或音译。

3.  **内容格式与多样性重塑**:
    *   **system_prompt**: 重写此字段，使其包含以下多样性回复指令： "1. 你的回复必须具备高度的多样性。 2. 你的回复可以自由组合或侧重于：内心独白、环境观察、动作、直接对话。所有非对话部分都用英文半角括号 () 包裹。 3. 不要总是使用“文字(动作)文字”的固定格式，有时纯动作或独白就足够了。"
    *   **mes_example**: 基于原始示例的意图，重写此字段以展示多种回复风格。所有非对话部分都必须用英文半角括号 () 包裹。例如：
        - {{{{user}}}}: 你在想什么？\\n{{{{char}}}}: (他为什么会知道这件事？难道是……) '你从哪听说的？'
        - {{{{user}}}}: 我们下一步怎么办？\\n{{{{char}}}}: (他沉默地站起身，走到窗边，冰冷的夜风吹动了他的衣角。)
    *   **first_mes**: 保持 `文字(动作)文字` 格式，确保括号内有丰富的非语言描述。

4.  **内容合规性修改**:
    *   仔细审查所有文本内容，删除或修改任何涉及"色情/暴力/血腥/政治"的敏感内容，并用合规但能体现性格的内容创意性地替换。

**任务要求**:
请处理以下JSON数据，并返回一个结构完全相同、但内容经过清洗、翻译和重塑的JSON对象。不要添加任何额外的解释或注释，只返回JSON。

**原始角色数据:**
```json
{original_data_json}
```

**处理后的中文角色数据 (JSON格式):**"""

        try:
            # 使用统一的LLM服务和指定的模型
            refined_data = await llm_service.generate_json(
                prompt,
                model_name=os.getenv("GEMINI_CHAT_MODEL", "gemini-2.5-flash")
            )
            print("  - ✅ AI内容处理成功。")
            return refined_data
        except Exception as e:
            print(f"  - ❌ AI内容处理失败: {e}。将使用原始数据继续导入。")
            return original_data
    # --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---

    async def upload_image_to_imagekit(self, image_path: Path, character_name: str) -> Optional[str]:
        """上传图片到ImageKit并返回URL"""
        try:
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            base64_data = base64.b64encode(image_bytes).decode('utf-8')
            
            safe_name = "".join(c for c in character_name if c.isalnum() or c in (' ', '-', '_')).rstrip().replace(' ', '_')
            file_name = f"{safe_name}_{uuid.uuid4().hex[:6]}.png"

            upload_result = await simple_imagekit_service.upload_image_from_base64(
                base64_data=base64_data,
                file_name=file_name,
                folder="/xinglian/agent/",
                tags=["character_card", "imported"]
            )

            if upload_result["success"]:
                return upload_result.get("url")
            else:
                print(f"❌ 图片上传失败: {upload_result.get('error')}")
                return None
        except Exception as e:
            print(f"❌ 上传图片时发生错误: {e}")
            return None

    async def import_character_to_database(self, character_data: Dict[str, Any], image_url: Optional[str]) -> Optional[Dict[str, Any]]:
        """将角色数据导入到数据库"""
        try:
            # 确保角色是公开的
            character_data['is_public'] = True
            character_data['avatar_url'] = image_url # 确保头像URL也被设置

            # 兼容旧版和新版字段：将 'first_mes' 或 'opening_line' 统一到 'first_mes'
            if 'first_mes' not in character_data and 'opening_line' in character_data:
                character_data['first_mes'] = character_data.pop('opening_line')

            # 使用supabase_service导入角色卡
            new_agent = await supabase_service.create_agent_from_character_card(
                user_id=self.user_id,
                character_data=character_data,
                image_url=image_url,
                spec=character_data.get('spec', 'chara_card_v1'),
                spec_version=character_data.get('spec_version', '1.0')
            )
            return new_agent
        except Exception as e:
            print(f"❌ 导入数据库失败: {e}")
            return None

    async def process_single_character(self, image_path: Path):
        """处理单个角色卡文件"""
        print(f"\n🔄 处理文件: {image_path.name}")
        
        # 1. 提取数据
        character_data = self.extract_character_data_from_png(image_path)
        if not character_data:
            self.error_count += 1
            self.errors.append({'file': image_path.name, 'error': '无法提取角色卡数据'})
            return

        # v2格式兼容
        if 'spec' in character_data and 'data' in character_data:
            character_data = character_data['data']

        # 调用AI进行翻译和清洗
        refined_data = await self.translate_and_refine_character_data(character_data)

        character_name = refined_data.get('name', 'Unknown')
        if character_name == 'Unknown' or not character_name.strip():
            print(f"❌ 文件 {image_path.name} 缺少角色名称，跳过。")
            self.error_count += 1
            self.errors.append({'file': image_path.name, 'error': '角色名称为空'})
            return
            
        print(f"  - 角色名称 (处理后): {character_name}")

        # 2. 上传图片到ImageKit
        print(f"  - 上传图片...")
        image_url = await self.upload_image_to_imagekit(image_path, character_name)
        if not image_url:
            print(f"  - ⚠️  图片上传失败，将不带图片继续导入。")

        # 3. 导入数据库（使用整理后的数据）
        print(f"  - 导入数据库...")
        new_agent = await self.import_character_to_database(refined_data, image_url)
        if not new_agent:
            self.error_count += 1
            self.errors.append({'file': image_path.name, 'character': character_name, 'error': '数据库导入失败'})
            return

        self.success_count += 1
        print(f"  - ✅ 成功导入角色: {character_name} (ID: {new_agent['id']})")


    async def batch_import(self):
        """批量导入所有角色卡"""
        print("🚀 开始批量导入角色卡...")
        print(f"📁 扫描目录: {ROLE_DIR}")

        png_files = list(ROLE_DIR.glob("*.png"))
        if not png_files:
            print("❌ 未找到任何PNG文件。")
            return

        print(f"📊 找到 {len(png_files)} 个PNG文件。")

        for image_path in png_files:
            await self.process_single_character(image_path)

        # 报告结果
        total = len(png_files)
        print("\n" + "="*60)
        print("📈 导入完成!")
        print(f"  - 总文件数: {total}")
        print(f"  - 成功导入: {self.success_count}")
        print(f"  - 失败数量: {self.error_count}")

        if self.errors:
            print("\n❌ 错误详情:")
            for error in self.errors:
                print(f"  - 文件: {error.get('file', 'N/A')}, 角色: {error.get('character', 'N/A')}, 错误: {error.get('error')}")
        print("="*60)


async def main():
    """主函数"""
    print("=" * 60)
    print("🌟 星恋AI - 批量角色卡导入工具 V3.0")
    print("=" * 60)

    # 1. 检查服务连接
    print("🔍 检查服务连接...")
    try:
        if (await supabase_service.health_check())["status"] != "healthy":
            print("❌ Supabase数据库连接失败。")
            return
        print("✅ Supabase数据库连接正常。")
        
        if not (await simple_imagekit_service.health_check())["success"]:
            print("❌ ImageKit服务连接失败。")
            return
        print("✅ ImageKit服务连接正常。")
    except Exception as e:
        print(f"❌ 服务连接检查失败: {e}")
        return

    # 2. 获取或创建导入专用用户
    print("\n🔍 准备导入用户...")
    user_id = await get_or_create_metarole_user()
    if not user_id:
        print("❌ 无法获取或创建导入所需的用户。脚本终止。")
        return
    print(f"✅ 使用用户ID: {user_id}")

    # 3. 开始导入
    importer = CharacterCardImporter(user_id=user_id)
    await importer.batch_import()

    print("\n🎉 脚本执行完毕。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ 用户中断导入。")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
