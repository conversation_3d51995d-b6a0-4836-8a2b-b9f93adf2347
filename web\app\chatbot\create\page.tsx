"use client"

import { AuthGuard } from "@/components/auth-guard"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import Link from "next/link"

export default function CreateChatbotPage() {
  const [name, setName] = useState("Elara Nightshade")
  const [title, setTitle] = useState("I am a renowned archeologist")
  const [greeting, setGreeting] = useState(
    "You've found me at an interesting time. Ancient texts whisper, and the ruins call. Shall we uncover what lies beneath? Together, we might discover something remarkable.",
  )
  const [personality, setPersonality] = useState(
    "A renowned archaeologist with a mysterious past, <PERSON><PERSON> has spent decades exploring forgotten temples and ancient ruins across the world. With her signature silver-streaked black hair and penetrating amber eyes, she commands respect in academic circles while maintaining an air of enigma. Rumored to possess knowledge of arcane artifacts that defy conventional science, <PERSON><PERSON> is",
  )
  const [visibility, setVisibility] = useState("private")
  const [definitionVisibility, setDefinitionVisibility] = useState("private")
  const [tags, setTags] = useState("")
  const [agreedToGuidelines, setAgreedToGuidelines] = useState(false)

  return (
    <AuthGuard requireAuth={true}>
      <div className="bg-black min-h-screen text-white">
        {/* Hero Banner */}
        <div className="bg-gradient-to-r from-purple-900 to-blue-900 p-8 mb-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-4">Unleash Your Creativity:</h1>
            <h2 className="text-3xl font-bold mb-4">Create Chatbot Characters</h2>
            <p className="text-lg text-gray-300 mb-6">
              Create and personalize AI chatbot characters to suit your deepest fantasies, bringing unique, uncensored
              personalities to life. Sign Up Free to Create Characters.
            </p>
            <Button className="bg-blue-600 hover:bg-blue-700">Create Free Account</Button>
          </div>
        </div>

        <div className="max-w-4xl mx-auto p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Create Your Own Chatbot</h1>
            <Link href="#" className="text-blue-400 hover:underline text-sm">
              Read our Chatbot Creation guide
            </Link>
          </div>

          <div className="space-y-6">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-white">
                Name <span className="text-muted-foreground">The name can be seen and last names.</span>
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>

            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title" className="text-white">
                Title{" "}
                <span className="text-muted-foreground">Short sentence describing your chatbot, for display only.</span>
              </Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>

            {/* Greeting */}
            <div className="space-y-2">
              <Label htmlFor="greeting" className="text-white">
                Greeting <span className="text-muted-foreground">What will they say to start a conversation.</span>
              </Label>
              <Textarea
                id="greeting"
                value={greeting}
                onChange={(e) => setGreeting(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white min-h-[80px]"
              />
            </div>

            {/* Chatbot's Personality */}
            <div className="space-y-2">
              <Label htmlFor="personality" className="text-white">
                Chatbot's Personality{" "}
                <span className="text-muted-foreground">In a few sentences, describe your chatbot's personality.</span>
              </Label>
              <Textarea
                id="personality"
                value={personality}
                onChange={(e) => setPersonality(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white min-h-[120px]"
              />
            </div>

            {/* Visibility */}
            <div className="space-y-3">
              <Label className="text-white">
                Visibility <span className="text-muted-foreground">Who can see and talk to your chatbot.</span>
              </Label>
              <RadioGroup value={visibility} onValueChange={setVisibility}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="public" id="public" />
                  <Label htmlFor="public" className="text-white">
                    Public: Anyone can chat
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="unlisted" id="unlisted" />
                  <Label htmlFor="unlisted" className="text-white">
                    Unlisted: Anyone with the link
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="private" id="private" />
                  <Label htmlFor="private" className="text-white">
                    Private: Only you can chat
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Avatar */}
            <div className="space-y-3">
              <Label className="text-white">
                Avatar <span className="text-muted-foreground">Choose an avatar for your chatbot.</span>
              </Label>
              <div className="flex gap-2">
                <Button className="bg-blue-600 hover:bg-blue-700">Generate Avatar</Button>
                <span className="text-muted-foreground self-center">or</span>
                <Button variant="outline" className="border-gray-600 text-white hover:bg-gray-800 bg-transparent">
                  Choose File
                </Button>
                <Button variant="outline" className="text-muted-foreground bg-transparent border-gray-600">
                  No File Chosen
                </Button>
              </div>
            </div>

            {/* Agreement */}
            <div className="flex items-center space-x-2">
              <Checkbox id="guidelines" checked={agreedToGuidelines} onCheckedChange={setAgreedToGuidelines} />
              <Label htmlFor="guidelines" className="text-sm text-white">
                I have read and agree with the{" "}
                <Link href="#" className="text-blue-400 hover:underline">
                  Community Guidelines.
                </Link>
              </Label>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-6">
              <Button variant="outline" className="border-gray-600 text-white hover:bg-gray-800 bg-transparent">
                Cancel
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700">Save as draft</Button>
              <Button variant="secondary" className="bg-gray-600 hover:bg-gray-700">
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
