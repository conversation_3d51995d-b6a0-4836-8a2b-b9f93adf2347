import { AuthGuard } from "@/components/auth-guard"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search } from "lucide-react"

export default function MyChatbotsPage() {
  return (
    <AuthGuard requireAuth={true}>
      <div className="bg-black min-h-screen text-white p-6">
        <h1 className="text-2xl font-bold mb-6">My Chatbots</h1>

        <div className="flex items-center gap-4 mb-8">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search" className="pl-9 bg-gray-800 border-gray-700 text-white" />
          </div>

          <Select defaultValue="all">
            <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="public">Public</SelectItem>
              <SelectItem value="private">Private</SelectItem>
            </SelectContent>
          </Select>

          <Select defaultValue="latest">
            <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              <SelectItem value="latest">Latest</SelectItem>
              <SelectItem value="oldest">Oldest</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Empty State */}
        <div className="flex flex-col items-center justify-center py-20 text-center">
          <h2 className="text-xl font-medium mb-2 text-white">No Results Found</h2>
          <p className="text-muted-foreground mb-6">Create your first bot and start chatting!</p>
          <Button className="bg-blue-600 hover:bg-blue-700">Create Chatbot</Button>
        </div>
      </div>
    </AuthGuard>
  )
}
