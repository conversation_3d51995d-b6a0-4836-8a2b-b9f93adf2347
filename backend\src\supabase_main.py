#!/usr/bin/env python3
"""
星恋 AI 后端服务 - V5.0 统一消息流重构版
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import asyncio
import uuid
import json
import base64
import traceback
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional

import os
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Query, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from PIL import Image
import io

from src.services import (
    llm_service,
    supabase_service,
    simple_imagekit_service,
    prompt_assembler,
    summarization_service,
    chat_service
)
from src.prompt_templates import STREAMING_ROLEPLAY_CHAT_PROMPT
from src.pydantic_models import CharacterCardImportRequest, ImportedAgentResponse, CharacterCardV2, TavernAICharacterCard

# 全局服务实例已通过导入获得
# llm_service 已从 src.services.llm_service 导入

# 全局并发锁已迁移到 chat_service.py

# regenerate_user_choices 函数已迁移到 chat_service.py


# --- Lifespan Manager ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("INFO: 启动 星恋AI FastAPI服务器 (V5.0 统一消息流)")
    print("=" * 50)
    health = await supabase_service.health_check()
    if health["status"] != "healthy":
        print(f"FATAL: Supabase数据库连接失败: {health.get('error')}")
    else:
        print("INFO: Supabase数据库连接成功。")
    print("INFO: 所有服务启动完成")
    yield
    print("INFO: FastAPI服务器已关闭")

# --- App 初始化 ---
app = FastAPI(title="Xingye Backend - Unified Message Stream", version="5.0.0", lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===================================================================
# API 端点 (V5 重构)
# ===================================================================

@app.get("/")
async def root():
    return {"message": "星恋 AI 后端服务运行中 - V5.0 统一消息流"}

@app.get("/health")
async def health_check():
    health = await supabase_service.health_check()
    return {
        "status": "healthy" if health["status"] == "healthy" else "unhealthy",
        "details": health
    }

# --- 新增：游客登录接口 (V4 - 缓存优先版) ---
class GuestLoginRequest(BaseModel):
    guest_id: str

@app.post("/api/auth/guest-login", response_model=Dict[str, Any])
async def guest_login(request: GuestLoginRequest):
    """游客登录接口 - V2 优化版"""
    guest_email = f"guest_{request.guest_id}@xinglian.app"
    # 使用基于 guest_id 的固定密码，确保幂等性
    guest_password = f"guest_password_{request.guest_id}" 

    try:
        # 步骤 1: 尝试直接登录
        print(f"INFO: [Guest] 尝试为游客 '{guest_email}' 直接登录...")
        session_response = await asyncio.to_thread(
            lambda: supabase_service.supabase.auth.sign_in_with_password({
                "email": guest_email,
                "password": guest_password
            })
        )
        print(f"INFO: [Guest] 游客 '{guest_email}' 登录成功。")
        return session_response.model_dump()

    except Exception as login_error:
        # 如果登录失败，很可能是用户不存在
        print(f"INFO: [Guest] 直接登录失败: {str(login_error)[:100]}... 尝试创建新游客账户。")
        try:
            # 步骤 2: 创建新用户
            await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.admin.create_user({
                    "email": guest_email,
                    "password": guest_password,
                    "email_confirm": True,
                    "user_metadata": { "display_name": f"游客_{request.guest_id[-6:]}" }
                })
            )
            print(f"INFO: [Guest] 游客账户 '{guest_email}' 创建成功。")

            # 步骤 3: 再次尝试登录
            # 此时用户必定存在，登录应该会成功
            session_response_after_create = await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.sign_in_with_password({
                    "email": guest_email,
                    "password": guest_password
                })
            )
            print(f"INFO: [Guest] 新游客账户登录成功。")
            return session_response_after_create.model_dump()

        except Exception as e:
            # 如果在创建或再次登录时发生错误
            print(f"ERROR: [Guest] 游客登录/创建流程最终失败: {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"游客登录/创建失败: {str(e)}")

# --- 对话创建 (入口) API ---

@app.post("/api/chats/start-with-agent/{agent_id}", response_model=Dict[str, str])
async def start_chat_with_agent(agent_id: str, user_id: str = Query(..., description="发起聊天的用户ID")):
    """开始与指定Agent的聊天会话 - 重构版本，业务逻辑委托给chat_service"""
    try:
        chat_id = await chat_service.start_chat_with_agent(user_id, agent_id)
        return {"chat_id": chat_id}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@app.post("/api/chats/start-story/{story_id}", response_model=Dict[str, str])
async def start_story_chat(story_id: str, user_id: str = Query(..., description="发起故事的用户ID")):
    try:
        existing_chat = await supabase_service.get_chat_by_user_and_story(user_id, story_id)
        if existing_chat:
            print(f"INFO: Found existing chat {existing_chat['id']} for user {user_id} and story {story_id}.")
            return {"chat_id": existing_chat["id"]}

        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")
        
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters")
        first_chapter = chapters[0]

        opening_sequence = first_chapter.get("opening_sequence") or []
        
        agent_ids = list(set(
            element.get("agent_id") or element.get("character_id")
            for element in opening_sequence
            if element.get("agent_id") or element.get("character_id")
        ))

        print(f"--- AGENT_IDS DEBUG ---")
        print(f"Opening sequence length: {len(opening_sequence)}")
        print(f"Extracted agent_ids: {agent_ids}")
        for i, element in enumerate(opening_sequence[:3]):
            agent_id = element.get('agent_id') or element.get('character_id')
            print(f"  Element {i}: type={element.get('element_type')}, agent_id={agent_id}")
        print(f"----------------------")

        if not agent_ids:
            print(f"WARN: Story {story_id} chapter 1 has no characters in opening sequence.")
            agents = await supabase_service.get_agents(is_public=True, limit=2)
            agent_ids = [a['id'] for a in agents]

        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=agent_ids, story_id=story_id)
        if not chat_id:
            raise HTTPException(status_code=500, detail="Failed to create chat session for story")

        return {"chat_id": chat_id}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@app.post("/api/chats/{chat_id}/touch")
async def touch_chat_session(chat_id: str):
    updated = await supabase_service.touch_chat(chat_id)
    if updated:
        return {"status": "ok"}
    raise HTTPException(status_code=404, detail="Chat not found or failed to update")

# --- 数据获取 API ---

@app.get("/api/chats/{chat_id}/messages", response_model=List[Dict])
async def get_chat_messages(chat_id: str, limit: int = 50, offset: int = 0):
    messages = await supabase_service.get_messages_by_chat_id(chat_id, limit, offset)
    return messages

@app.get("/api/chats/{chat_id}/details", response_model=Dict)
async def get_chat_details(chat_id: str):
    chat_details = await supabase_service.get_chat_by_id(chat_id)
    if not chat_details:
        raise HTTPException(status_code=404, detail="Chat session not found")
    return chat_details

@app.get("/api/chats/{chat_id}/participants", response_model=List[Dict])
async def get_chat_participants(chat_id: str):
    participants = await supabase_service.get_chat_participants(chat_id)
    return participants

@app.get("/api/user-chats", response_model=List[Dict])
async def get_user_chat_list(user_id: str, limit: int = 20):
    chat_list = await supabase_service.get_user_chat_list(user_id, limit)
    return chat_list

# --- 新增: 排行榜 & 发现 API ---

@app.get("/api/rankings", response_model=List[Dict])
async def get_rankings(type: str = Query(..., pattern="^(story|agent)$"), period: str = Query("daily")):
    try:
        if type == "story":
            rankings = await supabase_service.get_story_rankings(period)
        else:
            rankings = await supabase_service.get_agent_rankings(period)
        return rankings
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch rankings: {e}")

@app.get("/api/agents/public-with-creator", response_model=List[Dict])
async def get_public_agents_with_creator(limit: int = Query(10, ge=1, le=50)):
    try:
        agents = await supabase_service.get_public_agents_with_creator(limit)
        return agents
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agents: {e}")

@app.get("/api/agents/{agent_id}", response_model=Dict)
async def get_agent_detail(agent_id: str):
    try:
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        try:
            from .pydantic_models import AgentDetailResponse
            validated_agent = AgentDetailResponse.model_validate(agent)
            return validated_agent.model_dump()
        except Exception as validation_error:
            print(f"WARN: Agent详情数据验证失败，返回原始数据: {validation_error}")
            return agent
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agent detail: {e}")

# --- 角色卡导入 API ---

@app.post("/api/agents/import", response_model=ImportedAgentResponse)
async def import_character_card(file: UploadFile = File(...), user_id: str = Form(...)):
    try:
        if file.content_type == 'image/png':
            file_content = await file.read()
            image = Image.open(io.BytesIO(file_content))
            chara_data_b64 = image.info.get('chara')
            if not chara_data_b64:
                raise HTTPException(status_code=400, detail="PNG文件中未找到角色卡数据")
            try:
                json_data = json.loads(base64.b64decode(chara_data_b64))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"角色卡数据解析失败: {str(e)}")
        elif file.content_type == 'application/json':
            file_content = await file.read()
            try:
                json_data = json.loads(file_content.decode('utf-8'))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"JSON文件解析失败: {str(e)}")
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请上传PNG或JSON文件")

        if 'spec' in json_data and 'data' in json_data:
            spec = json_data.get('spec', 'chara_card_v2')
            spec_version = json_data.get('spec_version', '2.0')
            character_data = json_data['data']
        else:
            spec = 'chara_card_v1'
            spec_version = '1.0'
            character_data = json_data

        if not character_data.get('name'):
            raise HTTPException(status_code=400, detail="角色卡缺少必要的name字段")

        new_agent = await supabase_service.create_agent_from_character_card(
            user_id=user_id,
            character_data=character_data,
            spec=spec,
            spec_version=spec_version
        )

        if not new_agent:
            raise HTTPException(status_code=500, detail="角色卡导入失败")

        return ImportedAgentResponse(
            id=str(new_agent['id']),
            name=new_agent['name'],
            message=f"角色 '{new_agent['name']}' 导入成功！"
        )
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"导入角色卡时发生错误: {str(e)}")

@app.get("/api/stories", response_model=List[Dict])
async def get_stories(is_public: bool = Query(True), limit: int = Query(10, ge=1, le=50), user_id: Optional[str] = None):
    try:
        stories = await supabase_service.get_stories(user_id=user_id, is_public=is_public, limit=limit)
        return stories
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch stories: {e}")

@app.get("/api/stories/{story_id}", response_model=Dict)
async def get_story_detail(story_id: str):
    try:
        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")

        chapters = await supabase_service.get_story_chapters(story_id)

        try:
            agents_response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("story_agents")
                .select("agents(*)")
                .eq("story_id", story_id)
                .execute()
            )
            agents = [item['agents'] for item in agents_response.data] if agents_response.data else []
            for agent in agents:
                if 'first_mes' in agent and 'opening_line' not in agent:
                    agent['opening_line'] = agent['first_mes']
            print(f"DEBUG: Found {len(agents)} agents for story {story_id}")
        except Exception as agents_e:
            print(f"WARN: Failed to fetch story agents, falling back to public agents: {agents_e}")
            agents = await supabase_service.get_agents(is_public=True, limit=20)

        return {
            **story,
            "chapters": chapters,
            "agents": agents,
        }
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch story detail: {e}")

# --- 公开配置 API ---

class PublicConfig(BaseModel):
    supabase_url: str
    supabase_anon_key: str

@app.get("/api/config/public", response_model=PublicConfig)
async def get_public_config():
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")

    if not supabase_url or not supabase_anon_key:
        raise HTTPException(status_code=500, detail="Server configuration is incomplete.")

    return {
        "supabase_url": supabase_url,
        "supabase_anon_key": supabase_anon_key,
    }

# --- 独立评分API (PRD核心要求) ---

class EvaluateRequest(BaseModel):
    user_message: str

@app.post("/api/chats/{chat_id}/evaluate", response_model=Dict[str, Any])
async def evaluate_user_message_for_story(chat_id: str, request: EvaluateRequest):
    try:
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat or not chat.get('story_id'):
            raise HTTPException(status_code=404, detail="Story chat session not found.")

        story_id = chat['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters.")
        
        task_progress = chat.get('task_progress', {})

        current_chapter_id = task_progress.get('current_chapter_id')
        if not current_chapter_id and chapters:
            current_chapter_id = chapters[0]['id']

        current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

        if current_progress >= 100:
            return {
                "progress_increment": 0,
                "reasoning": "本章任务已完成。",
                "current_progress": 100,
                "chapter_complete": True
            }

        current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), chapters[0] if chapters else None)
        if not current_chapter:
            raise HTTPException(status_code=404, detail="Current chapter not found.")

        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=10)

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=request.user_message
        )

        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {
            'progress': new_progress,
            'status': new_status
        }

        if new_progress >= 100:
            current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
            if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                next_chapter = chapters[current_chapter_index + 1]
                if next_chapter['id'] not in updated_task_progress['chapters']:
                    updated_task_progress['chapters'][next_chapter['id']] = {
                        'progress': 0,
                        'status': 'unlocked'
                    }

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        return {
            "progress_increment": score_response.progress_increment,
            "current_progress": new_progress,
            "chapter_complete": new_progress >= 100
        }

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to evaluate message: {e}")

# --- 章节推进 API ---

@app.post("/api/chats/{chat_id}/next-chapter", response_model=Dict[str, str])
async def advance_to_next_chapter(chat_id: str, user_id: str = Query(...)):
    try:
        response = await asyncio.to_thread(
            lambda: supabase_service.supabase.rpc('advance_to_next_chapter', {
                'p_current_chat_id': chat_id,
                'p_user_id': user_id
            }).execute()
        )

        if response.error:
            raise Exception(response.error.message)

        new_chat_id = response.data

        if not new_chat_id:
            raise HTTPException(status_code=404, detail="已经是最后一章或找不到下一章。")

        return {"new_chat_id": new_chat_id}

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"进入下一章失败: {str(e)}")

# --- 羁绊系统 API ---

@app.get("/api/bonds/{agent_id}", response_model=Dict)
async def get_bond_details(agent_id: str, user_id: str = Query(...)):
    """获取用户与指定角色的羁绊详情"""
    try:
        bond_details = await supabase_service.get_user_agent_bond(user_id, agent_id)
        if not bond_details:
            # 如果还没有记录，返回一个初始状态
            return {"bond_value": 0, "bond_level": 1}
        return bond_details
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get bond details: {e}")

@app.get("/api/user/{user_id}/bonds", response_model=List[Dict])
async def get_user_all_bonds(user_id: str):
    """获取用户的所有羁绊关系"""
    try:
        bonds = await supabase_service.get_user_all_bonds(user_id)
        return bonds
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get user bonds: {e}")

# --- 摘要与记忆管理 API ---

@app.post("/api/chats/{chat_id}/summary/update")
async def update_chat_summary(chat_id: str):
    try:
        success = await summarization_service.update_chat_summary(chat_id)
        if success:
            return {"status": "success", "message": "摘要已更新"}
        else:
            raise HTTPException(status_code=500, detail="摘要更新失败")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update summary: {e}")

@app.get("/api/chats/{chat_id}/summary")
async def get_chat_summary(chat_id: str):
    try:
        summary = await summarization_service.get_chat_summary(chat_id)
        return {
            "chat_id": chat_id,
            "summary": summary,
            "has_summary": summary is not None
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {e}")

@app.get("/api/chats/{chat_id}/memory-status")
async def get_memory_status(chat_id: str):
    try:
        total_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .execute()
        )
        vectorized_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .not_.is_("embedding", "null")
            .execute()
        )
        summary = await summarization_service.get_chat_summary(chat_id)

        return {
            "chat_id": chat_id,
            "total_messages": total_messages.count or 0,
            "vectorized_messages": vectorized_messages.count or 0,
            "has_summary": summary is not None,
            "memory_coverage": (vectorized_messages.count or 0) / max(total_messages.count or 1, 1) * 100
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get memory status: {e}")

# ===================================================================
# WebSocket (V5 重构)
# ===================================================================

# ConnectionManager 和 manager 实例已移动到 chat_service.py

# ===================================================================
# 织梦者引擎 - 选择效果处理
# ===================================================================

async def process_choice_effect(chat_id: str, choice_effect: dict):
    try:
        print(f"INFO: 处理选择效果 for chat {chat_id}: {choice_effect}")

        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat:
            print(f"ERROR: Chat {chat_id} not found")
            return

        current_game_state = chat.get('game_state', {})
        
        if current_game_state is None:
            current_game_state = {}

        updated_game_state = dict(current_game_state)
        state_changes = []

        for variable_name, effect_value in choice_effect.items():
            if isinstance(effect_value, str):
                delta = int(effect_value)
            else:
                delta = int(effect_value)

            current_value = updated_game_state.get(variable_name, 50)
            new_value = max(0, min(100, current_value + delta))
            updated_game_state[variable_name] = new_value

            state_changes.append({
                "variable": variable_name,
                "old_value": current_value,
                "new_value": new_value,
                "delta": delta
            })
            print(f"  - {variable_name}: {current_value} -> {new_value} ({delta:+d})")

        await supabase_service.update_chat_game_state(chat_id, updated_game_state)

        change_descriptions = []
        for change in state_changes:
            variable_parts = change["variable"].split(".")
            if len(variable_parts) >= 2:
                character_name = variable_parts[0]
                variable_type = variable_parts[1]
                if change["delta"] > 0:
                    change_descriptions.append(f"{character_name}的{variable_type}提升了！")
                elif change["delta"] < 0:
                    change_descriptions.append(f"{character_name}的{variable_type}下降了...")

        if change_descriptions:
            await chat_service.manager.broadcast(chat_id, json.dumps({
                "type": "game_state_update",
                "changes": state_changes,
                "descriptions": change_descriptions,
                "new_game_state": updated_game_state
            }))
            print(f"SUCCESS: 游戏状态更新完成 for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: 处理选择效果失败 for chat {chat_id}: {e}")
        traceback.print_exc()

@app.websocket("/ws/chat/{chat_id}")
async def websocket_chat_endpoint(websocket: WebSocket, chat_id: str, user_id: str = Query(...)):
    # chat_service 现在拥有 manager，通过它来连接
    await chat_service.manager.connect(websocket, chat_id)
    try:
        # 1. 发送初始状态包 (此逻辑可封装进 chat_service)
        await chat_service.send_initial_state(websocket, chat_id)

        # 2. 循环接收消息
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # 3. 将消息处理完全委托给 chat_service
            await chat_service.handle_websocket_message(chat_id, user_id, message_data)

    except WebSocketDisconnect:
        chat_service.manager.disconnect(websocket, chat_id)
    except Exception as e:
        print(f"ERROR: WebSocket for chat {chat_id} error: {e}")
        traceback.print_exc()
        chat_service.manager.disconnect(websocket, chat_id)


# process_ai_turn 函数已迁移到 chat_service.py

# process_regular_chat_turn 和 process_rag_enhancement_async 函数已迁移到 chat_service.py

# generate_and_broadcast_choices 函数已迁移到 chat_service.py

# stream_and_save_response 函数已迁移到 chat_service.py

# generate_and_save_embedding 和 check_and_update_summary 函数已迁移到 chat_service.py

async def process_choice_scoring_only(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None):
    try:
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session or not chat_session.get('story_id'):
            print(f"ERROR: Chat {chat_id} is not a story chat")
            return

        story_id = chat_session['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            print(f"ERROR: No chapters found for story {story_id}")
            return

        current_chapter = chapters[0]
        task_progress = chat_session.get('task_progress', {})
        current_progress = task_progress.get('current_progress', 0)

        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=20)

        await asyncio.sleep(1.0)
        print(f"INFO: Starting choice scoring for chat {chat_id}")

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        if score_response:
            increment = score_response.progress_increment
            new_total = min(current_progress + increment, 100)
            is_chapter_complete = new_total >= 100

            updated_task_progress = {**task_progress, 'current_progress': new_total}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            score_message = {
                "type": "score_update",
                "progress_increment": increment,
                "current_progress": new_total,
                "chapter_complete": is_chapter_complete
            }
            await chat_service.manager.broadcast(chat_id, json.dumps(score_message))
            print(f"INFO: Choice scoring completed for chat {chat_id}: +{increment} -> {new_total}")

    except Exception as e:
        print(f"ERROR: Choice scoring failed for chat {chat_id}: {e}")
        traceback.print_exc()

async def process_story_turn_with_scoring(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None, target_agent_index: Optional[int] = None):
    lock = chat_service.get_lock(chat_id)
    async with lock:
        try:
            chat_task = supabase_service.get_chat_by_id(chat_id)
            history_task = supabase_service.get_messages_by_chat_id(chat_id, limit=15)
            participants_task = supabase_service.get_chat_participants(chat_id)
            chat, history, participants = await asyncio.gather(chat_task, history_task, participants_task)

            if not chat or not chat.get('story_id'):
                raise ValueError("Not a valid story session.")

            # 选择回复角色的逻辑保持不变
            if target_agent_index is not None and target_agent_index < len(participants):
                replying_agent = participants[target_agent_index]
            elif target_agent_id:
                replying_agent = next((p for p in participants if p['id'] == target_agent_id), participants[0])
            else:
                replying_agent = participants[0] if participants else None

            if not replying_agent:
                raise ValueError("No agent available for reply")

            # --- ▼▼▼ 核心修改区域开始 ▼▼▼ ---
            # 1. 不再手动构建简单的prompt
            #    移除所有 STREAMING_STORY_CHAT_PROMPT(...) 的相关代码

            # 2. 直接调用 prompt_assembler 来构建完整的、带有故事上下文的 prompt
            print(f"INFO: [Story Mode] Building advanced prompt for agent {replying_agent.get('name')}...")
            streaming_prompt = await prompt_assembler.build_prompt(
                chat_id=chat_id,
                user_message=user_message,
                agent_id=replying_agent['id'],
                mode='story' # <--- 关键：指明这是故事模式
            )

            # 3. 将 AI 回复和评分任务并行执行
            ai_reply_task = asyncio.create_task(stream_and_save_response(chat_id, replying_agent, streaming_prompt, is_story_mode=True))

            # (评分任务的逻辑保持不变，但需要确保它能正确获取章节信息)
            story_id = chat['story_id']
            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                raise ValueError("Story has no chapters.")

            task_progress = chat.get('task_progress', {})
            # 修正：从 task_progress 中获取当前章节和进度
            current_chapter_id = task_progress.get('current_chapter_id')
            current_chapter = None
            if current_chapter_id:
                current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), None)

            # 如果没有当前章节ID或找不到，则默认为第一个
            if current_chapter is None:
                current_chapter = chapters[0]
                current_chapter_id = current_chapter['id']

            current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

            scoring_task = asyncio.create_task(_delayed_scoring_task(
                chat_id=chat_id, user_message=user_message, current_chapter=current_chapter,
                current_progress=current_progress, task_progress=task_progress, history=history
            ))

            await ai_reply_task
            print(f"INFO: AI reply completed for chat {chat_id}, scoring task running in background")
            # --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---

        except Exception as e:
            print(f"ERROR: process_story_turn_with_scoring failed for chat {chat_id}: {e}")
            traceback.print_exc()

async def _delayed_scoring_task(chat_id: str, user_message: str, current_chapter: dict, current_progress: int, task_progress: dict, history: list):
    try:
        await asyncio.sleep(1.0)
        print(f"INFO: Starting delayed scoring task for chat {chat_id}")

        if current_progress >= 100:
            print(f"INFO: Chapter already completed for chat {chat_id}, skipping scoring")
            return

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {'progress': new_progress, 'status': new_status}

        if new_progress >= 100:
            chapters = await supabase_service.get_story_chapters(current_chapter.get('story_id'))
            if chapters:
                current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
                if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                    next_chapter = chapters[current_chapter_index + 1]
                    if next_chapter['id'] not in updated_task_progress['chapters']:
                        updated_task_progress['chapters'][next_chapter['id']] = {'progress': 0, 'status': 'unlocked'}

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        await chat_service.manager.broadcast(chat_id, json.dumps({
            "type": "score_update", "progress_increment": score_response.progress_increment,
            "current_progress": new_progress, "chapter_complete": new_progress >= 100
        }))
        print(f"INFO: Scoring completed for chat {chat_id}: +{score_response.progress_increment} -> {new_progress}")

    except Exception as e:
        print(f"ERROR: Delayed scoring task failed for chat {chat_id}: {e}")
        traceback.print_exc()

async def process_story_turn(chat: dict, history: list, user_message: str, agent: dict, participants: list):
    chat_id = chat['id']

    # 使用 prompt_assembler 来构建完整的、带有故事上下文的 prompt
    print(f"INFO: [Story Mode] Building advanced prompt for agent {agent.get('name')}...")
    streaming_prompt = await prompt_assembler.build_prompt(
        chat_id=chat_id,
        user_message=user_message,
        agent_id=agent['id'],
        mode='story' # 指明这是故事模式
    )

    await stream_and_save_response(chat_id, agent, streaming_prompt, is_story_mode=True)

async def process_opening_sequence(chat_id: str):
    lock = chat_service.get_lock(chat_id)
    async with lock:
        try:
            chat = await supabase_service.get_chat_by_id(chat_id)
            if not chat or not chat.get('story_id'):
                print(f"ERROR: Chat {chat_id} is not a story chat or not found")
                return

            story_id = chat['story_id']
            story_detail = await supabase_service.get_story_by_id(story_id)
            protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                print(f"ERROR: No chapters found for story {story_id}")
                return

            first_chapter = chapters[0]
            opening_sequence = first_chapter.get("opening_sequence") or []

            if not opening_sequence:
                print(f"INFO: No opening sequence found for story {story_id}")
                return

            task_progress = chat.get('task_progress', {})
            opening_progress = task_progress.get('opening_sequence_index', 0)

            if opening_progress >= len(opening_sequence):
                print(f"INFO: Opening sequence already completed for chat {chat_id}")
                return

            current_element = opening_sequence[opening_progress]
            element_type = current_element.get("element_type", "text")
            content = current_element.get("content_or_prompt", "")
            character_id = current_element.get("agent_id") or current_element.get("character_id")

            if content:
                role = 'narration'
                if character_id:
                    if character_id == protagonist_agent_id:
                        role = 'user'
                    else:
                        role = 'assistant'
                await supabase_service.add_message_to_chat(
                    chat_id=chat_id, role=role, content=content, agent_id=character_id,
                    metadata={"is_opening_sequence": True}
                )
                print(f"INFO: Saved opening sequence element to chat {chat_id} as '{role}' message (character_id: {character_id}, protagonist: {protagonist_agent_id}).")

            message_data = {
                "type": "opening_sequence_element",
                "data": {
                    "element_type": element_type, "content": content, "character_id": character_id,
                    "sequence_index": opening_progress, "total_elements": len(opening_sequence),
                    "is_last": opening_progress == len(opening_sequence) - 1
                }
            }
            if element_type == "choice" and "choices" in current_element:
                message_data["data"]["choices"] = current_element["choices"]

            await chat_service.manager.broadcast(chat_id, json.dumps(message_data))

            new_progress = opening_progress + 1
            updated_task_progress = {**task_progress, 'opening_sequence_index': new_progress}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            print(f"INFO: Sent opening sequence element {opening_progress + 1}/{len(opening_sequence)} for chat {chat_id}")

            if new_progress >= len(opening_sequence):
                completion_message = {"type": "opening_sequence_complete", "data": {"chat_id": chat_id}}
                await chat_service.manager.broadcast(chat_id, json.dumps(completion_message))
                print(f"INFO: Opening sequence completed for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: Failed to process opening sequence for chat {chat_id}: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    import uvicorn
    print("INFO: 启动星恋 AI 后端服务 - V5.0 统一消息流")
    print("INFO: 访问 http://127.0.0.1:8000/docs 查看API文档")
    uvicorn.run("supabase_main:app", host="0.0.0.0", port=8000, reload=True)