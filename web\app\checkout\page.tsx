"use client"

import Link from "next/link"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"

export default function CheckoutPage() {
  const [paymentMethod, setPaymentMethod] = useState("credit-card")
  const [agreedToTerms, setAgreedToTerms] = useState(false)

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="max-w-6xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Billing Info */}
          <div className="lg:col-span-2 space-y-6">
            <h1 className="text-2xl font-bold">Checkout</h1>

            {/* Personal Information */}
            <Card className="bg-gray-900 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-white">First Name</label>
                    <Input placeholder="Enter your first name" className="bg-gray-800 border-gray-600 text-white" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-white">Last Name</label>
                    <Input placeholder="Enter your last name" className="bg-gray-800 border-gray-600 text-white" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-white">Country</label>
                    <Select defaultValue="us">
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600">
                        <SelectItem value="us">United States</SelectItem>
                        <SelectItem value="ca">Canada</SelectItem>
                        <SelectItem value="uk">United Kingdom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-white">State</label>
                    <Select>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600">
                        <SelectItem value="ca">California</SelectItem>
                        <SelectItem value="ny">New York</SelectItem>
                        <SelectItem value="tx">Texas</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-white">Email</label>
                  <Input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="bg-gray-800 border-gray-600 text-white"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card className="bg-gray-900 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Payment Method</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div
                    className={`p-4 border rounded-lg cursor-pointer ${
                      paymentMethod === "credit-card" ? "border-blue-500 bg-blue-500/10" : "border-gray-600"
                    }`}
                    onClick={() => setPaymentMethod("credit-card")}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full border-2 border-blue-500 flex items-center justify-center">
                        {paymentMethod === "credit-card" && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                      </div>
                      <span className="text-white">Credit Card</span>
                    </div>
                  </div>
                  <div
                    className={`p-4 border rounded-lg cursor-pointer ${
                      paymentMethod === "venmo" ? "border-blue-500 bg-blue-500/10" : "border-gray-600"
                    }`}
                    onClick={() => setPaymentMethod("venmo")}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full border-2 border-gray-400">
                        {paymentMethod === "venmo" && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                      </div>
                      <span className="text-white">VENMO (paused)</span>
                      <span className="text-xs text-gray-400">One time payment</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div
                    className={`p-4 border rounded-lg cursor-pointer ${
                      paymentMethod === "crypto" ? "border-blue-500 bg-blue-500/10" : "border-gray-600"
                    }`}
                    onClick={() => setPaymentMethod("crypto")}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full border-2 border-gray-400">
                        {paymentMethod === "crypto" && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                      </div>
                      <span className="text-white">Cryptocurrencies</span>
                      <span className="text-xs text-gray-400">One time payment</span>
                    </div>
                  </div>
                  <div
                    className={`p-4 border rounded-lg cursor-pointer ${
                      paymentMethod === "alipay" ? "border-blue-500 bg-blue-500/10" : "border-gray-600"
                    }`}
                    onClick={() => setPaymentMethod("alipay")}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full border-2 border-gray-400">
                        {paymentMethod === "alipay" && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                      </div>
                      <span className="text-white">Alipay</span>
                      <span className="text-xs text-gray-400">One time payment (¥)</span>
                    </div>
                  </div>
                </div>

                {paymentMethod === "credit-card" && (
                  <div className="space-y-4 mt-6">
                    <div className="flex items-center gap-2 mb-4">
                      <span className="text-white font-medium">Card Info</span>
                      <div className="flex gap-1">
                        <img src="/placeholder.svg?height=20&width=30" alt="Visa" className="h-5" />
                        <img src="/placeholder.svg?height=20&width=30" alt="Mastercard" className="h-5" />
                        <img src="/placeholder.svg?height=20&width=30" alt="Amex" className="h-5" />
                      </div>
                    </div>
                    <Input placeholder="Card number" className="bg-gray-800 border-gray-600 text-white" />
                    <div className="grid grid-cols-2 gap-4">
                      <Input placeholder="MM/YY" className="bg-gray-800 border-gray-600 text-white" />
                      <Input placeholder="CVV" className="bg-gray-800 border-gray-600 text-white" />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Order Summary */}
          <div className="space-y-6">
            <Card className="bg-gray-900 border-gray-700">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">I'm All In</CardTitle>
                  <Select defaultValue="monthly">
                    <SelectTrigger className="w-32 bg-gray-800 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  variant="outline"
                  className="w-full border-gray-600 text-white hover:bg-gray-800 bg-transparent"
                >
                  Apply Coupon
                </Button>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Monthly Price</span>
                    <span className="text-white">$24.95</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">You Pay</span>
                    <span className="text-white font-bold">$24.95</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox id="terms" checked={agreedToTerms} onCheckedChange={setAgreedToTerms} />
                  <label htmlFor="terms" className="text-sm text-white">
                    I agree to the{" "}
                    <Link href="/terms" className="text-blue-400 hover:underline">
                      Terms & Conditions
                    </Link>
                  </label>
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700" disabled={!agreedToTerms}>
                  Subscribe
                </Button>

                <div className="text-xs text-gray-400 text-center">
                  This subscription automatically renews monthly, and you'll be notified in advance if the monthly
                  amount increases. Your next bill will appear as NEATCOM on your credit card statement or bank account.
                </div>

                <div className="flex items-center justify-center gap-2 text-green-400 text-sm">
                  <span>🔒</span>
                  <span>Payments are secure and encrypted</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
