"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { apiClient, type Message } from '@/lib/api'

export interface WebSocketMessage {
  type: string
  [key: string]: any
}

export interface ChatChoice {
  text: string
  target_agent_id?: string
}

export function useWebSocketChat(chatId: string, userId = 'guest', token?: string) {
  const [messages, setMessages] = useState<Message[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [choices, setChoices] = useState<ChatChoice[]>([])
  const [gameState, setGameState] = useState<any>({})
  const [participants, setParticipants] = useState<any[]>([])
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      console.log(`Connecting to WebSocket for chat ${chatId}...`)
      const ws = apiClient.createWebSocket(chatId, userId, token)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('WebSocket connected')
        setIsConnected(true)
        reconnectAttempts.current = 0
      }

      ws.onmessage = (event) => {
        try {
          const data: WebSocketMessage = JSON.parse(event.data)
          console.log('WebSocket message received:', data)

          switch (data.type) {
            case 'game_state_sync':
              // 初始状态同步
              if (data.data) {
                setMessages(data.data.messages || [])
                setParticipants(data.data.participants || [])
                setGameState(data.data.task_progress || {})
              }
              break

            case 'message_chunk':
              // 流式消息块
              setMessages(prev => {
                const existing = prev.find(m => m.id === data.temp_id)
                if (existing) {
                  return prev.map(m => 
                    m.id === data.temp_id 
                      ? { ...m, content: m.content + data.content_chunk }
                      : m
                  )
                } else {
                  return [...prev, {
                    id: data.temp_id,
                    role: data.role,
                    content: data.content_chunk,
                    agent_id: data.agent_id,
                    created_at: new Date().toISOString()
                  }]
                }
              })
              break

            case 'stream_end':
              // 流式消息结束，更新为最终消息
              if (data.final_message) {
                setMessages(prev => 
                  prev.map(m => 
                    m.id === data.temp_id 
                      ? {
                          id: data.final_message.id,
                          role: data.final_message.role,
                          content: data.final_message.content,
                          agent_id: data.final_message.agent_id,
                          created_at: data.final_message.created_at
                        }
                      : m
                  )
                )
              }
              setIsLoading(false)
              break

            case 'choices_updated':
            case 'choices_regenerated':
              // 更新用户选项
              setChoices(data.choices || [])
              break

            case 'bond_update':
              // 羁绊值更新
              console.log('Bond updated:', data)
              break

            case 'game_state_update':
              // 游戏状态更新
              setGameState(prev => ({ ...prev, ...data.new_game_state }))
              break

            case 'error':
              console.error('WebSocket error:', data.content)
              break

            default:
              console.log('Unknown message type:', data.type)
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setIsConnected(false)
        
        // 自动重连
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000 // 指数退避
          console.log(`Reconnecting in ${delay}ms... (attempt ${reconnectAttempts.current + 1}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++
            connect()
          }, delay)
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
    }
  }, [chatId, userId, token])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
    setIsConnected(false)
  }, [])

  const sendMessage = useCallback((content: string, targetAgentId?: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket is not connected')
      return
    }

    // 添加用户消息到本地状态
    const userMessage: Message = {
      id: `temp_${Date.now()}`,
      role: 'user',
      content,
      created_at: new Date().toISOString()
    }
    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    // 发送到服务器
    const message = {
      action: 'message',
      content,
      target_agent_id: targetAgentId
    }

    wsRef.current.send(JSON.stringify(message))
  }, [])

  const regenerateChoices = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket is not connected')
      return
    }

    wsRef.current.send(JSON.stringify({
      action: 'regenerate_choices'
    }))
  }, [])

  // 连接和清理
  useEffect(() => {
    connect()
    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return {
    messages,
    isConnected,
    isLoading,
    choices,
    gameState,
    participants,
    sendMessage,
    regenerateChoices,
    connect,
    disconnect
  }
}