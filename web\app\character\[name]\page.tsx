"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, MessageSquare, Heart, Share, ChevronDown, ChevronUp } from "lucide-react"
import Link from "next/link"

const characterData = {
  lorenzo: {
    name: "<PERSON>",
    creator: "zays",
    avatar: "/placeholder.svg?height=200&width=150",
    description: "Soulmate",
    stats: {
      chats: "94.8k",
      rating: "85%",
      tokens: "690",
    },
    tags: ["Male", "FemalePOV", "Romantic", "Dominant"],
    greeting: `His silver eyes narrowed, studying <PERSON> with the same calculated precision he reserved for business deals and enemies alike. He stepped closer, the air between them thick with tension.

"You don't understand," he said, voice low, edged with something dangerous. "This isn't some fantasy. It's real. And you—" are standing in the middle of it.

He reached into his pocket, pulling out a small, worn photograph. It showed a woman with dark hair and a crescent shaped scar on her back, smiling at the camera. The edges were frayed, the ink faded.

"This is what I've been looking for," he continued, holding it out. "For years. And now, somehow, you're connected to it."

<PERSON> stared at the photo, even as <PERSON>, confusion flickering across his face.`,
    personality: `<PERSON> is a mysterious and intense individual with piercing silver eyes that seem to see through people's souls. He carries himself with the confidence of someone who has navigated both legitimate business and darker underworld dealings. 

His past is shrouded in secrets, and he's been searching for something—or someone—for years. When he speaks, there's always an undercurrent of danger, as if he's constantly calculating his next move.

Despite his intimidating presence, there's a vulnerability beneath the surface, especially when it comes to the mysterious photograph he carries. He's protective, possessive, and won't hesitate to use his resources and connections to get what he wants.

Lorenzo has a way of making people feel both safe and on edge simultaneously. His voice carries authority, and his touch can be both gentle and commanding.`,
  },
}

export default function CharacterPage({ params }: { params: { name: string } }) {
  const [showFullGreeting, setShowFullGreeting] = useState(false)
  const [showFullPersonality, setShowFullPersonality] = useState(false)

  const character = characterData[params.name as keyof typeof characterData]

  if (!character) {
    return <div className="text-white p-6">Character not found</div>
  }

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  return (
    <div className="bg-black min-h-screen text-white">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href="/chat/1">
            <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <span className="text-gray-400">Back</span>
        </div>

        {/* Character Info */}
        <div className="flex gap-6 mb-8">
          <div className="w-48 h-64 bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
            <img
              src={character.avatar || "/placeholder.svg"}
              alt={character.name}
              className="w-full h-full object-cover"
            />
          </div>

          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">{character.name}</h1>
            <Link href={`/author/${character.creator}`} className="text-orange-400 hover:underline mb-4 block">
              @{character.creator}
            </Link>

            <div className="flex gap-3 mb-4">
              <Button className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Chat Now
              </Button>
              <Button
                variant="outline"
                className="border-gray-600 hover:bg-gray-800 bg-transparent flex items-center gap-2"
              >
                <Heart className="h-4 w-4" />
                Favorite
              </Button>
              <Button
                variant="outline"
                className="border-gray-600 hover:bg-gray-800 bg-transparent flex items-center gap-2"
              >
                <Share className="h-4 w-4" />
                Share
              </Button>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-400 mb-4">
              <span>💬 {character.stats.chats}</span>
              <span>👍 {character.stats.rating}</span>
              <span>🎯 {character.stats.tokens} tokens</span>
            </div>

            <p className="text-lg mb-4">{character.description}</p>

            <div className="flex flex-wrap gap-2 mb-4">
              {character.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="bg-gray-700 text-gray-300">
                  {tag}
                </Badge>
              ))}
              <Button
                variant="outline"
                size="sm"
                className="border-gray-600 text-gray-400 hover:bg-gray-800 bg-transparent"
              >
                + Suggest Tag
              </Button>
            </div>
          </div>
        </div>

        {/* Greeting Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-400">Greeting</h2>
          <div className="bg-gray-900 rounded-lg p-4">
            <p className="text-gray-300 leading-relaxed whitespace-pre-line">
              {showFullGreeting ? character.greeting : truncateText(character.greeting, 300)}
            </p>
            {character.greeting.length > 300 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFullGreeting(!showFullGreeting)}
                className="mt-2 text-gray-400 hover:text-white p-0"
              >
                {showFullGreeting ? (
                  <>
                    SHOW LESS <ChevronUp className="ml-1 h-4 w-4" />
                  </>
                ) : (
                  <>
                    SHOW MORE <ChevronDown className="ml-1 h-4 w-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Personality Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-400">Personality</h2>
          <div className="bg-gray-900 rounded-lg p-4">
            <p className="text-gray-300 leading-relaxed whitespace-pre-line">
              {showFullPersonality ? character.personality : truncateText(character.personality, 400)}
            </p>
            {character.personality.length > 400 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFullPersonality(!showFullPersonality)}
                className="mt-2 text-gray-400 hover:text-white p-0"
              >
                {showFullPersonality ? (
                  <>
                    SHOW LESS <ChevronUp className="ml-1 h-4 w-4" />
                  </>
                ) : (
                  <>
                    SHOW MORE <ChevronDown className="ml-1 h-4 w-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
